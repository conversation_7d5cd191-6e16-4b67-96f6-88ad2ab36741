#!/usr/bin/env python3
"""
AI Strategy Tuner Startup Script

Quick startup script for the AI Strategy Tuner with dependency checking.
"""

import sys
import subprocess
import importlib
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are available."""
    required_modules = [
        'aiohttp',
        'aiohttp_cors',
        'yaml',
        'sqlite3'
    ]
    
    missing = []
    for module in required_modules:
        try:
            importlib.import_module(module)
            print(f"✅ {module}")
        except ImportError:
            missing.append(module)
            print(f"❌ {module} - MISSING")
    
    if missing:
        print(f"\n⚠️  Missing dependencies: {', '.join(missing)}")
        print("Install with: pip install aiohttp aiohttp-cors pyyaml")
        return False
    
    return True

def check_files():
    """Check if required files exist."""
    required_files = [
        'config.yaml',
        'orchestrator.py',
        'data/bus.db'
    ]
    
    missing = []
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            missing.append(file_path)
            print(f"❌ {file_path} - MISSING")
    
    if missing:
        print(f"\n⚠️  Missing files: {', '.join(missing)}")
        if 'data/bus.db' in missing:
            print("💡 Tip: Run the orchestrator first to create the database")
        return False
    
    return True

def main():
    """Main startup function."""
    print("🧠 AI Strategy Tuner - Startup Check")
    print("=" * 50)
    
    print("\n📦 Checking Dependencies...")
    deps_ok = check_dependencies()
    
    print("\n📁 Checking Files...")
    files_ok = check_files()
    
    if not deps_ok or not files_ok:
        print("\n❌ Startup checks failed. Please fix the issues above.")
        return False
    
    print("\n✅ All checks passed!")
    print("\n🚀 Starting AI Strategy Tuner...")
    print("🌐 Access: http://localhost:8084")
    print("🎯 Focus: Smart Model Integrated Strategy")
    print("\n" + "=" * 50)
    
    # Start the AI Strategy Tuner
    try:
        subprocess.run([sys.executable, "ai_strategy_tuner.py"], check=True)
    except KeyboardInterrupt:
        print("\n🛑 AI Strategy Tuner stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting AI Strategy Tuner: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
