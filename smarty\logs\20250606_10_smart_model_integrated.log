2025-06-06 10:37:40,465 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-06-06 10:37:40,465 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250606_10_smart_model_integrated.log
2025-06-06 10:37:40,466 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250606_10_smart_model_integrated_events.json
2025-06-06 10:37:40,466 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-06-06 10:37:40,466 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-06-06 10:37:40
2025-06-06 10:37:40,467 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-06-06 10:37:40,467 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]
2025-06-06 10:37:40,467 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-06-06 10:37:40,469 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-06-06 10:37:40,469 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-06-06 10:37:40,469 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-06-06 10:37:40,469 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: False
2025-06-06 10:37:40,469 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-06-06 10:37:40,472 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-06-06 10:37:40,472 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-06-06 10:37:40,472 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-06-06 10:37:40,473 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-06-06 10:37:40,473 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-06-06 10:37:44,536 - strategy.smart_model_integrated - WARNING - [warning:93] - SignalStar client not initialized, social sentiment model disabled
2025-06-06 10:37:44,542 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-06-06 10:37:44,563 - strategy.smart_model_integrated - INFO - [info:89] - Starting orchestrator...
2025-06-06 10:37:44,563 - strategy.smart_model_integrated - INFO - [info:89] - 🔄 Attempting Multi-Exchange connection...
2025-06-06 10:37:46,242 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Connected to Multi-Exchange client
2025-06-06 10:43:14,571 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-06-06 10:43:14,572 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250606_10_smart_model_integrated.log
2025-06-06 10:43:14,572 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250606_10_smart_model_integrated_events.json
2025-06-06 10:43:14,572 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-06-06 10:43:14,573 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-06-06 10:43:14
2025-06-06 10:43:14,573 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-06-06 10:43:14,573 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]
2025-06-06 10:43:14,573 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-06-06 10:43:14,575 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-06-06 10:43:14,575 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-06-06 10:43:14,575 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-06-06 10:43:14,576 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: False
2025-06-06 10:43:14,576 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-06-06 10:43:14,579 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-06-06 10:43:14,580 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-06-06 10:43:14,581 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-06-06 10:43:14,581 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-06-06 10:43:14,581 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-06-06 10:43:15,637 - strategy.smart_model_integrated - WARNING - [warning:93] - SignalStar client not initialized, social sentiment model disabled
2025-06-06 10:43:15,639 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-06-06 10:43:15,644 - strategy.smart_model_integrated - INFO - [info:89] - Starting orchestrator...
2025-06-06 10:43:15,644 - strategy.smart_model_integrated - INFO - [info:89] - 🔄 Attempting Multi-Exchange connection...
2025-06-06 10:43:17,335 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Connected to Multi-Exchange client
2025-06-06 10:47:09,248 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-06-06 10:47:09,248 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250606_10_smart_model_integrated.log
2025-06-06 10:47:09,248 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250606_10_smart_model_integrated_events.json
2025-06-06 10:47:09,248 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-06-06 10:47:09,248 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-06-06 10:47:09
2025-06-06 10:47:09,248 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-06-06 10:47:09,248 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]
2025-06-06 10:47:09,248 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-06-06 10:47:09,248 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-06-06 10:47:09,248 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-06-06 10:47:09,248 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-06-06 10:47:09,248 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: False
2025-06-06 10:47:09,248 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-06-06 10:47:09,259 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-06-06 10:47:09,259 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-06-06 10:47:09,259 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-06-06 10:47:09,259 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-06-06 10:47:09,259 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-06-06 10:47:10,299 - strategy.smart_model_integrated - WARNING - [warning:93] - SignalStar client not initialized, social sentiment model disabled
2025-06-06 10:47:10,299 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-06-06 10:47:10,305 - strategy.smart_model_integrated - INFO - [info:89] - Starting orchestrator...
2025-06-06 10:47:10,306 - strategy.smart_model_integrated - INFO - [info:89] - 🔄 Attempting Multi-Exchange connection...
2025-06-06 10:47:11,985 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Connected to Multi-Exchange client
2025-06-06 10:49:30,268 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-06-06 10:49:30,268 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250606_10_smart_model_integrated.log
2025-06-06 10:49:30,268 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250606_10_smart_model_integrated_events.json
2025-06-06 10:49:30,268 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-06-06 10:49:30,279 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-06-06 10:49:30
2025-06-06 10:49:30,279 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-06-06 10:49:30,279 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]
2025-06-06 10:49:30,279 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-06-06 10:49:30,279 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-06-06 10:49:30,279 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-06-06 10:49:30,279 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-06-06 10:49:30,281 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: False
2025-06-06 10:49:30,281 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-06-06 10:49:30,281 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-06-06 10:49:30,281 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-06-06 10:49:30,281 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-06-06 10:49:30,281 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-06-06 10:49:30,281 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-06-06 10:49:31,137 - strategy.smart_model_integrated - WARNING - [warning:93] - SignalStar client not initialized, social sentiment model disabled
2025-06-06 10:49:31,139 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-06-06 10:49:31,142 - strategy.smart_model_integrated - INFO - [info:89] - Starting orchestrator...
2025-06-06 10:49:31,142 - strategy.smart_model_integrated - INFO - [info:89] - 🔄 Attempting Multi-Exchange connection...
2025-06-06 10:49:32,806 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Connected to Multi-Exchange client
