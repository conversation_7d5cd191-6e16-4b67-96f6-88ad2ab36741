#!/usr/bin/env python3
"""
Test script for Phase 8 Account-Aware Live Trade System
Tests live account monitoring, safety checks, and enhanced LLM integration
"""

import asyncio
import logging
import yaml
import os
import time
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

from monitoring.account_tracker import LiveAccountTracker, AccountSnapshot
from execution.execution_controller import ExecutionController
from models.llm_integration import LLMIntegration
from exchange.htx_client import HTXClient

async def test_account_tracker():
    """Test live account tracking functionality."""
    try:
        logger.info("🏦 Testing Live Account Tracker")

        # Load configuration
        config_path = Path("config/strategy.yaml")
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)

        # Initialize HTX client
        htx_client = HTXClient(config)

        # Initialize account tracker
        account_tracker = LiveAccountTracker(config, htx_client)

        # Start monitoring
        await account_tracker.start_monitoring()
        logger.info("✅ Account monitoring started")

        # Wait for first snapshot
        await asyncio.sleep(3)

        # Get current snapshot
        snapshot = account_tracker.get_current_snapshot()
        if snapshot:
            logger.info(f"📊 Account Snapshot:")
            logger.info(f"   Balance: ${snapshot.total_balance:.2f}")
            logger.info(f"   Margin Used: {snapshot.margin_used_pct:.1f}%")
            logger.info(f"   Leverage: {snapshot.leverage:.0f}x")
            logger.info(f"   Open Positions: {snapshot.open_positions}")
            logger.info(f"   Risk Level: {snapshot.risk_level}")
            logger.info(f"   Can Trade: {snapshot.can_trade}")
        else:
            logger.warning("No account snapshot available")

        # Test account summary for LLM
        account_summary = account_tracker.get_account_summary()
        logger.info(f"📋 Account Summary for LLM:")
        for key, value in account_summary.items():
            logger.info(f"   {key}: {value}")

        # Test trade capability check
        can_trade, warnings = account_tracker.can_place_trade(2.0, 'LONG')
        logger.info(f"🔍 Trade Check (LONG $2.00): {'✅ ALLOWED' if can_trade else '❌ BLOCKED'}")
        if warnings:
            for warning in warnings:
                logger.warning(f"   ⚠️ {warning}")

        # Stop monitoring
        await account_tracker.stop_monitoring()
        logger.info("✅ Account monitoring stopped")

        return True

    except Exception as e:
        logger.error(f"❌ Account tracker test failed: {e}")
        return False

async def test_execution_safeguards():
    """Test Phase 8 execution safeguards."""
    try:
        logger.info("\n🛡️ Testing Execution Safeguards")

        # Load configuration
        config_path = Path("config/strategy.yaml")
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)

        # Initialize execution controller
        execution_controller = ExecutionController(config)

        # Test decision processing with account awareness
        test_decision = {
            'symbol': 'DOGE-USDT',
            'action': 'LONG',
            'confidence': 0.85,
            'conviction_score': 4,
            'reasoning': 'Strong bullish signals with account-aware risk management',
            'risk_assessment': 'MEDIUM',
            'market_regime': 'trending_up'
        }

        test_market_data = {
            'symbol': 'DOGE-USDT',
            'last_price': 0.08,
            'volume_24h': 1000000,
            'volatility': 0.015,
            'spread': 0.0001
        }

        logger.info(f"🧪 Processing test decision: {test_decision['action']} {test_decision['symbol']}")

        # Process decision with Phase 8 safeguards
        result = await execution_controller.process_trading_decision(test_decision, test_market_data)

        logger.info(f"✅ Decision processed with safeguards:")
        logger.info(f"   Decision ID: {result.decision_id}")
        logger.info(f"   Executed: {'YES' if result.execution else 'NO'}")
        logger.info(f"   Execution Quality: {result.execution_quality:.2f}")
        logger.info(f"   Order Strategy: {result.order_recommendation.strategy.value}")

        if result.execution:
            execution = result.execution
            logger.info(f"🚀 Execution Details:")
            logger.info(f"   Symbol: {execution.symbol}")
            logger.info(f"   Action: {execution.action}")
            logger.info(f"   Size: {execution.size:.2f}")
            logger.info(f"   Price: ${execution.execution_price:.4f}")

        # Test account status
        account_tracker = execution_controller.account_tracker
        if account_tracker:
            risk_metrics = account_tracker.get_risk_metrics()
            logger.info(f"📊 Risk Metrics After Decision:")
            logger.info(f"   Risk Level: {risk_metrics['risk_level']}")
            logger.info(f"   Can Trade: {risk_metrics['can_trade']}")
            if risk_metrics.get('warnings'):
                for warning in risk_metrics['warnings']:
                    logger.warning(f"   ⚠️ {warning}")

        return True

    except Exception as e:
        logger.error(f"❌ Execution safeguards test failed: {e}")
        return False

async def test_llm_account_integration():
    """Test LLM integration with account context."""
    try:
        logger.info("\n🧠 Testing LLM Account Integration")

        # Load configuration
        config_path = Path("config/strategy.yaml")
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)

        # Initialize components
        htx_client = HTXClient(config)
        account_tracker = LiveAccountTracker(config, htx_client)

        # Start account monitoring
        await account_tracker.start_monitoring()
        await asyncio.sleep(2)  # Wait for first snapshot

        # Initialize LLM integration
        from data.data_store import LiveDataStore
        data_store = LiveDataStore()
        llm_integration = LLMIntegration(config, data_store)

        # Connect account tracker to LLM
        llm_integration.account_tracker = account_tracker

        # Create test signal and features
        from feeds.trade_parser import TradingSignal, MarketFeatures

        test_signal = TradingSignal(
            symbol='DOGE-USDT',
            action='LONG',
            confidence=0.85,
            score=0.78,
            reasoning='Strong technical signals',
            timestamp=time.time(),
            signal_strength=0.82,
            model_contributions={'RSI': 0.8, 'VWAP': 0.9}
        )

        test_features = MarketFeatures(
            symbol='DOGE-USDT',
            last_price=0.08,
            volume_24h=1000000,
            volume_1h=50000,
            volatility=0.015,
            price_change_1m=0.002,
            price_change_5m=0.008
        )

        logger.info(f"🧪 Testing LLM with account context")

        # Process signal through LLM (this will include account context)
        llm_decision = await llm_integration.process_signal(test_signal, test_features)

        if llm_decision:
            logger.info(f"✅ LLM Decision with Account Context:")
            logger.info(f"   Symbol: {llm_decision.symbol}")
            logger.info(f"   Decision: {llm_decision.final_decision}")
            logger.info(f"   Confidence: {llm_decision.confidence:.0f}%")
            logger.info(f"   Risk Assessment: {llm_decision.risk_assessment}")
            logger.info(f"   Reasoning: {llm_decision.reasoning[:100]}...")
        else:
            logger.warning("No LLM decision received (LLM server may be offline)")

        # Stop account monitoring
        await account_tracker.stop_monitoring()

        return True

    except Exception as e:
        logger.error(f"❌ LLM account integration test failed: {e}")
        return False

async def test_leverage_and_position_sizing():
    """Test leverage and position sizing calculations."""
    try:
        logger.info("\n⚖️ Testing Leverage and Position Sizing")

        # Test leverage configuration
        leverage = float(os.getenv('LEVERAGE', '20'))
        account_balance = float(os.getenv('ACCOUNT_BALANCE', '5.0'))
        max_position = float(os.getenv('MAX_POSITION_SIZE', '4.0'))

        logger.info(f"📊 Configuration:")
        logger.info(f"   Account Balance: ${account_balance}")
        logger.info(f"   Leverage: {leverage}x")
        logger.info(f"   Max Position Size: ${max_position}")

        # Calculate effective buying power
        buying_power = account_balance * leverage
        logger.info(f"   Effective Buying Power: ${buying_power}")

        # Test position sizing scenarios
        test_scenarios = [
            {'confidence': 0.9, 'conviction': 5, 'price': 0.08},
            {'confidence': 0.7, 'conviction': 3, 'price': 0.08},
            {'confidence': 0.5, 'conviction': 2, 'price': 0.08}
        ]

        logger.info(f"🧪 Position Sizing Scenarios:")

        for i, scenario in enumerate(test_scenarios, 1):
            confidence = scenario['confidence']
            conviction = scenario['conviction']
            price = scenario['price']

            # Simple position sizing calculation
            base_position = 1.0  # $1 base
            confidence_multiplier = 0.5 + (confidence * 1.5)
            conviction_multiplier = 0.8 + ((conviction - 1) / 4) * 0.6

            position_size = min(base_position * confidence_multiplier * conviction_multiplier, max_position)

            # Calculate margin required
            margin_required = position_size / leverage

            logger.info(f"   Scenario {i}: Confidence {confidence:.0%}, Conviction {conviction}⭐")
            logger.info(f"      Position Size: ${position_size:.2f}")
            logger.info(f"      Margin Required: ${margin_required:.2f}")
            logger.info(f"      Margin Usage: {(margin_required/account_balance)*100:.1f}%")

        return True

    except Exception as e:
        logger.error(f"❌ Leverage and position sizing test failed: {e}")
        return False

async def main():
    """Main test function."""
    logger.info("🚀 Starting Phase 8 Account-Aware Live Trade System Tests")
    logger.info("=" * 70)

    # Check environment
    api_key = os.getenv('HTX_API_KEY')
    if not api_key or api_key == 'your_htx_api_key_here':
        logger.warning("⚠️ HTX API credentials not configured - using mock data")

    # Run tests
    test_results = []

    # Test 1: Account Tracker
    result1 = await test_account_tracker()
    test_results.append(("Account Tracker", result1))

    # Test 2: Execution Safeguards
    result2 = await test_execution_safeguards()
    test_results.append(("Execution Safeguards", result2))

    # Test 3: LLM Account Integration
    result3 = await test_llm_account_integration()
    test_results.append(("LLM Account Integration", result3))

    # Test 4: Leverage and Position Sizing
    result4 = await test_leverage_and_position_sizing()
    test_results.append(("Leverage & Position Sizing", result4))

    # Summary
    logger.info("\n" + "=" * 70)
    logger.info("🏁 Phase 8 Test Summary")
    logger.info("=" * 70)

    passed = 0
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} {test_name}")
        if result:
            passed += 1

    logger.info(f"\nResults: {passed}/{len(test_results)} tests passed")

    if passed == len(test_results):
        logger.info("🎉 All tests passed! Phase 8 Account-Aware System ready!")
        logger.info("\n🚀 Phase 8 Features Operational:")
        logger.info("   ✅ Live account monitoring with real-time snapshots")
        logger.info("   ✅ Account-aware execution safeguards")
        logger.info("   ✅ LLM integration with account context")
        logger.info("   ✅ Leverage and position sizing controls")
        logger.info("   ✅ Risk-based trade blocking and warnings")
    else:
        logger.warning("⚠️ Some tests failed. Review configuration before live trading.")

    logger.info("\n🎯 Ready for Live Trading with DOGE/USDT:USDT!")
    logger.info("💰 Account Balance: $5.00 | Leverage: 20x | Max Position: $4.00")

if __name__ == "__main__":
    asyncio.run(main())
