#!/usr/bin/env python3
"""
Signal Generation Demo for Epinnox V6
Demonstrates AI model analysis and signal generation
"""

import asyncio
import logging
import sys
import time
import random
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from feeds.trade_parser import MarketFeatures
from storage.live_store import LiveDataStore
from models.smart_strategy import SmartStrategy

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class SignalDemo:
    """Demonstrate AI signal generation with realistic market scenarios."""
    
    def __init__(self):
        self.config = {
            'symbols': {
                'enabled': ['BTC-USDT', 'ETH-USDT'],
                'default': 'BTC-USDT'
            },
            'data_storage': {
                'trade_window_size': 500,
                'feature_update_interval': 2,
                'memory_cleanup_interval': 300,
                'persist_state': False
            },
            'models': {
                'weights': {
                    'rsi': 1.2,
                    'vwap': 1.0,
                    'orderflow': 1.5,
                    'volatility': 1.1
                },
                'rsi': {
                    'period': 14,
                    'overbought': 70,
                    'oversold': 30
                },
                'vwap': {
                    'deviation_threshold': 0.02
                },
                'orderflow': {
                    'imbalance_threshold': 0.3
                },
                'volatility': {
                    'threshold_high': 0.05,
                    'threshold_low': 0.01
                }
            },
            'signals': {
                'confidence_threshold': 0.6,
                'signal_cooldown': 10,  # Shorter for demo
                'thresholds': {
                    'strong_buy': 0.8,
                    'buy': 0.4,
                    'sell': -0.4,
                    'strong_sell': -0.8
                }
            }
        }
        
        # Initialize components
        self.data_store = LiveDataStore(self.config)
        self.strategy_engine = SmartStrategy(self.config, self.data_store)
        
        logger.info("🎯 Signal Demo initialized")
    
    def create_market_scenario(self, scenario_name: str, symbol: str = 'BTC-USDT') -> MarketFeatures:
        """Create different market scenarios for testing."""
        base_price = 50000.0
        current_time = int(time.time() * 1000)
        
        scenarios = {
            'oversold_bounce': MarketFeatures(
                symbol=symbol,
                timestamp=current_time,
                last_price=base_price,
                price_change_1m=-0.03,  # 3% drop
                price_velocity=-0.001,
                volume_1m=150.0,
                buy_volume_ratio=0.7,   # Strong buying interest
                volume_delta=50.0,
                order_flow_imbalance=0.5,  # Strong buy pressure
                trade_intensity=8.0,
                avg_trade_size=0.8,
                rsi=25.0,  # Oversold
                vwap=base_price * 1.02,  # Price below VWAP
                volatility=0.04
            ),
            
            'overbought_reversal': MarketFeatures(
                symbol=symbol,
                timestamp=current_time,
                last_price=base_price,
                price_change_1m=0.04,   # 4% pump
                price_velocity=0.002,
                volume_1m=200.0,
                buy_volume_ratio=0.3,   # Selling pressure
                volume_delta=-60.0,
                order_flow_imbalance=-0.4,  # Strong sell pressure
                trade_intensity=12.0,
                avg_trade_size=1.2,
                rsi=78.0,  # Overbought
                vwap=base_price * 0.98,  # Price above VWAP
                volatility=0.06
            ),
            
            'breakout_momentum': MarketFeatures(
                symbol=symbol,
                timestamp=current_time,
                last_price=base_price,
                price_change_1m=0.02,   # 2% move
                price_velocity=0.0015,
                volume_1m=300.0,        # High volume
                buy_volume_ratio=0.8,   # Strong buying
                volume_delta=120.0,
                order_flow_imbalance=0.6,  # Very strong buy pressure
                trade_intensity=15.0,
                avg_trade_size=1.5,
                rsi=65.0,  # Bullish but not overbought
                vwap=base_price * 0.995,  # Price slightly above VWAP
                volatility=0.03
            ),
            
            'sideways_chop': MarketFeatures(
                symbol=symbol,
                timestamp=current_time,
                last_price=base_price,
                price_change_1m=0.001,  # Minimal movement
                price_velocity=0.0001,
                volume_1m=80.0,         # Low volume
                buy_volume_ratio=0.52,  # Balanced
                volume_delta=3.0,
                order_flow_imbalance=0.05,  # Minimal imbalance
                trade_intensity=3.0,
                avg_trade_size=0.3,
                rsi=50.0,  # Neutral
                vwap=base_price,  # Price at VWAP
                volatility=0.015
            ),
            
            'high_volatility': MarketFeatures(
                symbol=symbol,
                timestamp=current_time,
                last_price=base_price,
                price_change_1m=0.06,   # 6% move
                price_velocity=0.003,
                volume_1m=400.0,        # Very high volume
                buy_volume_ratio=0.45,  # Mixed sentiment
                volume_delta=-40.0,
                order_flow_imbalance=-0.1,
                trade_intensity=20.0,
                avg_trade_size=2.0,
                rsi=55.0,
                vwap=base_price * 1.01,
                volatility=0.08  # High volatility
            )
        }
        
        return scenarios.get(scenario_name, scenarios['sideways_chop'])
    
    async def run_scenario_test(self, scenario_name: str):
        """Run a specific market scenario and analyze the AI response."""
        logger.info(f"\n🎬 Testing Scenario: {scenario_name.upper()}")
        logger.info("=" * 50)
        
        # Create market features for scenario
        features = self.create_market_scenario(scenario_name)
        
        # Display scenario details
        logger.info(f"📊 Market Conditions:")
        logger.info(f"   Price: ${features.last_price:,.2f}")
        logger.info(f"   1m Change: {features.price_change_1m:.2%}")
        logger.info(f"   Volume: {features.volume_1m:.1f}")
        logger.info(f"   Buy Ratio: {features.buy_volume_ratio:.1%}")
        logger.info(f"   Order Flow: {features.order_flow_imbalance:.2f}")
        logger.info(f"   RSI: {features.rsi:.1f}")
        logger.info(f"   VWAP: ${features.vwap:.2f}")
        logger.info(f"   Volatility: {features.volatility:.3f}")
        
        # Store features
        self.data_store.store_features(features)
        
        # Process through AI strategy
        signal = await self.strategy_engine.process_features(features)
        
        if signal:
            logger.info(f"\n🎯 AI SIGNAL GENERATED:")
            logger.info(f"   Action: {signal.action}")
            logger.info(f"   Confidence: {signal.confidence:.1%}")
            logger.info(f"   Score: {signal.score:.3f}")
            logger.info(f"   Reasoning: {signal.reasoning}")
            logger.info(f"   Model Contributions:")
            for model, contribution in signal.model_contributions.items():
                logger.info(f"     {model}: {contribution:.3f}")
        else:
            logger.info(f"\n⏸️ No signal generated (low confidence or cooldown)")
        
        # Get model outputs for detailed analysis
        model_outputs = self.data_store.get_model_outputs(features.symbol)
        if model_outputs:
            logger.info(f"\n🤖 Individual Model Analysis:")
            for model_name, output in model_outputs.items():
                logger.info(f"   {model_name.upper()}: {output.get('action', 'N/A')} "
                          f"({output.get('confidence', 0):.1%}) - {output.get('signal', 'N/A')}")
        
        logger.info("=" * 50)
        
        return signal
    
    async def run_all_scenarios(self):
        """Run all market scenarios to demonstrate AI capabilities."""
        logger.info("🚀 Starting AI Signal Generation Demo")
        logger.info("🧠 Testing multiple market scenarios with AI models")
        
        scenarios = [
            'oversold_bounce',
            'overbought_reversal', 
            'breakout_momentum',
            'sideways_chop',
            'high_volatility'
        ]
        
        results = {}
        
        for scenario in scenarios:
            signal = await self.run_scenario_test(scenario)
            results[scenario] = signal
            
            # Small delay between scenarios
            await asyncio.sleep(2)
        
        # Summary
        logger.info("\n🎯 DEMO SUMMARY")
        logger.info("=" * 60)
        
        signals_generated = sum(1 for signal in results.values() if signal is not None)
        
        logger.info(f"📊 Scenarios tested: {len(scenarios)}")
        logger.info(f"📈 Signals generated: {signals_generated}")
        logger.info(f"🎯 Signal rate: {signals_generated/len(scenarios)*100:.1f}%")
        
        logger.info(f"\n📋 Signal Breakdown:")
        for scenario, signal in results.items():
            if signal:
                logger.info(f"   {scenario}: {signal.action} ({signal.confidence:.1%})")
            else:
                logger.info(f"   {scenario}: No signal")
        
        # Performance metrics
        performance = self.data_store.get_performance_metrics()
        logger.info(f"\n📊 Performance Metrics:")
        logger.info(f"   Total signals: {performance.get('total_signals', 0)}")
        logger.info(f"   Win rate: {performance.get('win_rate', 0):.1%}")
        
        logger.info("\n🎉 Demo completed successfully!")
        logger.info("🌐 Check dashboard at http://localhost:8086 for real-time view")

async def main():
    """Main demo entry point."""
    demo = SignalDemo()
    await demo.run_all_scenarios()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Demo interrupted by user")
    except Exception as e:
        print(f"❌ Demo error: {e}")
        sys.exit(1)
