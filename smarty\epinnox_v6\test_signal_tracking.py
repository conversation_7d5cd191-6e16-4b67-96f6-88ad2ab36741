#!/usr/bin/env python3
"""
Test Script for Enhanced Signal Tracking and Performance Analysis
Tests the new signal tracking, performance analysis, and chart functionality
"""

import asyncio
import json
import logging
import time
import yaml
from typing import Dict, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import our components
from analytics.signal_tracker import SignalTracker, SignalStatus
from analytics.performance_analyzer import PerformanceAnalyzer
from storage.live_store import LiveDataStore

def load_test_config() -> Dict[str, Any]:
    """Load test configuration."""
    return {
        'signal_tracking': {
            'default_tp_percentage': 2.0,
            'default_sl_percentage': 1.0,
            'signal_expiry_hours': 24
        },
        'symbols': {
            'default': 'BTC-USDT',
            'enabled': ['BTC-USDT', 'ETH-USDT', 'DOGE-USDT']
        },
        'data_storage': {
            'persist_state': False,
            'memory_cleanup_interval': 3600
        }
    }

def create_test_signal(symbol: str, action: str, price: float, confidence: float) -> Dict[str, Any]:
    """Create a test signal."""
    return {
        'symbol': symbol,
        'action': action,
        'price': price,
        'confidence': confidence,
        'timestamp': time.time(),
        'signal_id': f"{symbol}_{action}_{int(time.time())}"
    }

async def test_signal_tracker():
    """Test the SignalTracker functionality."""
    logger.info("🧪 Testing Signal Tracker...")
    
    config = load_test_config()
    tracker = SignalTracker(config)
    
    # Test 1: Add signals
    logger.info("📊 Test 1: Adding signals...")
    
    signals = [
        create_test_signal('BTC-USDT', 'LONG', 45000.0, 0.85),
        create_test_signal('BTC-USDT', 'SHORT', 45100.0, 0.75),
        create_test_signal('ETH-USDT', 'LONG', 2800.0, 0.90),
    ]
    
    signal_ids = []
    for signal in signals:
        signal_id = tracker.add_signal(signal)
        signal_ids.append(signal_id)
        logger.info(f"✅ Added signal: {signal_id}")
    
    # Test 2: Update prices and check TP/SL
    logger.info("📈 Test 2: Testing price updates and TP/SL...")
    
    # Simulate price movements
    price_updates = [
        ('BTC-USDT', 45900.0),  # Should trigger TP for LONG
        ('BTC-USDT', 44600.0),  # Should trigger SL for SHORT
        ('ETH-USDT', 2856.0),   # Should trigger TP for ETH LONG
    ]
    
    for symbol, price in price_updates:
        tracker.update_price(symbol, price)
        logger.info(f"💰 Updated {symbol} price to ${price:.2f}")
    
    # Test 3: Get performance metrics
    logger.info("📊 Test 3: Getting performance metrics...")
    
    metrics = tracker.get_performance_metrics()
    logger.info(f"📈 Total signals: {metrics.total_signals}")
    logger.info(f"🎯 Win rate: {metrics.win_rate:.1f}%")
    logger.info(f"💵 Total P&L: ${metrics.total_pnl:.2f}")
    logger.info(f"🔄 Active positions: {metrics.active_positions}")
    
    # Test 4: Get signals for display
    logger.info("🖥️ Test 4: Getting signals for display...")
    
    display_signals = tracker.get_signals_for_display(10)
    for signal in display_signals:
        logger.info(f"📋 {signal['symbol']} {signal['direction']} - {signal['status_text']} - P&L: ${signal['pnl']:.2f}")
    
    return tracker

async def test_performance_analyzer(signal_tracker):
    """Test the PerformanceAnalyzer functionality."""
    logger.info("🧪 Testing Performance Analyzer...")
    
    analyzer = PerformanceAnalyzer(signal_tracker)
    
    # Test 1: Add price data
    logger.info("📈 Test 1: Adding price data...")
    
    price_data = [
        ('BTC-USDT', 45000.0),
        ('BTC-USDT', 45050.0),
        ('BTC-USDT', 45100.0),
        ('BTC-USDT', 45200.0),
        ('ETH-USDT', 2800.0),
        ('ETH-USDT', 2820.0),
        ('ETH-USDT', 2850.0),
    ]
    
    for symbol, price in price_data:
        analyzer.add_price_data(symbol, price)
        logger.info(f"📊 Added price data: {symbol} @ ${price:.2f}")
    
    # Test 2: Get advanced metrics
    logger.info("📊 Test 2: Getting advanced metrics...")
    
    advanced_metrics = analyzer.calculate_advanced_metrics()
    logger.info(f"🎯 Win rate: {advanced_metrics.win_rate:.1f}%")
    logger.info(f"💰 Profit factor: {advanced_metrics.profit_factor:.2f}")
    logger.info(f"📈 Average win: ${advanced_metrics.average_win:.2f}")
    logger.info(f"📉 Average loss: ${advanced_metrics.average_loss:.2f}")
    logger.info(f"⏱️ Average holding time: {advanced_metrics.average_holding_time_minutes:.1f} minutes")
    
    # Test 3: Get performance summary
    logger.info("📋 Test 3: Getting performance summary...")
    
    summary = analyzer.get_performance_summary()
    if summary:
        logger.info(f"📊 Performance summary generated with {len(summary.get('recent_signals', []))} recent signals")
        logger.info(f"📈 P&L chart data points: {len(summary.get('pnl_chart_data', []))}")
    
    # Test 4: Get chart data
    logger.info("📈 Test 4: Getting chart data...")
    
    btc_chart_data = analyzer.get_chart_price_data('BTC-USDT', 50)
    eth_chart_data = analyzer.get_chart_price_data('ETH-USDT', 50)
    
    logger.info(f"📊 BTC chart data points: {len(btc_chart_data)}")
    logger.info(f"📊 ETH chart data points: {len(eth_chart_data)}")
    
    return analyzer

async def test_data_store_integration():
    """Test integration with LiveDataStore."""
    logger.info("🧪 Testing Data Store Integration...")
    
    config = load_test_config()
    data_store = LiveDataStore(config)
    
    # Test storing signals with enhanced data
    test_signals = [
        {
            'symbol': 'BTC-USDT',
            'action': 'LONG',
            'price': 45000.0,
            'confidence': 0.85,
            'timestamp': time.time(),
            'pnl': 450.0,
            'status': 'profitable'
        },
        {
            'symbol': 'BTC-USDT',
            'action': 'SHORT',
            'price': 45100.0,
            'confidence': 0.75,
            'timestamp': time.time() - 300,
            'pnl': -225.0,
            'status': 'stopped_out'
        }
    ]
    
    for signal in test_signals:
        data_store.store_signal(signal)
        logger.info(f"💾 Stored signal: {signal['symbol']} {signal['action']} - P&L: ${signal['pnl']:.2f}")
    
    # Test getting dashboard data
    dashboard_data = data_store.get_dashboard_data('BTC-USDT')
    logger.info(f"🖥️ Dashboard data retrieved with {len(dashboard_data.get('recent_signals', []))} signals")
    
    return data_store

async def test_chart_data_format():
    """Test chart data formatting."""
    logger.info("🧪 Testing Chart Data Format...")
    
    config = load_test_config()
    tracker = SignalTracker(config)
    analyzer = PerformanceAnalyzer(tracker)
    
    # Add some test data
    test_signals = [
        create_test_signal('BTC-USDT', 'LONG', 45000.0, 0.85),
        create_test_signal('BTC-USDT', 'SHORT', 45100.0, 0.75),
    ]
    
    for signal in test_signals:
        tracker.add_signal(signal)
    
    # Add price data
    for i in range(10):
        price = 45000 + (i * 50)
        analyzer.add_price_data('BTC-USDT', price)
    
    # Get chart data
    signal_chart_data = tracker.get_chart_data('BTC-USDT')
    price_chart_data = analyzer.get_chart_price_data('BTC-USDT')
    
    logger.info(f"📊 Signal chart data: {len(signal_chart_data['signals'])} signals")
    logger.info(f"📈 Price chart data: {len(price_chart_data)} price points")
    
    # Verify data format
    if signal_chart_data['signals']:
        sample_signal = signal_chart_data['signals'][0]
        required_fields = ['x', 'y', 'direction', 'confidence', 'status']
        for field in required_fields:
            if field in sample_signal:
                logger.info(f"✅ Signal data has required field: {field}")
            else:
                logger.error(f"❌ Signal data missing field: {field}")
    
    if price_chart_data:
        sample_price = price_chart_data[0]
        required_fields = ['x', 'y']
        for field in required_fields:
            if field in sample_price:
                logger.info(f"✅ Price data has required field: {field}")
            else:
                logger.error(f"❌ Price data missing field: {field}")

async def run_comprehensive_test():
    """Run comprehensive test of all functionality."""
    logger.info("🚀 Starting Comprehensive Signal Tracking Test...")
    logger.info("=" * 60)
    
    try:
        # Test 1: Signal Tracker
        signal_tracker = await test_signal_tracker()
        logger.info("✅ Signal Tracker test completed")
        
        # Test 2: Performance Analyzer
        performance_analyzer = await test_performance_analyzer(signal_tracker)
        logger.info("✅ Performance Analyzer test completed")
        
        # Test 3: Data Store Integration
        data_store = await test_data_store_integration()
        logger.info("✅ Data Store Integration test completed")
        
        # Test 4: Chart Data Format
        await test_chart_data_format()
        logger.info("✅ Chart Data Format test completed")
        
        logger.info("=" * 60)
        logger.info("🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
        logger.info("=" * 60)
        
        # Final summary
        final_metrics = signal_tracker.get_performance_metrics()
        logger.info("📊 FINAL TEST RESULTS:")
        logger.info(f"   • Total signals tracked: {final_metrics.total_signals}")
        logger.info(f"   • Profitable signals: {final_metrics.profitable_signals}")
        logger.info(f"   • Win rate: {final_metrics.win_rate:.1f}%")
        logger.info(f"   • Total P&L: ${final_metrics.total_pnl:.2f}")
        logger.info(f"   • Active positions: {final_metrics.active_positions}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Enhanced Signal Tracking Test Suite")
    print("=" * 50)
    
    # Run the comprehensive test
    success = asyncio.run(run_comprehensive_test())
    
    if success:
        print("\n✅ All tests passed! Signal tracking system is ready.")
    else:
        print("\n❌ Some tests failed. Please check the logs.")
