#!/usr/bin/env python3
"""
Final Decision Enhancer
Phase 6-7 bridge that enhances LLM decisions with meta-awareness and clarity
"""

import logging
import time
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class ConsensusLevel(Enum):
    STRONG_AGREEMENT = "STRONG_AGREEMENT"
    MODERATE_AGREEMENT = "MODERATE_AGREEMENT"
    CONFLICTED = "CONFLICTED"
    NO_CONSENSUS = "NO_CONSENSUS"

@dataclass
class EnhancedDecision:
    """Enhanced decision with meta-awareness and clarity."""
    # Original decision
    original_decision: str
    confidence: float
    reasoning: str

    # Enhanced metadata
    final_decision_enhanced: str  # e.g., "LONG ✅", "WAIT ⚠️"
    final_commentary: str  # Plain language alert
    risk_assessment: RiskLevel
    model_consensus: ConsensusLevel
    conviction_score: int  # 1-5 stars
    reasoning_quality: str  # HIGH, MEDIUM, LOW

    # Meta-awareness
    conflicting_models: List[str]
    disagreement_detected: bool
    market_regime_factor: float
    confidence_drift: float

    # Performance context
    recent_performance: Dict[str, float]
    similar_setup_history: List[Dict[str, Any]]

    # Timestamps
    timestamp: float
    processing_time_ms: float

class FinalDecisionEnhancer:
    """
    Final Decision Enhancer that bridges Phase 6 execution with Phase 7 autonomy.
    Enhances LLM decisions with meta-awareness, conflict detection, and clarity.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.enhancer_config = config.get('final_decision_enhancer', {})

        # Enhancement parameters
        self.confidence_threshold_high = self.enhancer_config.get('confidence_threshold_high', 0.8)
        self.confidence_threshold_low = self.enhancer_config.get('confidence_threshold_low', 0.5)
        self.consensus_threshold = self.enhancer_config.get('consensus_threshold', 0.7)
        self.conflict_threshold = self.enhancer_config.get('conflict_threshold', 0.3)

        # Risk assessment parameters
        self.risk_factors = self.enhancer_config.get('risk_factors', {
            'volatility_weight': 0.3,
            'confidence_weight': 0.3,
            'consensus_weight': 0.2,
            'regime_weight': 0.2
        })

        # Performance tracking
        self.recent_decisions: List[EnhancedDecision] = []
        self.max_history = self.enhancer_config.get('max_history', 100)

        logger.info("Final Decision Enhancer initialized")

    def enhance_decision(self, llm_decision: Dict[str, Any],
                        model_outputs: Dict[str, Any],
                        market_data: Dict[str, Any],
                        performance_context: Optional[Dict[str, Any]] = None) -> EnhancedDecision:
        """
        Enhance an LLM decision with meta-awareness and clarity.

        Args:
            llm_decision: Original LLM decision
            model_outputs: Individual model outputs for consensus analysis
            market_data: Current market conditions
            performance_context: Recent performance data

        Returns:
            EnhancedDecision with comprehensive metadata
        """
        try:
            start_time = time.time()

            # Extract original decision components
            original_decision = llm_decision.get('final_decision', 'WAIT')
            confidence = llm_decision.get('confidence', 0.0) / 100.0  # Convert to 0-1
            reasoning = llm_decision.get('reasoning', '')

            # Analyze model consensus
            model_consensus, conflicting_models = self._analyze_model_consensus(model_outputs)
            disagreement_detected = len(conflicting_models) > 0

            # Assess risk level
            risk_assessment = self._assess_risk_level(confidence, model_consensus, market_data)

            # Calculate conviction score
            conviction_score = self._calculate_conviction_score(confidence, model_consensus, reasoning)

            # Assess reasoning quality
            reasoning_quality = self._assess_reasoning_quality(reasoning, model_outputs)

            # Generate enhanced decision and commentary
            final_decision_enhanced = self._generate_enhanced_decision(original_decision, risk_assessment, model_consensus)
            final_commentary = self._generate_final_commentary(original_decision, risk_assessment, model_consensus, conflicting_models)

            # Calculate meta-awareness metrics
            market_regime_factor = self._calculate_regime_factor(market_data)
            confidence_drift = self._calculate_confidence_drift(confidence)

            # Get performance context
            recent_performance = performance_context or {}
            similar_setup_history = self._find_similar_setups(llm_decision, model_outputs)

            # Create enhanced decision
            enhanced_decision = EnhancedDecision(
                original_decision=original_decision,
                confidence=confidence,
                reasoning=reasoning,
                final_decision_enhanced=final_decision_enhanced,
                final_commentary=final_commentary,
                risk_assessment=risk_assessment,
                model_consensus=model_consensus,
                conviction_score=conviction_score,
                reasoning_quality=reasoning_quality,
                conflicting_models=conflicting_models,
                disagreement_detected=disagreement_detected,
                market_regime_factor=market_regime_factor,
                confidence_drift=confidence_drift,
                recent_performance=recent_performance,
                similar_setup_history=similar_setup_history,
                timestamp=time.time(),
                processing_time_ms=(time.time() - start_time) * 1000
            )

            # Store for history
            self.recent_decisions.append(enhanced_decision)
            if len(self.recent_decisions) > self.max_history:
                self.recent_decisions = self.recent_decisions[-self.max_history:]

            logger.debug(f"Enhanced decision: {final_decision_enhanced} (Risk: {risk_assessment.value}, Consensus: {model_consensus.value})")

            return enhanced_decision

        except Exception as e:
            logger.error(f"Error enhancing decision: {e}")
            return self._create_fallback_decision(llm_decision)

    def _analyze_model_consensus(self, model_outputs: Dict[str, Any]) -> Tuple[ConsensusLevel, List[str]]:
        """Analyze consensus among model outputs."""
        try:
            if not model_outputs:
                return ConsensusLevel.NO_CONSENSUS, []

            # Extract model signals
            model_signals = {}
            for model_name, output in model_outputs.items():
                signal = output.get('signal', 'NEUTRAL')
                confidence = output.get('confidence', 0.0)
                model_signals[model_name] = (signal, confidence)

            # Count signal types
            signal_counts = {}
            for model_name, (signal, confidence) in model_signals.items():
                if signal not in signal_counts:
                    signal_counts[signal] = []
                signal_counts[signal].append((model_name, confidence))

            # Determine consensus
            total_models = len(model_signals)
            if not signal_counts:
                return ConsensusLevel.NO_CONSENSUS, []

            # Find dominant signal
            dominant_signal = max(signal_counts.keys(), key=lambda x: len(signal_counts[x]))
            dominant_count = len(signal_counts[dominant_signal])

            # Identify conflicting models
            conflicting_models = []
            for signal, models in signal_counts.items():
                if signal != dominant_signal:
                    conflicting_models.extend([model[0] for model in models])

            # Determine consensus level
            consensus_ratio = dominant_count / total_models

            if consensus_ratio >= 0.8:
                consensus_level = ConsensusLevel.STRONG_AGREEMENT
            elif consensus_ratio >= 0.6:
                consensus_level = ConsensusLevel.MODERATE_AGREEMENT
            elif consensus_ratio >= 0.4:
                consensus_level = ConsensusLevel.CONFLICTED
            else:
                consensus_level = ConsensusLevel.NO_CONSENSUS

            return consensus_level, conflicting_models

        except Exception as e:
            logger.error(f"Error analyzing model consensus: {e}")
            return ConsensusLevel.NO_CONSENSUS, []

    def _assess_risk_level(self, confidence: float, consensus: ConsensusLevel, market_data: Dict[str, Any]) -> RiskLevel:
        """Assess overall risk level for the decision."""
        try:
            risk_score = 0.0

            # Confidence factor (lower confidence = higher risk)
            confidence_risk = (1.0 - confidence) * self.risk_factors['confidence_weight']
            risk_score += confidence_risk

            # Consensus factor (less consensus = higher risk)
            consensus_risk_map = {
                ConsensusLevel.STRONG_AGREEMENT: 0.0,
                ConsensusLevel.MODERATE_AGREEMENT: 0.2,
                ConsensusLevel.CONFLICTED: 0.6,
                ConsensusLevel.NO_CONSENSUS: 1.0
            }
            consensus_risk = consensus_risk_map.get(consensus, 0.5) * self.risk_factors['consensus_weight']
            risk_score += consensus_risk

            # Volatility factor
            volatility = market_data.get('volatility', 0.01)
            volatility_risk = min(volatility * 50, 1.0) * self.risk_factors['volatility_weight']  # Scale volatility
            risk_score += volatility_risk

            # Market regime factor
            regime = market_data.get('market_regime', 'unknown')
            regime_risk_map = {
                'trending_up': 0.1,
                'trending_down': 0.1,
                'ranging': 0.3,
                'high_volatility': 0.8,
                'unknown': 0.5
            }
            regime_risk = regime_risk_map.get(regime, 0.5) * self.risk_factors['regime_weight']
            risk_score += regime_risk

            # Determine risk level
            if risk_score <= 0.2:
                return RiskLevel.LOW
            elif risk_score <= 0.5:
                return RiskLevel.MEDIUM
            elif risk_score <= 0.8:
                return RiskLevel.HIGH
            else:
                return RiskLevel.CRITICAL

        except Exception as e:
            logger.error(f"Error assessing risk level: {e}")
            return RiskLevel.MEDIUM

    def _calculate_conviction_score(self, confidence: float, consensus: ConsensusLevel, reasoning: str) -> int:
        """Calculate conviction score (1-5 stars)."""
        try:
            base_score = confidence * 3  # 0-3 from confidence

            # Consensus bonus
            consensus_bonus_map = {
                ConsensusLevel.STRONG_AGREEMENT: 1.5,
                ConsensusLevel.MODERATE_AGREEMENT: 1.0,
                ConsensusLevel.CONFLICTED: 0.0,
                ConsensusLevel.NO_CONSENSUS: -0.5
            }
            consensus_bonus = consensus_bonus_map.get(consensus, 0.0)

            # Reasoning quality bonus
            reasoning_bonus = 0.5 if len(reasoning) > 100 else 0.0

            # Calculate final score
            final_score = base_score + consensus_bonus + reasoning_bonus

            # Clamp to 1-5 range
            return max(1, min(5, int(round(final_score))))

        except Exception as e:
            logger.error(f"Error calculating conviction score: {e}")
            return 3

    def _assess_reasoning_quality(self, reasoning: str, model_outputs: Dict[str, Any]) -> str:
        """Assess the quality of reasoning provided."""
        try:
            quality_score = 0

            # Length and detail
            if len(reasoning) > 200:
                quality_score += 2
            elif len(reasoning) > 100:
                quality_score += 1

            # Technical terms and analysis depth
            technical_terms = ['support', 'resistance', 'trend', 'volume', 'volatility', 'momentum', 'rsi', 'vwap']
            term_count = sum(1 for term in technical_terms if term.lower() in reasoning.lower())
            quality_score += min(term_count, 3)

            # Model integration
            model_mentions = sum(1 for model in model_outputs.keys() if model.lower() in reasoning.lower())
            if model_mentions >= 2:
                quality_score += 2
            elif model_mentions >= 1:
                quality_score += 1

            # Risk consideration
            risk_terms = ['risk', 'caution', 'uncertainty', 'volatility']
            if any(term in reasoning.lower() for term in risk_terms):
                quality_score += 1

            # Determine quality level
            if quality_score >= 7:
                return "HIGH"
            elif quality_score >= 4:
                return "MEDIUM"
            else:
                return "LOW"

        except Exception as e:
            logger.error(f"Error assessing reasoning quality: {e}")
            return "MEDIUM"

    def _generate_enhanced_decision(self, decision: str, risk: RiskLevel, consensus: ConsensusLevel) -> str:
        """Generate enhanced decision with visual indicators."""
        try:
            # Base decision
            enhanced = decision.upper()

            # Add visual indicators based on risk and consensus
            if risk == RiskLevel.LOW and consensus == ConsensusLevel.STRONG_AGREEMENT:
                if decision.upper() in ['LONG', 'SHORT']:
                    enhanced += " ✅"
                else:
                    enhanced += " ✅"
            elif risk == RiskLevel.MEDIUM or consensus == ConsensusLevel.MODERATE_AGREEMENT:
                enhanced += " ⚠️"
            elif risk == RiskLevel.HIGH or consensus == ConsensusLevel.CONFLICTED:
                enhanced += " ⚠️"
            elif risk == RiskLevel.CRITICAL or consensus == ConsensusLevel.NO_CONSENSUS:
                enhanced += " ❌"

            return enhanced

        except Exception as e:
            logger.error(f"Error generating enhanced decision: {e}")
            return decision.upper()

    def _generate_final_commentary(self, decision: str, risk: RiskLevel,
                                 consensus: ConsensusLevel, conflicting_models: List[str]) -> str:
        """Generate plain language commentary."""
        try:
            commentary_parts = []

            # Risk-based commentary
            if risk == RiskLevel.LOW:
                commentary_parts.append("✅ Low risk setup")
            elif risk == RiskLevel.MEDIUM:
                commentary_parts.append("⚠️ Moderate risk")
            elif risk == RiskLevel.HIGH:
                commentary_parts.append("⚠️ High risk environment")
            else:
                commentary_parts.append("❌ Critical risk - exercise extreme caution")

            # Consensus commentary
            if consensus == ConsensusLevel.STRONG_AGREEMENT:
                commentary_parts.append("with strong model alignment")
            elif consensus == ConsensusLevel.MODERATE_AGREEMENT:
                commentary_parts.append("with moderate model agreement")
            elif consensus == ConsensusLevel.CONFLICTED:
                commentary_parts.append("with conflicting model signals")
                if conflicting_models:
                    commentary_parts.append(f"({', '.join(conflicting_models[:2])} disagree)")
            else:
                commentary_parts.append("with no clear model consensus")

            # Decision-specific commentary
            if decision.upper() == 'WAIT':
                commentary_parts.append("- holding position recommended")
            elif decision.upper() in ['LONG', 'SHORT']:
                commentary_parts.append(f"- {decision.lower()} position suggested")

            return " ".join(commentary_parts) + "."

        except Exception as e:
            logger.error(f"Error generating final commentary: {e}")
            return f"Decision: {decision} - assess risk and model consensus before acting."

    def _calculate_regime_factor(self, market_data: Dict[str, Any]) -> float:
        """Calculate market regime influence factor."""
        try:
            regime = market_data.get('market_regime', 'unknown')
            volatility = market_data.get('volatility', 0.01)

            # Base regime factors
            regime_factors = {
                'trending_up': 1.2,
                'trending_down': 1.2,
                'ranging': 0.8,
                'high_volatility': 0.6,
                'unknown': 1.0
            }

            base_factor = regime_factors.get(regime, 1.0)

            # Adjust for volatility
            volatility_adjustment = 1.0 - min(volatility * 10, 0.5)  # Higher volatility reduces factor

            return base_factor * volatility_adjustment

        except Exception as e:
            logger.error(f"Error calculating regime factor: {e}")
            return 1.0

    def _calculate_confidence_drift(self, current_confidence: float) -> float:
        """Calculate confidence drift from recent decisions."""
        try:
            if len(self.recent_decisions) < 5:
                return 0.0

            recent_confidences = [d.confidence for d in self.recent_decisions[-5:]]
            avg_recent = sum(recent_confidences) / len(recent_confidences)

            return current_confidence - avg_recent

        except Exception as e:
            logger.error(f"Error calculating confidence drift: {e}")
            return 0.0

    def _find_similar_setups(self, llm_decision: Dict[str, Any],
                           model_outputs: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find similar historical setups for context."""
        try:
            # Simplified similarity matching
            similar_setups = []

            current_decision = llm_decision.get('final_decision', '')
            current_confidence = llm_decision.get('confidence', 0.0)

            for past_decision in self.recent_decisions[-20:]:  # Last 20 decisions
                # Check decision similarity
                if past_decision.original_decision == current_decision:
                    # Check confidence similarity (within 10%)
                    confidence_diff = abs(past_decision.confidence - current_confidence / 100.0)
                    if confidence_diff <= 0.1:
                        similar_setups.append({
                            'timestamp': past_decision.timestamp,
                            'decision': past_decision.original_decision,
                            'confidence': past_decision.confidence,
                            'risk_assessment': past_decision.risk_assessment.value,
                            'conviction_score': past_decision.conviction_score
                        })

            return similar_setups[-3:]  # Return last 3 similar setups

        except Exception as e:
            logger.error(f"Error finding similar setups: {e}")
            return []

    def _create_fallback_decision(self, llm_decision: Dict[str, Any]) -> EnhancedDecision:
        """Create fallback enhanced decision when enhancement fails."""
        return EnhancedDecision(
            original_decision=llm_decision.get('final_decision', 'WAIT'),
            confidence=llm_decision.get('confidence', 0.0) / 100.0,
            reasoning=llm_decision.get('reasoning', ''),
            final_decision_enhanced=llm_decision.get('final_decision', 'WAIT') + " ⚠️",
            final_commentary="⚠️ Enhancement failed - manual review recommended.",
            risk_assessment=RiskLevel.MEDIUM,
            model_consensus=ConsensusLevel.NO_CONSENSUS,
            conviction_score=2,
            reasoning_quality="LOW",
            conflicting_models=[],
            disagreement_detected=False,
            market_regime_factor=1.0,
            confidence_drift=0.0,
            recent_performance={},
            similar_setup_history=[],
            timestamp=time.time(),
            processing_time_ms=0.0
        )

    def get_enhancement_summary(self) -> Dict[str, Any]:
        """Get summary of recent enhancements."""
        try:
            if not self.recent_decisions:
                return {'message': 'No recent decisions to analyze'}

            recent = self.recent_decisions[-10:]  # Last 10 decisions

            # Calculate statistics
            avg_confidence = sum(d.confidence for d in recent) / len(recent)
            avg_conviction = sum(d.conviction_score for d in recent) / len(recent)

            risk_distribution = {}
            for decision in recent:
                risk = decision.risk_assessment.value
                risk_distribution[risk] = risk_distribution.get(risk, 0) + 1

            consensus_distribution = {}
            for decision in recent:
                consensus = decision.model_consensus.value
                consensus_distribution[consensus] = consensus_distribution.get(consensus, 0) + 1

            conflict_rate = sum(1 for d in recent if d.disagreement_detected) / len(recent)

            return {
                'total_decisions': len(self.recent_decisions),
                'recent_decisions': len(recent),
                'avg_confidence': avg_confidence,
                'avg_conviction': avg_conviction,
                'risk_distribution': risk_distribution,
                'consensus_distribution': consensus_distribution,
                'conflict_rate': conflict_rate,
                'avg_processing_time_ms': sum(d.processing_time_ms for d in recent) / len(recent)
            }

        except Exception as e:
            logger.error(f"Error getting enhancement summary: {e}")
            return {'error': str(e)}
