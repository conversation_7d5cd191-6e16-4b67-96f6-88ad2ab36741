#!/usr/bin/env python3
"""
Phase 10: Autonomous Market Intelligence
Full autonomous trading AI that thinks, adapts, and trades independently
to maximize account growth through intelligent market analysis.
"""

import logging
import time
import asyncio
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

class MarketRegime(Enum):
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    BREAKOUT = "breakout"
    REVERSAL = "reversal"

class LiquidityLevel(Enum):
    VERY_HIGH = "very_high"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    VERY_LOW = "very_low"

@dataclass
class MarketIntelligence:
    """Comprehensive market intelligence for autonomous trading."""
    # Market structure
    regime: MarketRegime
    liquidity_level: LiquidityLevel
    order_book_imbalance: float  # -1 to 1 (bearish to bullish)
    recent_volume_profile: Dict[str, float]

    # Trading behavior analysis
    average_trade_size: float
    dominant_trade_size_range: Tuple[float, float]
    institutional_activity_score: float  # 0-1
    retail_activity_score: float  # 0-1

    # Optimal execution parameters
    recommended_position_size: float
    stealth_execution_required: bool
    optimal_entry_chunks: int
    time_between_chunks: float

    # Profit optimization
    dynamic_take_profit: float
    dynamic_stop_loss: float
    profit_probability: float
    expected_move_size: float

    # Meta-intelligence
    confidence_in_analysis: float
    market_predictability: float
    execution_difficulty: float
    timestamp: float

class AutonomousMarketIntelligence:
    """
    Autonomous Market Intelligence System

    Analyzes market microstructure, trading patterns, and liquidity
    to make intelligent autonomous trading decisions that maximize profits
    while minimizing market impact.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.intelligence_config = config.get('market_intelligence', {})

        # Analysis parameters
        self.lookback_periods = {
            'micro': 60,      # 1 minute for microstructure
            'short': 300,     # 5 minutes for short-term patterns
            'medium': 1800,   # 30 minutes for medium-term trends
            'long': 7200      # 2 hours for long-term context
        }

        # Trading behavior tracking
        self.recent_trades = []
        self.volume_profile = {}
        self.order_book_history = []

        # Intelligence state
        self.current_intelligence: Optional[MarketIntelligence] = None
        self.intelligence_history = []

        # Performance tracking
        self.autonomous_decisions = 0
        self.successful_predictions = 0
        self.total_profit_generated = 0.0

        logger.info("🧠 Autonomous Market Intelligence initialized")

    async def analyze_market_for_autonomous_trading(self,
                                                  market_data: Dict[str, Any],
                                                  order_book: Dict[str, Any],
                                                  recent_trades: List[Dict[str, Any]],
                                                  account_data: Dict[str, Any]) -> MarketIntelligence:
        """
        Perform comprehensive market analysis for autonomous trading decisions.

        This is the core intelligence function that analyzes:
        1. Market microstructure and liquidity
        2. Trading behavior patterns
        3. Optimal execution strategies
        4. Profit maximization opportunities
        """
        try:
            start_time = time.time()

            # Step 1: Analyze market regime
            regime = await self._analyze_market_regime(market_data, recent_trades)

            # Step 2: Assess liquidity and order book
            liquidity_analysis = await self._analyze_liquidity(order_book, recent_trades)

            # Step 3: Analyze trading behavior patterns
            behavior_analysis = await self._analyze_trading_behavior(recent_trades)

            # Step 4: Calculate optimal execution parameters
            execution_params = await self._calculate_optimal_execution(
                market_data, order_book, behavior_analysis, account_data
            )

            # Step 5: Optimize profit targets
            profit_optimization = await self._optimize_profit_targets(
                regime, liquidity_analysis, market_data
            )

            # Step 6: Generate meta-intelligence
            meta_intelligence = await self._generate_meta_intelligence(
                regime, liquidity_analysis, behavior_analysis, market_data
            )

            # Compile comprehensive intelligence
            intelligence = MarketIntelligence(
                regime=regime,
                liquidity_level=liquidity_analysis['level'],
                order_book_imbalance=liquidity_analysis['imbalance'],
                recent_volume_profile=behavior_analysis['volume_profile'],
                average_trade_size=behavior_analysis['avg_trade_size'],
                dominant_trade_size_range=behavior_analysis['dominant_range'],
                institutional_activity_score=behavior_analysis['institutional_score'],
                retail_activity_score=behavior_analysis['retail_score'],
                recommended_position_size=execution_params['position_size'],
                stealth_execution_required=execution_params['stealth_required'],
                optimal_entry_chunks=execution_params['entry_chunks'],
                time_between_chunks=execution_params['chunk_delay'],
                dynamic_take_profit=profit_optimization['take_profit'],
                dynamic_stop_loss=profit_optimization['stop_loss'],
                profit_probability=profit_optimization['profit_probability'],
                expected_move_size=profit_optimization['expected_move'],
                confidence_in_analysis=meta_intelligence['confidence'],
                market_predictability=meta_intelligence['predictability'],
                execution_difficulty=meta_intelligence['difficulty'],
                timestamp=time.time()
            )

            # Store intelligence
            self.current_intelligence = intelligence
            self.intelligence_history.append(intelligence)

            # Keep history manageable
            if len(self.intelligence_history) > 1000:
                self.intelligence_history = self.intelligence_history[-500:]

            processing_time = (time.time() - start_time) * 1000
            logger.info(f"🧠 Market intelligence analysis complete in {processing_time:.1f}ms")
            logger.info(f"   Regime: {regime.value}, Liquidity: {liquidity_analysis['level'].value}")
            logger.info(f"   Recommended size: {execution_params['position_size']:.2f}")
            logger.info(f"   Profit probability: {profit_optimization['profit_probability']:.1%}")

            return intelligence

        except Exception as e:
            logger.error(f"❌ Error in market intelligence analysis: {e}")
            # Return conservative default intelligence
            return self._get_conservative_intelligence()

    async def _analyze_market_regime(self, market_data: Dict[str, Any],
                                   recent_trades: List[Dict[str, Any]]) -> MarketRegime:
        """Analyze current market regime for autonomous decision making."""
        try:
            # Get price data
            current_price = market_data.get('last_price', 0)
            price_change = market_data.get('price_change_1m', 0)
            volatility = market_data.get('volatility', 0)

            # Analyze price momentum
            if abs(price_change) > volatility * 2:
                if price_change > 0:
                    return MarketRegime.TRENDING_UP
                else:
                    return MarketRegime.TRENDING_DOWN

            # Check volatility levels
            if volatility > 0.02:  # 2% volatility threshold
                return MarketRegime.HIGH_VOLATILITY
            elif volatility < 0.005:  # 0.5% volatility threshold
                return MarketRegime.LOW_VOLATILITY

            # Default to sideways if no clear trend
            return MarketRegime.SIDEWAYS

        except Exception as e:
            logger.error(f"Error analyzing market regime: {e}")
            return MarketRegime.SIDEWAYS

    async def _analyze_liquidity(self, order_book: Dict[str, Any],
                               recent_trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze market liquidity and order book dynamics."""
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])

            if not bids or not asks:
                return {
                    'level': LiquidityLevel.LOW,
                    'imbalance': 0.0,
                    'spread': 0.001
                }

            # Calculate bid-ask spread
            best_bid = float(bids[0][0]) if bids else 0
            best_ask = float(asks[0][0]) if asks else 0
            spread = (best_ask - best_bid) / best_bid if best_bid > 0 else 0.001

            # Calculate order book depth
            bid_depth = sum(float(bid[1]) for bid in bids[:10])
            ask_depth = sum(float(ask[1]) for ask in asks[:10])
            total_depth = bid_depth + ask_depth

            # Calculate order book imbalance
            imbalance = (bid_depth - ask_depth) / (bid_depth + ask_depth) if total_depth > 0 else 0

            # Determine liquidity level
            if total_depth > 100000:  # High liquidity threshold
                liquidity_level = LiquidityLevel.VERY_HIGH
            elif total_depth > 50000:
                liquidity_level = LiquidityLevel.HIGH
            elif total_depth > 10000:
                liquidity_level = LiquidityLevel.MEDIUM
            elif total_depth > 1000:
                liquidity_level = LiquidityLevel.LOW
            else:
                liquidity_level = LiquidityLevel.VERY_LOW

            return {
                'level': liquidity_level,
                'imbalance': imbalance,
                'spread': spread,
                'depth': total_depth
            }

        except Exception as e:
            logger.error(f"Error analyzing liquidity: {e}")
            return {
                'level': LiquidityLevel.MEDIUM,
                'imbalance': 0.0,
                'spread': 0.001
            }

    async def _analyze_trading_behavior(self, recent_trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze trading behavior patterns to blend in with market participants."""
        try:
            if not recent_trades:
                return self._get_default_behavior_analysis()

            # Extract trade sizes
            trade_sizes = []
            for trade in recent_trades[-100:]:  # Last 100 trades
                size = trade.get('size', 0) or trade.get('amount', 0) or trade.get('quantity', 0)
                if size > 0:
                    trade_sizes.append(float(size))

            if not trade_sizes:
                return self._get_default_behavior_analysis()

            # Calculate statistics
            avg_trade_size = np.mean(trade_sizes)
            median_trade_size = np.median(trade_sizes)
            std_trade_size = np.std(trade_sizes)

            # Find dominant trade size range (where 60% of trades occur)
            percentile_20 = np.percentile(trade_sizes, 20)
            percentile_80 = np.percentile(trade_sizes, 80)
            dominant_range = (percentile_20, percentile_80)

            # Analyze institutional vs retail activity
            large_trades = [size for size in trade_sizes if size > avg_trade_size + 2 * std_trade_size]
            small_trades = [size for size in trade_sizes if size < avg_trade_size - std_trade_size]

            institutional_score = len(large_trades) / len(trade_sizes) if trade_sizes else 0
            retail_score = len(small_trades) / len(trade_sizes) if trade_sizes else 0

            # Create volume profile
            volume_profile = {
                'small': len([s for s in trade_sizes if s < percentile_20]) / len(trade_sizes),
                'medium': len([s for s in trade_sizes if percentile_20 <= s <= percentile_80]) / len(trade_sizes),
                'large': len([s for s in trade_sizes if s > percentile_80]) / len(trade_sizes)
            }

            logger.debug(f"📊 Trading behavior analysis: avg_size={avg_trade_size:.2f}, "
                        f"institutional={institutional_score:.2%}, retail={retail_score:.2%}")

            return {
                'avg_trade_size': avg_trade_size,
                'median_trade_size': median_trade_size,
                'dominant_range': dominant_range,
                'institutional_score': institutional_score,
                'retail_score': retail_score,
                'volume_profile': volume_profile,
                'trade_count': len(trade_sizes)
            }

        except Exception as e:
            logger.error(f"Error analyzing trading behavior: {e}")
            return self._get_default_behavior_analysis()

    def _get_default_behavior_analysis(self) -> Dict[str, Any]:
        """Get default behavior analysis when no trade data is available."""
        return {
            'avg_trade_size': 100.0,
            'median_trade_size': 50.0,
            'dominant_range': (20.0, 200.0),
            'institutional_score': 0.1,
            'retail_score': 0.7,
            'volume_profile': {'small': 0.6, 'medium': 0.3, 'large': 0.1},
            'trade_count': 0
        }

    async def _calculate_optimal_execution(self, market_data: Dict[str, Any],
                                         order_book: Dict[str, Any],
                                         behavior_analysis: Dict[str, Any],
                                         account_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate optimal execution parameters for stealth trading."""
        try:
            # Get account information
            account_balance = account_data.get('available_balance', 100.0)
            leverage = account_data.get('leverage', 1.0)

            # Base position size calculation (2-5% of account)
            base_risk_pct = 0.03  # 3% base risk
            base_position_value = account_balance * base_risk_pct * leverage

            # Adjust based on market behavior
            avg_trade_size = behavior_analysis['avg_trade_size']
            dominant_range = behavior_analysis['dominant_range']

            # Calculate stealth position size (blend in with dominant range)
            if avg_trade_size > 0:
                # Target the median of the dominant range to blend in
                target_size = (dominant_range[0] + dominant_range[1]) / 2

                # But don't exceed our risk limits
                max_size_by_risk = base_position_value / market_data.get('last_price', 1.0)
                recommended_size = min(target_size, max_size_by_risk)
            else:
                recommended_size = base_position_value / market_data.get('last_price', 1.0)

            # CRITICAL: Ensure minimum position size for HTX (minimum 1 DOGE)
            min_position_size = 1.0  # HTX minimum for DOGE/USDT:USDT
            recommended_size = max(recommended_size, min_position_size)

            # Determine if stealth execution is needed
            large_trade_threshold = behavior_analysis['dominant_range'][1] * 2
            stealth_required = recommended_size > large_trade_threshold

            # Calculate chunking strategy for large orders
            if stealth_required:
                chunk_size = behavior_analysis['dominant_range'][1] * 0.8  # Stay below large threshold
                entry_chunks = max(2, int(recommended_size / chunk_size))
                time_between_chunks = 30.0  # 30 seconds between chunks
            else:
                entry_chunks = 1
                time_between_chunks = 0.0

            logger.debug(f"💡 Execution optimization: size={recommended_size:.2f}, "
                        f"stealth={stealth_required}, chunks={entry_chunks}")

            return {
                'position_size': recommended_size,
                'stealth_required': stealth_required,
                'entry_chunks': entry_chunks,
                'chunk_delay': time_between_chunks,
                'target_blend_size': (dominant_range[0] + dominant_range[1]) / 2
            }

        except Exception as e:
            logger.error(f"Error calculating optimal execution: {e}")
            return {
                'position_size': 50.0,  # Conservative default
                'stealth_required': False,
                'entry_chunks': 1,
                'chunk_delay': 0.0,
                'target_blend_size': 100.0
            }

    async def _optimize_profit_targets(self, regime: MarketRegime,
                                     liquidity_analysis: Dict[str, Any],
                                     market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize profit targets based on market conditions and trading style."""
        try:
            current_price = market_data.get('last_price', 1.0)
            volatility = market_data.get('volatility', 0.01)

            # Determine trading style based on market conditions
            trading_style = self._determine_trading_style(regime, volatility, liquidity_analysis)

            # Base profit targets based on trading style
            if trading_style == 'scalping':
                base_take_profit = 0.005  # 0.5% for scalping
                base_stop_loss = 0.01     # 1.0% stop loss
            elif trading_style == 'swing':
                base_take_profit = 0.02   # 2% for swing trading
                base_stop_loss = 0.015    # 1.5% stop loss
            else:  # investor/position trading
                base_take_profit = 0.05   # 5% for position trading
                base_stop_loss = 0.03     # 3% stop loss

            # Adjust based on market regime
            regime_multipliers = {
                MarketRegime.TRENDING_UP: {'tp': 1.5, 'sl': 0.8},
                MarketRegime.TRENDING_DOWN: {'tp': 1.5, 'sl': 0.8},
                MarketRegime.HIGH_VOLATILITY: {'tp': 2.0, 'sl': 1.2},
                MarketRegime.LOW_VOLATILITY: {'tp': 0.8, 'sl': 0.6},
                MarketRegime.BREAKOUT: {'tp': 2.5, 'sl': 1.0},
                MarketRegime.REVERSAL: {'tp': 1.2, 'sl': 1.5},
                MarketRegime.SIDEWAYS: {'tp': 1.0, 'sl': 1.0}
            }

            multiplier = regime_multipliers.get(regime, {'tp': 1.0, 'sl': 1.0})

            # Adjust based on volatility
            volatility_factor = min(2.0, max(0.5, volatility / 0.01))  # Normalize to 1% volatility

            # Calculate dynamic targets
            dynamic_take_profit = base_take_profit * multiplier['tp'] * volatility_factor
            dynamic_stop_loss = base_stop_loss * multiplier['sl'] * volatility_factor

            # Adjust based on liquidity
            liquidity_level = liquidity_analysis['level']
            if liquidity_level in [LiquidityLevel.LOW, LiquidityLevel.VERY_LOW]:
                # Tighter targets in low liquidity
                dynamic_take_profit *= 0.8
                dynamic_stop_loss *= 1.2

            # Calculate profit probability based on market conditions
            base_probability = 0.55  # 55% base win rate

            # Adjust probability based on regime
            regime_prob_adjustments = {
                MarketRegime.TRENDING_UP: 0.1,
                MarketRegime.TRENDING_DOWN: 0.1,
                MarketRegime.HIGH_VOLATILITY: -0.05,
                MarketRegime.LOW_VOLATILITY: 0.05,
                MarketRegime.BREAKOUT: 0.15,
                MarketRegime.REVERSAL: -0.1,
                MarketRegime.SIDEWAYS: 0.0
            }

            profit_probability = base_probability + regime_prob_adjustments.get(regime, 0.0)
            profit_probability = max(0.3, min(0.8, profit_probability))  # Clamp between 30-80%

            # Expected move size
            expected_move = volatility * 1.5  # 1.5x daily volatility

            logger.debug(f"🎯 Profit optimization ({trading_style}): TP={dynamic_take_profit:.1%}, "
                        f"SL={dynamic_stop_loss:.1%}, prob={profit_probability:.1%}")

            return {
                'take_profit': dynamic_take_profit,
                'stop_loss': dynamic_stop_loss,
                'profit_probability': profit_probability,
                'expected_move': expected_move,
                'trading_style': trading_style
            }

        except Exception as e:
            logger.error(f"Error optimizing profit targets: {e}")
            return {
                'take_profit': 0.005,  # Default to scalping
                'stop_loss': 0.01,
                'profit_probability': 0.55,
                'expected_move': 0.015
            }

    def _determine_trading_style(self, regime: MarketRegime, volatility: float,
                               liquidity_analysis: Dict[str, Any]) -> str:
        """Determine optimal trading style based on market conditions."""
        try:
            # High volatility + high liquidity = scalping opportunities
            if (volatility > 0.015 and  # 1.5% volatility
                liquidity_analysis['level'] in [LiquidityLevel.HIGH, LiquidityLevel.VERY_HIGH]):
                return 'scalping'

            # Medium volatility + trending market = swing trading
            elif (0.005 < volatility <= 0.015 and
                  regime in [MarketRegime.TRENDING_UP, MarketRegime.TRENDING_DOWN]):
                return 'swing'

            # Low volatility or sideways = position trading
            elif (volatility <= 0.005 or
                  regime in [MarketRegime.SIDEWAYS, MarketRegime.LOW_VOLATILITY]):
                return 'investor'

            # Default to scalping for crypto futures (fast-moving markets)
            return 'scalping'

        except Exception as e:
            logger.error(f"Error determining trading style: {e}")
            return 'scalping'  # Default to scalping for crypto

    async def _generate_meta_intelligence(self, regime: MarketRegime,
                                        liquidity_analysis: Dict[str, Any],
                                        behavior_analysis: Dict[str, Any],
                                        market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate meta-intelligence about the quality of our analysis."""
        try:
            # Base confidence
            confidence = 0.7

            # Adjust confidence based on data quality
            trade_count = behavior_analysis.get('trade_count', 0)
            if trade_count > 50:
                confidence += 0.1
            elif trade_count < 10:
                confidence -= 0.2

            # Adjust based on liquidity
            liquidity_level = liquidity_analysis['level']
            if liquidity_level in [LiquidityLevel.HIGH, LiquidityLevel.VERY_HIGH]:
                confidence += 0.1
            elif liquidity_level in [LiquidityLevel.LOW, LiquidityLevel.VERY_LOW]:
                confidence -= 0.15

            # Market predictability based on regime
            predictability_scores = {
                MarketRegime.TRENDING_UP: 0.8,
                MarketRegime.TRENDING_DOWN: 0.8,
                MarketRegime.BREAKOUT: 0.9,
                MarketRegime.HIGH_VOLATILITY: 0.4,
                MarketRegime.LOW_VOLATILITY: 0.7,
                MarketRegime.REVERSAL: 0.3,
                MarketRegime.SIDEWAYS: 0.5
            }

            predictability = predictability_scores.get(regime, 0.6)

            # Execution difficulty
            spread = liquidity_analysis.get('spread', 0.001)
            if spread > 0.005:  # 0.5% spread
                difficulty = 0.8  # High difficulty
            elif spread > 0.002:  # 0.2% spread
                difficulty = 0.5  # Medium difficulty
            else:
                difficulty = 0.2  # Low difficulty

            # Clamp values
            confidence = max(0.1, min(0.95, confidence))
            predictability = max(0.1, min(0.95, predictability))
            difficulty = max(0.1, min(0.95, difficulty))

            return {
                'confidence': confidence,
                'predictability': predictability,
                'difficulty': difficulty
            }

        except Exception as e:
            logger.error(f"Error generating meta-intelligence: {e}")
            return {
                'confidence': 0.5,
                'predictability': 0.5,
                'difficulty': 0.5
            }

    def _get_conservative_intelligence(self) -> MarketIntelligence:
        """Get conservative default intelligence when analysis fails."""
        return MarketIntelligence(
            regime=MarketRegime.SIDEWAYS,
            liquidity_level=LiquidityLevel.MEDIUM,
            order_book_imbalance=0.0,
            recent_volume_profile={'small': 0.6, 'medium': 0.3, 'large': 0.1},
            average_trade_size=100.0,
            dominant_trade_size_range=(50.0, 200.0),
            institutional_activity_score=0.1,
            retail_activity_score=0.7,
            recommended_position_size=50.0,
            stealth_execution_required=False,
            optimal_entry_chunks=1,
            time_between_chunks=0.0,
            dynamic_take_profit=0.015,
            dynamic_stop_loss=0.01,
            profit_probability=0.5,
            expected_move_size=0.01,
            confidence_in_analysis=0.3,
            market_predictability=0.4,
            execution_difficulty=0.5,
            timestamp=time.time()
        )

    def get_autonomous_trading_decision(self,
                                      llm_decision: Dict[str, Any],
                                      model_outputs: Dict[str, Any],
                                      account_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate autonomous trading decision based on market intelligence.

        This is the final decision-making function that combines:
        - LLM reasoning
        - Model ensemble outputs
        - Market intelligence analysis
        - Account optimization

        Returns a complete autonomous trading decision.
        """
        try:
            if not self.current_intelligence:
                logger.warning("⚠️ No market intelligence available, using conservative approach")
                return self._get_conservative_trading_decision(llm_decision)

            intelligence = self.current_intelligence

            # Extract base decision
            base_action = llm_decision.get('action', 'WAIT')
            base_confidence = llm_decision.get('confidence', 0.5)

            # Enhance decision with market intelligence
            enhanced_decision = {
                'action': base_action,
                'confidence': base_confidence,
                'reasoning': llm_decision.get('reasoning', 'AI analysis'),

                # Market intelligence enhancements
                'market_regime': intelligence.regime.value,
                'liquidity_assessment': intelligence.liquidity_level.value,
                'execution_strategy': 'stealth' if intelligence.stealth_execution_required else 'normal',

                # Optimized execution parameters
                'position_size': intelligence.recommended_position_size,
                'entry_chunks': intelligence.optimal_entry_chunks,
                'chunk_delay': intelligence.time_between_chunks,

                # Dynamic profit targets
                'take_profit_pct': intelligence.dynamic_take_profit * 100,
                'stop_loss_pct': intelligence.dynamic_stop_loss * 100,
                'profit_probability': intelligence.profit_probability,

                # Meta-intelligence
                'analysis_confidence': intelligence.confidence_in_analysis,
                'market_predictability': intelligence.market_predictability,
                'execution_difficulty': intelligence.execution_difficulty,

                # Autonomous decision metadata
                'autonomous_decision': True,
                'decision_quality': self._assess_decision_quality(intelligence, base_confidence),
                'expected_profit': self._calculate_expected_profit(intelligence, account_data),
                'risk_reward_ratio': intelligence.dynamic_take_profit / intelligence.dynamic_stop_loss,

                'timestamp': time.time()
            }

            # Track autonomous decision
            self.autonomous_decisions += 1

            logger.info(f"🤖 Autonomous trading decision generated:")
            logger.info(f"   Action: {enhanced_decision['action']}")
            logger.info(f"   Position size: {enhanced_decision['position_size']:.2f}")
            logger.info(f"   TP: {enhanced_decision['take_profit_pct']:.1f}% | SL: {enhanced_decision['stop_loss_pct']:.1f}%")
            logger.info(f"   Profit probability: {enhanced_decision['profit_probability']:.1%}")
            logger.info(f"   Decision quality: {enhanced_decision['decision_quality']:.1%}")

            return enhanced_decision

        except Exception as e:
            logger.error(f"❌ Error generating autonomous trading decision: {e}")
            return self._get_conservative_trading_decision(llm_decision)

    def _assess_decision_quality(self, intelligence: MarketIntelligence, base_confidence: float) -> float:
        """Assess the quality of the autonomous trading decision."""
        try:
            # Combine multiple quality factors
            quality_factors = [
                intelligence.confidence_in_analysis,
                intelligence.market_predictability,
                1.0 - intelligence.execution_difficulty,  # Lower difficulty = higher quality
                base_confidence,
                intelligence.profit_probability
            ]

            # Weighted average
            weights = [0.25, 0.25, 0.15, 0.20, 0.15]
            quality_score = sum(factor * weight for factor, weight in zip(quality_factors, weights))

            return max(0.1, min(0.95, quality_score))

        except Exception as e:
            logger.error(f"Error assessing decision quality: {e}")
            return 0.5

    def _calculate_expected_profit(self, intelligence: MarketIntelligence, account_data: Dict[str, Any]) -> float:
        """Calculate expected profit from the autonomous decision."""
        try:
            position_size = intelligence.recommended_position_size
            take_profit = intelligence.dynamic_take_profit
            profit_probability = intelligence.profit_probability

            # Expected profit = position_size * take_profit * probability
            expected_profit = position_size * take_profit * profit_probability

            return expected_profit

        except Exception as e:
            logger.error(f"Error calculating expected profit: {e}")
            return 0.0

    def _get_conservative_trading_decision(self, llm_decision: Dict[str, Any]) -> Dict[str, Any]:
        """Get conservative trading decision when intelligence is unavailable."""
        return {
            'action': 'WAIT',  # Conservative default
            'confidence': 0.3,
            'reasoning': 'Conservative approach - insufficient market intelligence',
            'position_size': 25.0,  # Small conservative size
            'take_profit_pct': 1.5,
            'stop_loss_pct': 1.0,
            'autonomous_decision': True,
            'decision_quality': 0.3,
            'timestamp': time.time()
        }

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary of autonomous intelligence system."""
        try:
            success_rate = (self.successful_predictions / self.autonomous_decisions
                          if self.autonomous_decisions > 0 else 0.0)

            return {
                'autonomous_decisions': self.autonomous_decisions,
                'successful_predictions': self.successful_predictions,
                'success_rate': success_rate,
                'total_profit_generated': self.total_profit_generated,
                'intelligence_history_count': len(self.intelligence_history),
                'current_intelligence_available': self.current_intelligence is not None,
                'last_analysis_time': (self.current_intelligence.timestamp
                                     if self.current_intelligence else 0)
            }

        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return {
                'autonomous_decisions': 0,
                'successful_predictions': 0,
                'success_rate': 0.0,
                'total_profit_generated': 0.0
            }
