#!/usr/bin/env python3
"""
Test Chart Data - Verify that chart data is being generated correctly
"""

import asyncio
import json
import logging
import time
import requests

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_api_data():
    """Test the API data endpoint to see what chart data is being sent."""
    try:
        # Test the API endpoint
        response = requests.get('http://localhost:8086/api/data')
        
        if response.status_code == 200:
            data = response.json()
            
            logger.info("=== API DATA RESPONSE ===")
            logger.info(f"Status Code: {response.status_code}")
            logger.info(f"Response Keys: {list(data.keys())}")
            
            # Check for price data
            if 'price_data' in data:
                price_data = data['price_data']
                logger.info(f"Price Data Length: {len(price_data)}")
                if price_data:
                    logger.info(f"Sample Price Data: {price_data[:3]}")
                else:
                    logger.warning("Price data is empty!")
            else:
                logger.warning("No price_data key found in response!")
            
            # Check for signals
            if 'recent_signals' in data:
                signals = data['recent_signals']
                logger.info(f"Recent Signals Length: {len(signals)}")
                if signals:
                    logger.info(f"Sample Signal: {signals[0]}")
                else:
                    logger.info("No recent signals")
            
            # Check features
            if 'features' in data:
                features = data['features']
                logger.info(f"Features: {features}")
            
            # Pretty print the full response for debugging
            logger.info("=== FULL RESPONSE ===")
            print(json.dumps(data, indent=2, default=str))
            
        else:
            logger.error(f"API request failed with status: {response.status_code}")
            logger.error(f"Response: {response.text}")
            
    except Exception as e:
        logger.error(f"Error testing API: {e}")

async def test_websocket_data():
    """Test WebSocket connection to see real-time data."""
    try:
        import websockets
        
        uri = "ws://localhost:8086/ws"
        logger.info(f"Connecting to WebSocket: {uri}")
        
        async with websockets.connect(uri) as websocket:
            logger.info("WebSocket connected successfully")
            
            # Listen for a few messages
            for i in range(3):
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                    data = json.loads(message)
                    
                    logger.info(f"=== WEBSOCKET MESSAGE {i+1} ===")
                    logger.info(f"Message Keys: {list(data.keys())}")
                    
                    if 'price_data' in data:
                        price_data = data['price_data']
                        logger.info(f"WebSocket Price Data Length: {len(price_data)}")
                    
                    if 'recent_signals' in data:
                        signals = data['recent_signals']
                        logger.info(f"WebSocket Signals Length: {len(signals)}")
                    
                except asyncio.TimeoutError:
                    logger.warning(f"Timeout waiting for WebSocket message {i+1}")
                    break
                    
    except Exception as e:
        logger.error(f"WebSocket test failed: {e}")

def test_chart_initialization():
    """Test if Chart.js is properly initialized."""
    logger.info("=== CHART INITIALIZATION TEST ===")
    
    # Check if the dashboard is accessible
    try:
        response = requests.get('http://localhost:8086/')
        if response.status_code == 200:
            logger.info("✅ Dashboard is accessible")
            
            # Check if Chart.js is included
            if 'chart.js' in response.text.lower():
                logger.info("✅ Chart.js is included in the page")
            else:
                logger.warning("⚠️ Chart.js not found in page")
            
            # Check if our chart canvas exists
            if 'signal-chart' in response.text:
                logger.info("✅ Signal chart canvas found")
            else:
                logger.warning("⚠️ Signal chart canvas not found")
                
        else:
            logger.error(f"❌ Dashboard not accessible: {response.status_code}")
            
    except Exception as e:
        logger.error(f"❌ Error testing dashboard: {e}")

async def main():
    """Run all tests."""
    logger.info("🧪 Starting Chart Data Tests...")
    logger.info("=" * 50)
    
    # Test 1: Chart initialization
    test_chart_initialization()
    
    # Test 2: API data
    await test_api_data()
    
    # Test 3: WebSocket data (optional, requires websockets package)
    try:
        await test_websocket_data()
    except ImportError:
        logger.warning("⚠️ websockets package not available, skipping WebSocket test")
    except Exception as e:
        logger.error(f"❌ WebSocket test failed: {e}")
    
    logger.info("=" * 50)
    logger.info("🏁 Chart Data Tests Complete")

if __name__ == "__main__":
    asyncio.run(main())
