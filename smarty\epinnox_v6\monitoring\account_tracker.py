#!/usr/bin/env python3
"""
Live Account Tracker - Phase 8
Real-time HTX account monitoring for DOGE/USDT:USDT futures trading
"""

import logging
import time
import asyncio
import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class AccountSnapshot:
    """Real-time account snapshot."""
    timestamp: float
    symbol: str

    # Account Balance
    total_balance: float
    available_balance: float
    margin_used: float
    margin_used_pct: float

    # Position Information
    open_positions: int
    position_size: float
    position_direction: str  # 'long', 'short', 'none'
    entry_price: float
    current_price: float
    unrealized_pnl: float
    unrealized_pnl_pct: float

    # Risk Metrics
    leverage: float
    liquidation_price: float
    liquidation_buffer: float
    liquidation_buffer_pct: float

    # Trading Status
    can_trade: bool
    risk_level: str  # 'safe', 'moderate', 'high', 'critical'
    warnings: List[str]

class LiveAccountTracker:
    """
    Live Account Tracker for HTX USDT-M Futures
    Monitors account status, positions, and risk metrics in real-time
    """

    def __init__(self, config: Dict[str, Any], htx_client=None):
        self.config = config
        self.htx_client = htx_client  # This will be CCXTHTXClient

        # Configuration
        trading_symbol_env = os.getenv('TRADING_SYMBOL', 'DOGE-USDT')
        # Convert to CCXT format if needed: DOGE-USDT -> DOGE/USDT:USDT
        if '-' in trading_symbol_env and '/' not in trading_symbol_env:
            base, quote = trading_symbol_env.split('-')
            self.symbol = f"{base}/{quote}:{quote}"  # DOGE/USDT:USDT for futures
        else:
            self.symbol = trading_symbol_env
        self.account_balance = float(os.getenv('ACCOUNT_BALANCE', '5.0'))
        self.max_position_size = float(os.getenv('MAX_POSITION_SIZE', '4.0'))
        self.leverage = float(os.getenv('LEVERAGE', '20'))

        # Safety thresholds
        self.margin_usage_limit = float(os.getenv('MARGIN_USAGE_LIMIT', '90'))
        self.liquidation_buffer_min = float(os.getenv('LIQUIDATION_BUFFER_MIN', '2.5'))
        self.max_open_positions = int(os.getenv('MAX_OPEN_POSITIONS', '1'))

        # State tracking
        self.current_snapshot: Optional[AccountSnapshot] = None
        self.snapshot_history: List[AccountSnapshot] = []
        self.last_update_time = 0
        self.update_interval = 2.0  # STANDARDIZED: Update every 2 seconds

        # Monitoring task
        self.monitoring_task = None
        self.monitoring_enabled = False

        logger.info(f"🏦 Live Account Tracker initialized for {self.symbol}")
        logger.info(f"   Account Balance: ${self.account_balance}")
        logger.info(f"   Max Position: ${self.max_position_size}")
        logger.info(f"   Leverage: {self.leverage}x")

    async def start_monitoring(self):
        """Start real-time account monitoring."""
        try:
            if self.monitoring_task and not self.monitoring_task.done():
                logger.warning("Account monitoring already running")
                return

            self.monitoring_enabled = True
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            logger.info("🔄 Live account monitoring started")

        except Exception as e:
            logger.error(f"Error starting account monitoring: {e}")

    async def stop_monitoring(self):
        """Stop account monitoring."""
        try:
            self.monitoring_enabled = False
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
                self.monitoring_task = None

            logger.info("🛑 Live account monitoring stopped")

        except Exception as e:
            logger.error(f"Error stopping account monitoring: {e}")

    async def _monitoring_loop(self):
        """Main monitoring loop."""
        try:
            while self.monitoring_enabled:
                await self._update_account_snapshot()
                await asyncio.sleep(self.update_interval)

        except asyncio.CancelledError:
            logger.info("Account monitoring loop cancelled")
        except Exception as e:
            logger.error(f"Error in account monitoring loop: {e}")

    async def _update_account_snapshot(self):
        """Update account snapshot with latest data using CCXT."""
        try:
            if not self.htx_client or not hasattr(self.htx_client, 'is_connected'):
                # Create mock snapshot for testing
                self.current_snapshot = self._create_mock_snapshot()
                return

            # Ensure client is connected
            if not self.htx_client.is_connected:
                connected = await self.htx_client.connect()
                if not connected:
                    logger.warning("Failed to connect to HTX - using mock data")
                    self.current_snapshot = self._create_mock_snapshot()
                    return

            # Get account balance using CCXT
            account_balance = await self.htx_client.get_account_balance()

            # Get positions using CCXT
            positions = await self.htx_client.get_positions(self.symbol)

            if account_balance is None:
                logger.warning("Failed to get account balance - using mock data")
                self.current_snapshot = self._create_mock_snapshot()
                return

            # Parse CCXT data into snapshot
            snapshot = self._parse_ccxt_data(account_balance, positions)

            # Update current snapshot
            self.current_snapshot = snapshot

            # Add to history
            self.snapshot_history.append(snapshot)
            if len(self.snapshot_history) > 1000:  # Keep last 1000 snapshots
                self.snapshot_history = self.snapshot_history[-1000:]

            self.last_update_time = time.time()

            # Log critical warnings
            if snapshot.warnings:
                for warning in snapshot.warnings:
                    logger.warning(f"⚠️ {warning}")

            # Log risk level changes
            if len(self.snapshot_history) > 1:
                prev_risk = self.snapshot_history[-2].risk_level
                if snapshot.risk_level != prev_risk:
                    logger.info(f"🎯 Risk level changed: {prev_risk} → {snapshot.risk_level}")

        except Exception as e:
            logger.error(f"Error updating account snapshot: {e}")
            # Fallback to mock data
            self.current_snapshot = self._create_mock_snapshot()

    def _parse_account_data(self, account_info: Dict[str, Any],
                           position_info: Optional[Dict[str, Any]]) -> AccountSnapshot:
        """Parse HTX account data into snapshot."""
        try:
            # Parse account balance
            account_data = account_info[0] if account_info else {}
            total_balance = float(account_data.get('margin_balance', self.account_balance))
            available_balance = float(account_data.get('margin_available', total_balance))
            margin_used = total_balance - available_balance
            margin_used_pct = (margin_used / total_balance * 100) if total_balance > 0 else 0

            # Parse position data
            position_data = position_info[0] if position_info and len(position_info) > 0 else {}
            open_positions = 1 if position_data.get('volume', 0) > 0 else 0
            position_size = float(position_data.get('volume', 0))
            position_direction = 'long' if position_data.get('direction') == 'buy' else 'short' if position_data.get('direction') == 'sell' else 'none'
            entry_price = float(position_data.get('cost_open', 0))
            unrealized_pnl = float(position_data.get('profit_unreal', 0))

            # Calculate current price (mock for now)
            current_price = entry_price * (1 + (unrealized_pnl / (position_size * entry_price))) if position_size > 0 and entry_price > 0 else 0
            unrealized_pnl_pct = (unrealized_pnl / (position_size * entry_price) * 100) if position_size > 0 and entry_price > 0 else 0

            # Calculate liquidation metrics
            liquidation_price = float(position_data.get('liquidation_price', 0))
            liquidation_buffer = abs(current_price - liquidation_price) if liquidation_price > 0 and current_price > 0 else 0
            liquidation_buffer_pct = (liquidation_buffer / current_price * 100) if current_price > 0 else 100

            # Assess risk and trading capability
            can_trade, risk_level, warnings = self._assess_trading_status(
                margin_used_pct, liquidation_buffer_pct, open_positions, unrealized_pnl
            )

            return AccountSnapshot(
                timestamp=time.time(),
                symbol=self.symbol,
                total_balance=total_balance,
                available_balance=available_balance,
                margin_used=margin_used,
                margin_used_pct=margin_used_pct,
                open_positions=open_positions,
                position_size=position_size,
                position_direction=position_direction,
                entry_price=entry_price,
                current_price=current_price,
                unrealized_pnl=unrealized_pnl,
                unrealized_pnl_pct=unrealized_pnl_pct,
                leverage=self.leverage,
                liquidation_price=liquidation_price,
                liquidation_buffer=liquidation_buffer,
                liquidation_buffer_pct=liquidation_buffer_pct,
                can_trade=can_trade,
                risk_level=risk_level,
                warnings=warnings
            )

        except Exception as e:
            logger.error(f"Error parsing account data: {e}")
            return self._create_mock_snapshot()

    def _parse_ccxt_data(self, account_balance: Dict[str, Any],
                        positions: Optional[List[Dict[str, Any]]]) -> AccountSnapshot:
        """Parse CCXT account data into snapshot."""
        try:
            # Parse USDT balance from CCXT
            usdt_balance = account_balance.get('USDT', {})
            total_balance = float(usdt_balance.get('total', self.account_balance))
            free_balance = float(usdt_balance.get('free', total_balance))
            used_balance = float(usdt_balance.get('used', 0))

            # Calculate margin usage
            margin_used = used_balance
            margin_used_pct = (margin_used / total_balance * 100) if total_balance > 0 else 0

            # Parse position data
            open_positions = 0
            position_size = 0.0
            position_direction = 'none'
            entry_price = 0.0
            current_price = 0.0
            unrealized_pnl = 0.0
            unrealized_pnl_pct = 0.0
            liquidation_price = 0.0

            if positions and len(positions) > 0:
                # Find position for our symbol
                symbol_position = None
                for pos in positions:
                    if pos.get('symbol') == self.symbol:
                        symbol_position = pos
                        break

                if symbol_position and float(symbol_position.get('size', 0)) > 0:
                    open_positions = 1
                    position_size = float(symbol_position.get('size', 0))
                    position_direction = 'long' if symbol_position.get('side') == 'long' else 'short'
                    entry_price = float(symbol_position.get('entryPrice', 0))
                    current_price = float(symbol_position.get('markPrice', entry_price))
                    unrealized_pnl = float(symbol_position.get('unrealizedPnl', 0))
                    liquidation_price = float(symbol_position.get('liquidationPrice', 0))

                    # Calculate unrealized PnL percentage
                    if position_size > 0 and entry_price > 0:
                        unrealized_pnl_pct = (unrealized_pnl / (position_size * entry_price) * 100)

            # Calculate liquidation buffer
            liquidation_buffer = 0.0
            liquidation_buffer_pct = 100.0

            if liquidation_price > 0 and current_price > 0:
                liquidation_buffer = abs(current_price - liquidation_price)
                liquidation_buffer_pct = (liquidation_buffer / current_price * 100)

            # Assess risk and trading capability
            can_trade, risk_level, warnings = self._assess_trading_status(
                margin_used_pct, liquidation_buffer_pct, open_positions, unrealized_pnl
            )

            return AccountSnapshot(
                timestamp=time.time(),
                symbol=self.symbol,
                total_balance=total_balance,
                available_balance=free_balance,
                margin_used=margin_used,
                margin_used_pct=margin_used_pct,
                open_positions=open_positions,
                position_size=position_size,
                position_direction=position_direction,
                entry_price=entry_price,
                current_price=current_price,
                unrealized_pnl=unrealized_pnl,
                unrealized_pnl_pct=unrealized_pnl_pct,
                leverage=self.leverage,
                liquidation_price=liquidation_price,
                liquidation_buffer=liquidation_buffer,
                liquidation_buffer_pct=liquidation_buffer_pct,
                can_trade=can_trade,
                risk_level=risk_level,
                warnings=warnings
            )

        except Exception as e:
            logger.error(f"Error parsing CCXT data: {e}")
            return self._create_mock_snapshot()

    def _create_mock_snapshot(self) -> AccountSnapshot:
        """Create mock snapshot for testing."""
        return AccountSnapshot(
            timestamp=time.time(),
            symbol=self.symbol,
            total_balance=self.account_balance,
            available_balance=self.account_balance * 0.8,
            margin_used=self.account_balance * 0.2,
            margin_used_pct=20.0,
            open_positions=0,
            position_size=0.0,
            position_direction='none',
            entry_price=0.0,
            current_price=0.08,  # Mock DOGE price
            unrealized_pnl=0.0,
            unrealized_pnl_pct=0.0,
            leverage=self.leverage,
            liquidation_price=0.0,
            liquidation_buffer=0.0,
            liquidation_buffer_pct=100.0,
            can_trade=True,
            risk_level='safe',
            warnings=[]
        )

    def _assess_trading_status(self, margin_used_pct: float, liquidation_buffer_pct: float,
                              open_positions: int, unrealized_pnl: float) -> tuple:
        """Assess if trading is safe and determine risk level."""
        try:
            warnings = []
            can_trade = True

            # Check margin usage
            if margin_used_pct > self.margin_usage_limit:
                warnings.append(f"Margin usage {margin_used_pct:.1f}% exceeds limit {self.margin_usage_limit}%")
                can_trade = False

            # Check liquidation buffer
            if liquidation_buffer_pct < self.liquidation_buffer_min:
                warnings.append(f"Liquidation buffer {liquidation_buffer_pct:.1f}% below minimum {self.liquidation_buffer_min}%")
                can_trade = False

            # Check position limits
            if open_positions >= self.max_open_positions:
                warnings.append(f"Maximum positions reached: {open_positions}/{self.max_open_positions}")
                can_trade = False

            # Determine risk level
            if margin_used_pct > 80 or liquidation_buffer_pct < 5:
                risk_level = 'critical'
            elif margin_used_pct > 60 or liquidation_buffer_pct < 10:
                risk_level = 'high'
            elif margin_used_pct > 40 or liquidation_buffer_pct < 20:
                risk_level = 'moderate'
            else:
                risk_level = 'safe'

            return can_trade, risk_level, warnings

        except Exception as e:
            logger.error(f"Error assessing trading status: {e}")
            return False, 'critical', ['Error assessing trading status']

    # Public Interface Methods

    def get_current_snapshot(self) -> Optional[AccountSnapshot]:
        """Get current account snapshot."""
        return self.current_snapshot

    def get_account_summary(self) -> Dict[str, Any]:
        """Get account summary for LLM prompt injection."""
        try:
            if not self.current_snapshot:
                return {
                    'balance': self.account_balance,
                    'margin_used_pct': 0,
                    'leverage': self.leverage,
                    'positions_open': 0,
                    'unrealized_pnl': 0,
                    'liquidation_buffer_pct': 100,
                    'can_trade': True,
                    'risk_level': 'safe'
                }

            snapshot = self.current_snapshot
            return {
                'balance': f"${snapshot.total_balance:.2f}",
                'margin_used_pct': f"{snapshot.margin_used_pct:.1f}%",
                'leverage': f"{snapshot.leverage:.0f}x",
                'positions_open': snapshot.open_positions,
                'unrealized_pnl': f"${snapshot.unrealized_pnl:+.2f}",
                'liquidation_buffer_pct': f"{snapshot.liquidation_buffer_pct:.1f}%",
                'can_trade': snapshot.can_trade,
                'risk_level': snapshot.risk_level,
                'warnings': snapshot.warnings
            }

        except Exception as e:
            logger.error(f"Error getting account summary: {e}")
            return {'error': 'Failed to get account summary'}

    def can_place_trade(self, trade_size: float, direction: str) -> tuple:
        """Check if a trade can be placed safely."""
        try:
            if not self.current_snapshot:
                return True, []

            snapshot = self.current_snapshot
            warnings = []

            # Basic trading capability check
            if not snapshot.can_trade:
                return False, snapshot.warnings

            # Check if adding position in same direction
            if (snapshot.open_positions > 0 and
                ((direction.upper() in ['LONG', 'BUY'] and snapshot.position_direction == 'long') or
                 (direction.upper() in ['SHORT', 'SELL'] and snapshot.position_direction == 'short'))):
                warnings.append("Cannot add to existing position in same direction")
                return False, warnings

            # Check trade size vs available balance
            required_margin = trade_size / self.leverage
            if required_margin > snapshot.available_balance:
                warnings.append(f"Insufficient margin: need ${required_margin:.2f}, have ${snapshot.available_balance:.2f}")
                return False, warnings

            # Check if trade would exceed margin limits
            new_margin_used_pct = ((snapshot.margin_used + required_margin) / snapshot.total_balance) * 100
            if new_margin_used_pct > self.margin_usage_limit:
                warnings.append(f"Trade would exceed margin limit: {new_margin_used_pct:.1f}% > {self.margin_usage_limit}%")
                return False, warnings

            return True, warnings

        except Exception as e:
            logger.error(f"Error checking trade capability: {e}")
            return False, ['Error checking trade capability']

    def get_position_info(self) -> Dict[str, Any]:
        """Get current position information."""
        try:
            if not self.current_snapshot:
                return {'has_position': False}

            snapshot = self.current_snapshot

            if snapshot.open_positions == 0:
                return {'has_position': False}

            return {
                'has_position': True,
                'size': snapshot.position_size,
                'direction': snapshot.position_direction,
                'entry_price': snapshot.entry_price,
                'current_price': snapshot.current_price,
                'unrealized_pnl': snapshot.unrealized_pnl,
                'unrealized_pnl_pct': snapshot.unrealized_pnl_pct,
                'liquidation_price': snapshot.liquidation_price,
                'liquidation_buffer_pct': snapshot.liquidation_buffer_pct
            }

        except Exception as e:
            logger.error(f"Error getting position info: {e}")
            return {'has_position': False, 'error': str(e)}

    def get_risk_metrics(self) -> Dict[str, Any]:
        """Get current risk metrics."""
        try:
            if not self.current_snapshot:
                return {
                    'risk_level': 'safe',
                    'margin_used_pct': 0,
                    'liquidation_buffer_pct': 100,
                    'can_trade': True,
                    'warnings': [],
                    'leverage': self.leverage
                }

            snapshot = self.current_snapshot

            return {
                'risk_level': snapshot.risk_level,
                'margin_used_pct': snapshot.margin_used_pct,
                'liquidation_buffer_pct': snapshot.liquidation_buffer_pct,
                'can_trade': snapshot.can_trade,
                'warnings': snapshot.warnings,
                'leverage': snapshot.leverage
            }

        except Exception as e:
            logger.error(f"Error getting risk metrics: {e}")
            return {'risk_level': 'error', 'error': str(e)}

    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get monitoring system status."""
        return {
            'monitoring_enabled': self.monitoring_enabled,
            'last_update': self.last_update_time,
            'update_interval': self.update_interval,
            'snapshots_count': len(self.snapshot_history),
            'current_snapshot_age': time.time() - self.last_update_time if self.last_update_time > 0 else 0
        }
