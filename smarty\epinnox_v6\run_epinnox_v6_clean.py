#!/usr/bin/env python3
"""
Epinnox V6 - Standalone AI Strategy Tuner
Main Application Startup (Clean ASCII Logging Version)
"""

import asyncio
import logging
import signal
import sys
import time
import yaml
import os
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from feeds.htx_ws_client import HTXWebSocketClient
from feeds.binance_ws_client import BinanceWebSocketClient
from feeds.kraken_ws_client import KrakenWebSocketClient
from feeds.trade_parser import TradeParser
from storage.live_store import LiveDataStore
from models.smart_strategy import SmartStrategy
from models.llm_integration import LLMIntegration
from ui.ai_strategy_tuner import AIStrategyTunerDashboard
from utils.system_monitor import SystemMonitor
from analytics.signal_tracker import SignalTracker
from analytics.performance_analyzer import PerformanceAnalyzer

def setup_logging():
    """Setup logging with Windows-compatible ASCII output."""
    # Ensure logs directory exists
    os.makedirs('logs', exist_ok=True)

    # Create formatters
    console_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Console handler (ASCII-safe)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(console_formatter)

    # File handler with UTF-8 encoding for emojis in file logs
    file_handler = logging.FileHandler('logs/epinnox_v6.log', encoding='utf-8')
    file_handler.setFormatter(file_formatter)

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

class EpinnoxV6Application:
    """
    Main application class for Epinnox V6 AI Strategy Tuner.
    Coordinates all components for real-time trading analysis.
    """

    def __init__(self, config_path: str = 'config/strategy.yaml'):
        self.config_path = config_path
        self.config = None
        self.running = False

        # Core components
        self.data_store = None
        self.trade_parser = None
        self.ws_client = None
        self.backup_ws_client = None
        self.kraken_ws_client = None
        self.active_exchange = None
        self.strategy_engine = None
        self.llm_integration = None
        self.dashboard = None
        self.system_monitor = None

        # Analytics components
        self.signal_tracker = None
        self.performance_analyzer = None

        logger.info("[INIT] Epinnox V6 AI Strategy Tuner initializing...")

    async def load_config(self):
        """Load configuration from YAML file."""
        try:
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)

            logger.info(f"[OK] Configuration loaded from {self.config_path}")
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to load configuration: {e}")
            return False

    async def initialize_components(self):
        """Initialize all application components."""
        try:
            logger.info("[SETUP] Initializing components...")

            # Initialize data store
            self.data_store = LiveDataStore(self.config)
            await self.data_store.start_background_tasks()
            logger.info("[OK] Data store initialized")

            # Initialize analytics components
            self.signal_tracker = SignalTracker(self.config)
            self.performance_analyzer = PerformanceAnalyzer(self.signal_tracker)
            logger.info("[OK] Signal tracking and performance analytics initialized")

            # Initialize trade parser
            self.trade_parser = TradeParser(self.config)
            logger.info("[OK] Trade parser initialized")

            # Initialize WebSocket clients (HTX primary, Binance + Kraken backups)
            self.ws_client = HTXWebSocketClient(self.config)
            self.backup_ws_client = BinanceWebSocketClient(self.config)
            self.kraken_ws_client = KrakenWebSocketClient(self.config)

            # Set up data handlers for all clients
            for client in [self.ws_client, self.backup_ws_client, self.kraken_ws_client]:
                client.set_trade_handler(self.handle_trade_data)
                if hasattr(client, 'set_depth_handler'):
                    client.set_depth_handler(self.handle_depth_data)
                client.set_error_handler(self.handle_ws_error)

            logger.info("[OK] WebSocket clients initialized (HTX + Binance + Kraken backups)")

            # Initialize strategy engine
            self.strategy_engine = SmartStrategy(self.config, self.data_store)
            logger.info("[OK] Strategy engine initialized")

            # Initialize LLM integration
            self.llm_integration = LLMIntegration(self.config, self.data_store)
            if self.llm_integration.enabled:
                await self.llm_integration.start_background_processing()
                logger.info("[OK] LLM integration initialized and started")
            else:
                logger.info("[OK] LLM integration initialized (disabled)")

            # Initialize dashboard
            self.dashboard = AIStrategyTunerDashboard(self.config, self.data_store)
            logger.info("[OK] Dashboard initialized")

            # Initialize system monitor
            self.system_monitor = SystemMonitor(self.config)
            asyncio.create_task(self.system_monitor.start_monitoring())
            logger.info("[OK] System monitor initialized")

            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize components: {e}")
            return False

    async def handle_trade_data(self, trade_data):
        """Handle incoming trade data from WebSocket."""
        try:
            # Process trade through parser
            features = await self.trade_parser.process_trade(trade_data)

            if features:
                # Store features
                self.data_store.store_features(features)

                # Update performance analyzer with price data
                if self.performance_analyzer and hasattr(features, 'last_price') and features.last_price:
                    symbol = getattr(features, 'symbol', 'BTC-USDT')
                    price = features.last_price
                    timestamp = getattr(features, 'timestamp', time.time())
                    self.performance_analyzer.add_price_data(symbol, price, timestamp)

                # Process through strategy engine
                signal = await self.strategy_engine.process_features(features)

                if signal:
                    # Record signal generation for monitoring
                    if self.system_monitor:
                        self.system_monitor.record_signal()

                    # Track signal for performance analysis
                    if self.signal_tracker and isinstance(signal, dict) and signal.get('action') in ['LONG', 'SHORT']:
                        signal_id = self.signal_tracker.add_signal(signal)
                        logger.info(f"📊 Signal tracked: {signal_id}")

                    # Process through LLM if enabled
                    if self.llm_integration.enabled:
                        await self.llm_integration.process_signal(signal, features)

        except Exception as e:
            logger.error(f"[ERROR] Error handling trade data: {e}")
            if self.system_monitor:
                self.system_monitor.record_error()

    async def handle_depth_data(self, depth_data):
        """Handle incoming order book depth data."""
        try:
            await self.trade_parser.process_depth(depth_data)
        except Exception as e:
            logger.error(f"[ERROR] Error handling depth data: {e}")

    async def handle_ws_error(self, error):
        """Handle WebSocket errors."""
        logger.error(f"[WS_ERROR] WebSocket error: {error}")

    async def start_websocket(self):
        """Start WebSocket connection and data streaming with multiple fallbacks."""
        try:
            # Try HTX first
            logger.info("[CONNECT] Connecting to HTX WebSocket...")

            if await self.ws_client.connect():
                logger.info("[OK] HTX WebSocket connected successfully")
                self.active_exchange = "HTX"
                asyncio.create_task(self.ws_client.listen())
                return True
            else:
                logger.warning("[WARN] HTX connection failed, trying Binance backup...")

                # Try Binance backup
                if await self.backup_ws_client.connect():
                    logger.info("[OK] Binance WebSocket connected successfully")
                    self.active_exchange = "Binance"
                    asyncio.create_task(self.backup_ws_client.listen())
                    return True
                else:
                    logger.warning("[WARN] Binance connection failed, trying Kraken backup...")

                    # Try Kraken backup (EU-friendly)
                    if await self.kraken_ws_client.connect():
                        logger.info("[OK] Kraken WebSocket connected successfully")
                        self.active_exchange = "Kraken"
                        asyncio.create_task(self.kraken_ws_client.listen())
                        return True
                    else:
                        logger.error("[ERROR] All exchange connections failed (HTX, Binance, Kraken)")
                        # Continue without WebSocket for testing
                        logger.info("[MODE] Continuing without WebSocket for testing purposes")
                        self.active_exchange = "None (Testing Mode)"
                        return True

        except Exception as e:
            logger.error(f"[ERROR] WebSocket startup error: {e}")
            # Continue without WebSocket for testing
            logger.info("[MODE] Continuing without WebSocket for testing purposes")
            self.active_exchange = "None (Testing Mode)"
            return True

    async def start_dashboard(self):
        """Start the web dashboard."""
        try:
            host = self.config['dashboard']['host']
            port = self.config['dashboard']['port']

            logger.info(f"[WEB] Starting dashboard on http://{host}:{port}")

            # Set strategy running status
            self.dashboard.set_strategy_running(True)

            # Start dashboard server
            await self.dashboard.start_server(host, port)

            logger.info("[OK] Dashboard started successfully")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to start dashboard: {e}")
            return False

    async def start(self):
        """Start the complete application."""
        try:
            logger.info("[START] Starting Epinnox V6 AI Strategy Tuner...")

            # Load configuration
            if not await self.load_config():
                return False

            # Initialize components
            if not await self.initialize_components():
                return False

            # Start WebSocket connection
            if not await self.start_websocket():
                return False

            # Start dashboard
            if not await self.start_dashboard():
                return False

            self.running = True

            logger.info("[SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!")
            logger.info("=" * 60)
            logger.info(f"[WEB] Dashboard: http://localhost:8086")
            logger.info(f"[DATA] Market data source: {self.active_exchange}")
            logger.info("[AI] AI model analysis and LLM integration")
            logger.info("[SIGNALS] Live trading signal generation")
            logger.info("=" * 60)

            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to start application: {e}")
            return False

    async def stop(self):
        """Stop the application gracefully."""
        logger.info("[STOP] Stopping Epinnox V6 AI Strategy Tuner...")

        self.running = False

        try:
            # Disconnect WebSockets
            if self.ws_client:
                await self.ws_client.disconnect()
            if self.backup_ws_client:
                await self.backup_ws_client.disconnect()

            # Shutdown data store
            if self.data_store:
                self.data_store.shutdown()

            logger.info("[OK] Application stopped gracefully")

        except Exception as e:
            logger.error(f"[ERROR] Error during shutdown: {e}")

    async def run_forever(self):
        """Run the application until interrupted."""
        if not await self.start():
            return False

        try:
            # Keep running until interrupted
            while self.running:
                await asyncio.sleep(1)

                # Optional: Add periodic health checks here

        except KeyboardInterrupt:
            logger.info("[INTERRUPT] Received interrupt signal")
        except Exception as e:
            logger.error(f"[ERROR] Runtime error: {e}")
        finally:
            await self.stop()

        return True

async def main():
    """Main entry point."""
    # Ensure logs directory exists
    Path('logs').mkdir(exist_ok=True)

    # Create and run application
    app = EpinnoxV6Application()

    # Setup signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.info(f"[SIGNAL] Received signal {signum}")
        app.running = False

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Run application
    success = await app.run_forever()

    if success:
        logger.info("[COMPLETE] Epinnox V6 AI Strategy Tuner completed successfully")
    else:
        logger.error("[FAILED] Epinnox V6 AI Strategy Tuner failed to start")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n[INTERRUPT] Application interrupted by user")
    except Exception as e:
        print(f"[FATAL] Fatal error: {e}")
        sys.exit(1)
