"""
Enhanced Performance Analytics System
Real-time P&L tracking, advanced metrics, and performance analysis
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import statistics
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class AdvancedMetrics:
    """Advanced performance metrics and analytics."""
    # Basic metrics
    total_signals: int = 0
    win_rate: float = 0.0
    total_pnl: float = 0.0
    total_pnl_percentage: float = 0.0
    
    # Advanced metrics
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    profit_factor: float = 0.0
    average_win: float = 0.0
    average_loss: float = 0.0
    largest_win: float = 0.0
    largest_loss: float = 0.0
    
    # Time-based metrics
    average_holding_time_minutes: float = 0.0
    fastest_profit_minutes: float = 0.0
    longest_loss_minutes: float = 0.0
    
    # Confidence analysis
    high_confidence_win_rate: float = 0.0  # >80% confidence
    medium_confidence_win_rate: float = 0.0  # 60-80% confidence
    low_confidence_win_rate: float = 0.0  # <60% confidence
    
    # Direction analysis
    long_win_rate: float = 0.0
    short_win_rate: float = 0.0
    long_total_pnl: float = 0.0
    short_total_pnl: float = 0.0
    
    # Real-time tracking
    current_unrealized_pnl: float = 0.0
    active_positions: int = 0
    daily_pnl: float = 0.0
    weekly_pnl: float = 0.0
    monthly_pnl: float = 0.0

@dataclass
class RealTimeUpdate:
    """Real-time update data for dashboard."""
    timestamp: float
    current_price: float
    unrealized_pnl: float
    total_pnl: float
    active_signals: int
    win_rate: float
    daily_change: float

class PerformanceAnalyzer:
    """
    Advanced performance analysis and real-time tracking system.
    """
    
    def __init__(self, signal_tracker):
        self.signal_tracker = signal_tracker
        self.price_history: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        self.pnl_history: List[Dict[str, Any]] = []
        self.real_time_updates: List[RealTimeUpdate] = []
        
        # Performance tracking
        self.daily_start_pnl = 0.0
        self.session_start_time = time.time()
        
        logger.info("Performance Analyzer initialized")
    
    def add_price_data(self, symbol: str, price: float, timestamp: float = None):
        """Add price data for analysis."""
        if timestamp is None:
            timestamp = time.time()
        
        price_data = {
            'timestamp': timestamp,
            'price': price,
            'symbol': symbol
        }
        
        self.price_history[symbol].append(price_data)
        
        # Keep only last 1000 price points per symbol
        if len(self.price_history[symbol]) > 1000:
            self.price_history[symbol] = self.price_history[symbol][-1000:]
        
        # Update signal tracker
        self.signal_tracker.update_price(symbol, price)
        
        # Generate real-time update
        self._generate_real_time_update(symbol, price, timestamp)
    
    def _generate_real_time_update(self, symbol: str, price: float, timestamp: float):
        """Generate real-time update for dashboard."""
        try:
            metrics = self.signal_tracker.get_performance_metrics()
            
            # Calculate daily change
            daily_change = 0.0
            if self.pnl_history:
                today_start = time.time() - (24 * 3600)
                today_pnl = [p['total_pnl'] for p in self.pnl_history if p['timestamp'] >= today_start]
                if today_pnl:
                    daily_change = today_pnl[-1] - today_pnl[0] if len(today_pnl) > 1 else today_pnl[0]
            
            update = RealTimeUpdate(
                timestamp=timestamp,
                current_price=price,
                unrealized_pnl=metrics.current_unrealized_pnl,
                total_pnl=metrics.total_pnl,
                active_signals=metrics.active_positions,
                win_rate=metrics.win_rate,
                daily_change=daily_change
            )
            
            self.real_time_updates.append(update)
            
            # Keep only last 100 updates
            if len(self.real_time_updates) > 100:
                self.real_time_updates = self.real_time_updates[-100:]
            
            # Add to P&L history
            pnl_entry = {
                'timestamp': timestamp,
                'total_pnl': metrics.total_pnl,
                'unrealized_pnl': metrics.current_unrealized_pnl,
                'active_positions': metrics.active_positions,
                'win_rate': metrics.win_rate
            }
            
            self.pnl_history.append(pnl_entry)
            
            # Keep only last 1000 P&L entries
            if len(self.pnl_history) > 1000:
                self.pnl_history = self.pnl_history[-1000:]
                
        except Exception as e:
            logger.error(f"Error generating real-time update: {e}")
    
    def calculate_advanced_metrics(self) -> AdvancedMetrics:
        """Calculate comprehensive advanced metrics."""
        try:
            metrics = AdvancedMetrics()
            signals = list(self.signal_tracker.signals.values())
            
            if not signals:
                return metrics
            
            # Basic metrics
            basic_metrics = self.signal_tracker.get_performance_metrics()
            metrics.total_signals = basic_metrics.total_signals
            metrics.win_rate = basic_metrics.win_rate
            metrics.total_pnl = basic_metrics.total_pnl
            metrics.total_pnl_percentage = basic_metrics.total_pnl_percentage
            metrics.current_unrealized_pnl = basic_metrics.current_unrealized_pnl
            metrics.active_positions = basic_metrics.active_positions
            
            # Get closed signals for analysis
            closed_signals = [s for s in signals if s.status.value in ['profitable', 'stopped_out', 'expired']]
            
            if not closed_signals:
                return metrics
            
            # Win/Loss analysis
            winning_signals = [s for s in closed_signals if s.pnl > 0]
            losing_signals = [s for s in closed_signals if s.pnl <= 0]
            
            if winning_signals:
                metrics.average_win = statistics.mean([s.pnl for s in winning_signals])
                metrics.largest_win = max([s.pnl for s in winning_signals])
                metrics.fastest_profit_minutes = min([s.holding_time / 60 for s in winning_signals])
            
            if losing_signals:
                metrics.average_loss = statistics.mean([s.pnl for s in losing_signals])
                metrics.largest_loss = min([s.pnl for s in losing_signals])  # Most negative
                metrics.longest_loss_minutes = max([s.holding_time / 60 for s in losing_signals])
            
            # Profit factor
            total_wins = sum([s.pnl for s in winning_signals])
            total_losses = abs(sum([s.pnl for s in losing_signals]))
            if total_losses > 0:
                metrics.profit_factor = total_wins / total_losses
            
            # Holding time analysis
            holding_times = [s.holding_time / 60 for s in closed_signals]  # Convert to minutes
            if holding_times:
                metrics.average_holding_time_minutes = statistics.mean(holding_times)
            
            # Confidence analysis
            high_conf_signals = [s for s in closed_signals if s.confidence >= 80]
            medium_conf_signals = [s for s in closed_signals if 60 <= s.confidence < 80]
            low_conf_signals = [s for s in closed_signals if s.confidence < 60]
            
            if high_conf_signals:
                high_conf_wins = len([s for s in high_conf_signals if s.pnl > 0])
                metrics.high_confidence_win_rate = (high_conf_wins / len(high_conf_signals)) * 100
            
            if medium_conf_signals:
                medium_conf_wins = len([s for s in medium_conf_signals if s.pnl > 0])
                metrics.medium_confidence_win_rate = (medium_conf_wins / len(medium_conf_signals)) * 100
            
            if low_conf_signals:
                low_conf_wins = len([s for s in low_conf_signals if s.pnl > 0])
                metrics.low_confidence_win_rate = (low_conf_wins / len(low_conf_signals)) * 100
            
            # Direction analysis
            long_signals = [s for s in closed_signals if s.direction == 'LONG']
            short_signals = [s for s in closed_signals if s.direction == 'SHORT']
            
            if long_signals:
                long_wins = len([s for s in long_signals if s.pnl > 0])
                metrics.long_win_rate = (long_wins / len(long_signals)) * 100
                metrics.long_total_pnl = sum([s.pnl for s in long_signals])
            
            if short_signals:
                short_wins = len([s for s in short_signals if s.pnl > 0])
                metrics.short_win_rate = (short_wins / len(short_signals)) * 100
                metrics.short_total_pnl = sum([s.pnl for s in short_signals])
            
            # Drawdown calculation
            if self.pnl_history:
                running_max = 0
                max_drawdown = 0
                for entry in self.pnl_history:
                    total_pnl = entry['total_pnl']
                    if total_pnl > running_max:
                        running_max = total_pnl
                    drawdown = running_max - total_pnl
                    if drawdown > max_drawdown:
                        max_drawdown = drawdown
                metrics.max_drawdown = max_drawdown
            
            # Sharpe ratio (simplified)
            if closed_signals and len(closed_signals) > 1:
                returns = [s.pnl_percentage for s in closed_signals]
                if statistics.stdev(returns) > 0:
                    metrics.sharpe_ratio = statistics.mean(returns) / statistics.stdev(returns)
            
            # Time-based P&L
            current_time = time.time()
            day_ago = current_time - (24 * 3600)
            week_ago = current_time - (7 * 24 * 3600)
            month_ago = current_time - (30 * 24 * 3600)
            
            daily_signals = [s for s in closed_signals if s.exit_time and s.exit_time >= day_ago]
            weekly_signals = [s for s in closed_signals if s.exit_time and s.exit_time >= week_ago]
            monthly_signals = [s for s in closed_signals if s.exit_time and s.exit_time >= month_ago]
            
            metrics.daily_pnl = sum([s.pnl for s in daily_signals])
            metrics.weekly_pnl = sum([s.pnl for s in weekly_signals])
            metrics.monthly_pnl = sum([s.pnl for s in monthly_signals])
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating advanced metrics: {e}")
            return AdvancedMetrics()
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary for dashboard."""
        try:
            advanced_metrics = self.calculate_advanced_metrics()
            basic_metrics = self.signal_tracker.get_performance_metrics()
            
            # Get recent signals
            recent_signals = self.signal_tracker.get_signals_for_display(10)
            
            # Get latest real-time update
            latest_update = self.real_time_updates[-1] if self.real_time_updates else None
            
            summary = {
                'basic_metrics': asdict(basic_metrics),
                'advanced_metrics': asdict(advanced_metrics),
                'recent_signals': recent_signals,
                'latest_update': asdict(latest_update) if latest_update else None,
                'pnl_chart_data': self._get_pnl_chart_data(),
                'performance_breakdown': self._get_performance_breakdown(),
                'timestamp': time.time()
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return {}
    
    def _get_pnl_chart_data(self) -> List[Dict[str, Any]]:
        """Get P&L data formatted for charting."""
        try:
            chart_data = []
            
            for entry in self.pnl_history[-100:]:  # Last 100 entries
                chart_data.append({
                    'x': entry['timestamp'] * 1000,  # Chart.js expects milliseconds
                    'y': entry['total_pnl'],
                    'unrealized': entry['unrealized_pnl'],
                    'active_positions': entry['active_positions']
                })
            
            return chart_data
            
        except Exception as e:
            logger.error(f"Error getting P&L chart data: {e}")
            return []
    
    def _get_performance_breakdown(self) -> Dict[str, Any]:
        """Get detailed performance breakdown."""
        try:
            signals = list(self.signal_tracker.signals.values())
            
            breakdown = {
                'by_direction': {
                    'LONG': {'count': 0, 'wins': 0, 'total_pnl': 0.0},
                    'SHORT': {'count': 0, 'wins': 0, 'total_pnl': 0.0}
                },
                'by_confidence': {
                    'high': {'count': 0, 'wins': 0, 'total_pnl': 0.0},  # >80%
                    'medium': {'count': 0, 'wins': 0, 'total_pnl': 0.0},  # 60-80%
                    'low': {'count': 0, 'wins': 0, 'total_pnl': 0.0}  # <60%
                },
                'by_status': {
                    'pending': 0,
                    'profitable': 0,
                    'stopped_out': 0,
                    'expired': 0
                }
            }
            
            for signal in signals:
                # Direction breakdown
                direction = signal.direction
                breakdown['by_direction'][direction]['count'] += 1
                if signal.pnl > 0:
                    breakdown['by_direction'][direction]['wins'] += 1
                breakdown['by_direction'][direction]['total_pnl'] += signal.pnl
                
                # Confidence breakdown
                if signal.confidence >= 80:
                    conf_level = 'high'
                elif signal.confidence >= 60:
                    conf_level = 'medium'
                else:
                    conf_level = 'low'
                
                breakdown['by_confidence'][conf_level]['count'] += 1
                if signal.pnl > 0:
                    breakdown['by_confidence'][conf_level]['wins'] += 1
                breakdown['by_confidence'][conf_level]['total_pnl'] += signal.pnl
                
                # Status breakdown
                breakdown['by_status'][signal.status.value] += 1
            
            # Calculate win rates
            for direction_data in breakdown['by_direction'].values():
                if direction_data['count'] > 0:
                    direction_data['win_rate'] = (direction_data['wins'] / direction_data['count']) * 100
                else:
                    direction_data['win_rate'] = 0.0
            
            for conf_data in breakdown['by_confidence'].values():
                if conf_data['count'] > 0:
                    conf_data['win_rate'] = (conf_data['wins'] / conf_data['count']) * 100
                else:
                    conf_data['win_rate'] = 0.0
            
            return breakdown
            
        except Exception as e:
            logger.error(f"Error getting performance breakdown: {e}")
            return {}
    
    def get_chart_price_data(self, symbol: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get price data formatted for charting."""
        try:
            if symbol not in self.price_history:
                return []
            
            price_data = self.price_history[symbol][-limit:]
            
            chart_data = []
            for entry in price_data:
                chart_data.append({
                    'x': entry['timestamp'] * 1000,  # Chart.js expects milliseconds
                    'y': entry['price']
                })
            
            return chart_data
            
        except Exception as e:
            logger.error(f"Error getting chart price data: {e}")
            return []
