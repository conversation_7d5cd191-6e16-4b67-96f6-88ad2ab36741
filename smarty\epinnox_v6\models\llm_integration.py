#!/usr/bin/env python3
"""
LLM Integration for AI Trading Decisions
Epinnox V6 - Standalone AI Strategy Tuner
"""

import json
import logging
import time
import asyncio
import aiohttp
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from feeds.trade_parser import MarketFeatures
from models.smart_strategy import TradingSignal
from storage.live_store import LiveDataStore

logger = logging.getLogger(__name__)

@dataclass
class LLMDecision:
    """LLM decision structure."""
    symbol: str
    action: str  # 'LONG', 'SHORT', 'WAIT'
    confidence: float
    reasoning: str
    risk_level: str  # 'LOW', 'MEDIUM', 'HIGH'
    model_consensus: str  # 'STRONG', 'MODERATE', 'WEAK'
    timestamp: float

class LLMIntegration:
    """
    LLM integration for final trading decision synthesis.
    Uses Phi-3.1 model to analyze AI model outputs and market context.
    """

    def __init__(self, config: Dict[str, Any], data_store: LiveDataStore):
        self.config = config
        self.data_store = data_store
        self.llm_config = config['llm']

        # LLM state
        self.enabled = self.llm_config['enabled']
        self.auto_detect = self.llm_config.get('auto_detect', True)
        self.api_url = self.llm_config['api_url']
        self.last_call_time = {}
        self.call_interval = self.llm_config['call_interval']

        # Health check state
        self.server_available = False
        self.last_health_check = 0
        self.health_check_interval = 300  # Check every 5 minutes
        self.consecutive_failures = 0
        self.max_failures_before_disable = 3

        # Statistics
        self.stats = {
            'decisions_made': 0,
            'api_calls': 0,
            'errors': 0,
            'avg_response_time': 0.0,
            'last_decision_time': None,
            'server_available': False,
            'last_health_check': None
        }

        if self.enabled:
            logger.info(f"LLM Integration initialized (enabled: {self.enabled}, auto-detect: {self.auto_detect})")
        else:
            logger.info("LLM Integration disabled in configuration")

    async def _check_server_health(self) -> bool:
        """Check if LLM server is available and responding."""
        try:
            current_time = time.time()

            # Skip if recently checked
            if current_time - self.last_health_check < self.health_check_interval:
                return self.server_available

            self.last_health_check = current_time

            # Simple health check - try to connect to the API
            timeout = aiohttp.ClientTimeout(total=5)  # Short timeout for health check
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # Try a simple request to check if server is responding
                test_payload = {
                    "model": "phi-3.1-mini",
                    "messages": [{"role": "user", "content": "test"}],
                    "max_tokens": 1
                }

                async with session.post(self.api_url, json=test_payload) as response:
                    if response.status in [200, 400]:  # 400 is OK, means server is responding
                        self.server_available = True
                        self.consecutive_failures = 0
                        logger.info("[LLM] Server health check: AVAILABLE")
                        return True
                    else:
                        self.server_available = False
                        self.consecutive_failures += 1
                        logger.warning(f"[LLM] Server health check: UNAVAILABLE (status: {response.status})")
                        return False

        except Exception as e:
            self.server_available = False
            self.consecutive_failures += 1

            # Only log error on first few failures to avoid spam
            if self.consecutive_failures <= 2:
                logger.warning(f"[LLM] Server health check: FAILED ({e})")
            elif self.consecutive_failures == self.max_failures_before_disable:
                logger.info("[LLM] Server appears to be offline, disabling LLM calls to reduce log noise")

            return False
        finally:
            self.stats['server_available'] = self.server_available
            self.stats['last_health_check'] = current_time

    async def process_signal(self, signal: TradingSignal, features: MarketFeatures) -> Optional[LLMDecision]:
        """
        Process trading signal through LLM for final decision.

        Args:
            signal: Trading signal from AI models
            features: Current market features

        Returns:
            LLMDecision if successful, None otherwise
        """
        if not self.enabled:
            return None

        # Check server health before attempting API call
        if not await self._check_server_health():
            # Server is not available, skip silently after max failures
            if self.consecutive_failures >= self.max_failures_before_disable:
                return None
            else:
                logger.debug(f"[LLM] Skipping call for {signal.symbol} - server unavailable")
                return None

        try:
            symbol = signal.symbol
            current_time = time.time()

            # Check call interval
            if self._is_in_cooldown(symbol, current_time):
                return None

            # Prepare context for LLM
            context = await self._prepare_context(signal, features)

            # Call LLM API
            llm_response = await self._call_llm_api(context)

            if llm_response:
                # Parse LLM response
                decision = await self._parse_llm_response(llm_response, signal)

                if decision:
                    # Update call time
                    self.last_call_time[symbol] = current_time

                    # Store decision
                    decision_data = {
                        'symbol': decision.symbol,
                        'action': decision.action,
                        'confidence': decision.confidence,
                        'reasoning': decision.reasoning,
                        'risk_level': decision.risk_level,
                        'model_consensus': decision.model_consensus,
                        'timestamp': decision.timestamp
                    }

                    self.data_store.store_llm_decision(decision_data)

                    # Update statistics
                    self.stats['decisions_made'] += 1
                    self.stats['last_decision_time'] = current_time

                    logger.info(f"🧠 LLM decision: {symbol} - {decision.action} (confidence: {decision.confidence:.2%})")
                    return decision

            return None

        except Exception as e:
            logger.error(f"Error processing LLM signal: {e}")
            self.stats['errors'] += 1
            return None

    async def _prepare_context(self, signal: TradingSignal, features: MarketFeatures) -> Dict[str, Any]:
        """Prepare context data for LLM."""
        # Get recent signals for context
        recent_signals = self.data_store.get_signals(signal.symbol, limit=5)

        # Get model outputs
        model_outputs = self.data_store.get_model_outputs(signal.symbol)

        # Prepare market context
        market_context = {
            'symbol': signal.symbol,
            'current_price': features.last_price,
            'price_change_1m': features.price_change_1m,
            'volume_1m': features.volume_1m,
            'buy_volume_ratio': features.buy_volume_ratio,
            'order_flow_imbalance': features.order_flow_imbalance,
            'rsi': features.rsi,
            'vwap': features.vwap,
            'volatility': features.volatility
        }

        # Prepare signal context
        signal_context = {
            'action': signal.action,
            'confidence': signal.confidence,
            'score': signal.score,
            'reasoning': signal.reasoning,
            'model_contributions': signal.model_contributions
        }

        return {
            'market_context': market_context,
            'signal_context': signal_context,
            'model_outputs': model_outputs,
            'recent_signals': recent_signals[-3:] if recent_signals else []
        }

    async def _call_llm_api(self, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Call LLM API with context data."""
        try:
            start_time = time.time()

            # Prepare prompt
            prompt = self._build_prompt(context)

            # Prepare API request
            payload = {
                "model": "phi-3.1-mini",
                "messages": [
                    {
                        "role": "system",
                        "content": self.llm_config['prompt']['system']
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": self.llm_config['max_tokens'],
                "temperature": self.llm_config['temperature']
            }

            # Make API call
            timeout = aiohttp.ClientTimeout(total=self.llm_config['timeout'])
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(self.api_url, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()

                        # Update statistics
                        response_time = time.time() - start_time
                        self.stats['api_calls'] += 1
                        self.stats['avg_response_time'] = (
                            (self.stats['avg_response_time'] * (self.stats['api_calls'] - 1) + response_time) /
                            self.stats['api_calls']
                        )

                        return result
                    else:
                        # Mark server as unavailable on error
                        self.server_available = False
                        self.consecutive_failures += 1

                        # Only log first few errors to avoid spam
                        if self.consecutive_failures <= 2:
                            logger.error(f"LLM API error: {response.status}")

                        self.stats['errors'] += 1
                        return None

        except asyncio.TimeoutError:
            self.server_available = False
            self.consecutive_failures += 1

            if self.consecutive_failures <= 2:
                logger.error("LLM API timeout")

            self.stats['errors'] += 1
            return None
        except Exception as e:
            self.server_available = False
            self.consecutive_failures += 1

            if self.consecutive_failures <= 2:
                logger.error(f"LLM API call error: {e}")

            self.stats['errors'] += 1
            return None

    def _build_prompt(self, context: Dict[str, Any]) -> str:
        """Build prompt for LLM from context data."""
        market = context['market_context']
        signal = context['signal_context']
        models = context['model_outputs']
        recent = context['recent_signals']

        # Safe formatting for potentially None values
        def safe_format(value, format_str, default='N/A'):
            try:
                if value is not None:
                    return format_str.format(value)
                else:
                    return default
            except (ValueError, TypeError):
                return default

        prompt = f"""
MARKET ANALYSIS REQUEST

Symbol: {market['symbol']}
Current Price: ${safe_format(market.get('current_price'), '${:,.2f}')}
1-minute Price Change: {safe_format(market.get('price_change_1m'), '{:.2%}')}
Volume (1m): {safe_format(market.get('volume_1m'), '{:,.0f}')}
Buy/Sell Ratio: {safe_format(market.get('buy_volume_ratio'), '{:.2%}')}
Order Flow Imbalance: {safe_format(market.get('order_flow_imbalance'), '{:.3f}')}

TECHNICAL INDICATORS:
- RSI: {safe_format(market.get('rsi'), '{:.1f}')}
- VWAP: ${safe_format(market.get('vwap'), '{:.2f}')}
- Volatility: {safe_format(market.get('volatility'), '{:.3f}')}

AI MODEL ENSEMBLE SIGNAL:
Action: {signal.get('action', 'UNKNOWN')}
Confidence: {safe_format(signal.get('confidence'), '{:.1%}')}
Score: {safe_format(signal.get('score'), '{:.3f}')}
Reasoning: {signal.get('reasoning', 'No reasoning provided')}

INDIVIDUAL MODEL OUTPUTS:
"""

        for model_name, output in models.items():
            prompt += f"- {model_name.upper()}: {output.get('action', 'N/A')} ({output.get('confidence', 0):.1%})\n"

        if recent:
            prompt += f"\nRECENT SIGNALS ({len(recent)} last):\n"
            for i, sig in enumerate(recent, 1):
                prompt += f"{i}. {sig.get('action', 'N/A')} at {sig.get('formatted_time', 'N/A')}\n"

        prompt += """
Please analyze this trading situation and provide your recommendation as a JSON object with:
- action: "LONG", "SHORT", or "WAIT"
- confidence: float between 0.0 and 1.0
- reasoning: brief explanation (max 100 words)
- risk_level: "LOW", "MEDIUM", or "HIGH"
"""

        return prompt

    async def _parse_llm_response(self, response: Dict[str, Any], signal: TradingSignal) -> Optional[LLMDecision]:
        """Parse LLM API response into decision structure."""
        try:
            # Extract content from response
            content = response.get('choices', [{}])[0].get('message', {}).get('content', '')

            if not content:
                return None

            # Try to parse JSON from content
            # Sometimes LLM wraps JSON in markdown code blocks
            if '```json' in content:
                json_start = content.find('```json') + 7
                json_end = content.find('```', json_start)
                json_content = content[json_start:json_end].strip()
            elif '{' in content and '}' in content:
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                json_content = content[json_start:json_end]
            else:
                logger.error("No JSON found in LLM response")
                return None

            # Parse JSON
            decision_data = json.loads(json_content)

            # Validate required fields
            required_fields = ['action', 'confidence', 'reasoning', 'risk_level']
            for field in required_fields:
                if field not in decision_data:
                    logger.error(f"Missing field in LLM response: {field}")
                    return None

            # Determine model consensus strength
            model_consensus = self._determine_consensus_strength(signal)

            return LLMDecision(
                symbol=signal.symbol,
                action=decision_data['action'].upper(),
                confidence=float(decision_data['confidence']),
                reasoning=decision_data['reasoning'],
                risk_level=decision_data['risk_level'].upper(),
                model_consensus=model_consensus,
                timestamp=time.time()
            )

        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error in LLM response: {e}")
            return None
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            return None

    def _determine_consensus_strength(self, signal: TradingSignal) -> str:
        """Determine consensus strength from model contributions."""
        contributions = signal.model_contributions
        if not contributions:
            return 'WEAK'

        # Calculate agreement level
        positive_models = sum(1 for score in contributions.values() if score > 0.3)
        negative_models = sum(1 for score in contributions.values() if score < -0.3)
        total_models = len(contributions)

        agreement_ratio = max(positive_models, negative_models) / total_models

        if agreement_ratio >= 0.8:
            return 'STRONG'
        elif agreement_ratio >= 0.6:
            return 'MODERATE'
        else:
            return 'WEAK'

    def _is_in_cooldown(self, symbol: str, current_time: float) -> bool:
        """Check if symbol is in LLM call cooldown period."""
        last_time = self.last_call_time.get(symbol, 0)
        return (current_time - last_time) < self.call_interval

    async def start_background_processing(self):
        """Start background LLM processing task."""
        if self.enabled:
            asyncio.create_task(self._periodic_analysis())
            logger.info("LLM background processing started")
        else:
            logger.info("LLM background processing skipped (disabled)")

    async def _periodic_analysis(self):
        """Periodic analysis of market conditions."""
        while True:
            try:
                await asyncio.sleep(self.call_interval)

                # Check server health first
                if not await self._check_server_health():
                    # Skip this cycle if server is not available
                    continue

                # Get symbols with recent activity
                symbols = self.config['symbols']['enabled']

                for symbol in symbols:
                    # Check if we have recent features
                    features = self.data_store.get_features(symbol)
                    if features:
                        # Get recent signals
                        recent_signals = self.data_store.get_signals(symbol, limit=1)
                        if recent_signals:
                            latest_signal = recent_signals[-1]

                            # Create mock signal for analysis
                            from models.smart_strategy import TradingSignal
                            signal = TradingSignal(
                                symbol=symbol,
                                action=latest_signal.get('action', 'WAIT'),
                                confidence=latest_signal.get('confidence', 0.5),
                                score=latest_signal.get('score', 0.0),
                                reasoning=latest_signal.get('reasoning', ''),
                                model_contributions=latest_signal.get('model_contributions', {}),
                                timestamp=time.time(),
                                price=features.last_price
                            )

                            # Process through LLM
                            await self.process_signal(signal, features)

            except Exception as e:
                logger.error(f"Error in periodic LLM analysis: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """Get LLM integration statistics."""
        return dict(self.stats)
