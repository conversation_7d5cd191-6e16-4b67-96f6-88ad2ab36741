#!/usr/bin/env python3
"""
LLM Integration for AI Trading Decisions
Epinnox V6 - Standalone AI Strategy Tuner
"""

import json
import logging
import time
import asyncio
import aiohttp
import yaml
import os
import statistics
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from feeds.trade_parser import MarketFeatures
from models.smart_strategy import TradingSignal
from storage.live_store import LiveDataStore

logger = logging.getLogger(__name__)

@dataclass
class LLMDecision:
    """Enhanced LLM decision structure with self-awareness and conflict resolution."""
    symbol: str
    final_decision: str  # 'LONG', 'SHORT', 'WAIT'
    confidence: float  # 0-100 (validated range)
    reasoning: str
    risk_assessment: str  # 'LOW', 'MEDIUM', 'HIGH'
    model_consensus: str  # 'STRONG', 'WEAK', 'CONFLICTED'
    timestamp: float

    # Enhanced self-awareness fields
    disagreement_detected: bool = False
    conflicting_models: list = None
    conviction_score: int = 3  # 1-5 stars
    reasoning_quality: str = "MEDIUM"  # 'LOW', 'MEDIUM', 'HIGH'
    performance_context_applied: bool = False
    market_regime_factor: str = ""

    # Additional context fields
    market_regime: str = "unknown"
    performance_feedback: str = ""
    confidence_adjustment: float = 0.0

    def __post_init__(self):
        """Validate and clamp all decision parameters."""
        # Initialize conflicting_models if None
        if self.conflicting_models is None:
            self.conflicting_models = []

        # Ensure confidence is in valid range (0-100)
        if self.confidence > 100:
            logger.warning(f"Confidence {self.confidence}% clamped to 100%")
            self.confidence = 100.0
        elif self.confidence < 0:
            logger.warning(f"Confidence {self.confidence}% clamped to 0%")
            self.confidence = 0.0

        # Validate action
        valid_actions = ['LONG', 'SHORT', 'WAIT']
        if self.final_decision not in valid_actions:
            logger.warning(f"Invalid action {self.final_decision}, defaulting to WAIT")
            self.final_decision = 'WAIT'
            self.confidence = 0.0

        # Validate conviction score
        if not isinstance(self.conviction_score, int) or not (1 <= self.conviction_score <= 5):
            logger.warning(f"Invalid conviction score {self.conviction_score}, defaulting to 3")
            self.conviction_score = 3

        # Validate reasoning quality
        valid_qualities = ['LOW', 'MEDIUM', 'HIGH']
        if self.reasoning_quality not in valid_qualities:
            logger.warning(f"Invalid reasoning quality {self.reasoning_quality}, defaulting to MEDIUM")
            self.reasoning_quality = 'MEDIUM'

class LLMIntegration:
    """
    LLM integration for final trading decision synthesis.
    Uses Phi-3.1 model to analyze AI model outputs and market context.
    """

    def __init__(self, config: Dict[str, Any], data_store: LiveDataStore):
        self.config = config
        self.data_store = data_store
        self.llm_config = config['llm']

        # LLM state
        self.enabled = self.llm_config['enabled']
        self.auto_detect = self.llm_config.get('auto_detect', True)
        self.api_url = self.llm_config['api_url']
        self.last_call_time = {}
        self.call_interval = self.llm_config['call_interval']

        # Load prompt templates
        self.prompt_templates = self._load_prompt_templates()

        # Performance tracking for adaptive reasoning
        self.recent_decisions = []
        self.performance_history = []

        # Health check state
        self.server_available = False
        self.last_health_check = 0
        self.health_check_interval = 300  # Check every 5 minutes
        self.consecutive_failures = 0
        self.max_failures_before_disable = 3

        # Statistics
        self.stats = {
            'decisions_made': 0,
            'api_calls': 0,
            'errors': 0,
            'avg_response_time': 0.0,
            'last_decision_time': None,
            'server_available': False,
            'last_health_check': None
        }

        if self.enabled:
            logger.info(f"LLM Integration initialized (enabled: {self.enabled}, auto-detect: {self.auto_detect})")
            logger.info(f"Loaded {len(self.prompt_templates)} prompt templates")
        else:
            logger.info("LLM Integration disabled in configuration")

    def _load_prompt_templates(self) -> Dict[str, Any]:
        """Load dynamic prompt templates from YAML configuration."""
        try:
            template_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'trading_prompt_templates.yaml')

            if os.path.exists(template_path):
                with open(template_path, 'r', encoding='utf-8') as f:
                    templates = yaml.safe_load(f)
                logger.info("✅ Loaded dynamic prompt templates from YAML")
                return templates
            else:
                logger.warning(f"⚠️ Prompt template file not found: {template_path}")
                return self._get_fallback_templates()

        except Exception as e:
            logger.error(f"❌ Error loading prompt templates: {e}")
            return self._get_fallback_templates()

    def _get_fallback_templates(self) -> Dict[str, Any]:
        """Fallback prompt templates if YAML file is not available."""
        return {
            'system_prompt': "You are an expert cryptocurrency trading AI assistant. Provide trading decisions in JSON format with fields: final_decision, confidence, reasoning, risk_assessment, model_consensus.",
            'trading_decision_prompt': "Analyze the market data and provide a trading decision for {symbol} at ${current_price:.2f}. Model outputs: {model_outputs}. Recent performance: {performance_context}.",
            'fallback_templates': {
                'llm_error': {
                    'final_decision': 'WAIT',
                    'confidence': 0,
                    'reasoning': 'LLM analysis failed - defaulting to safe position',
                    'risk_assessment': 'HIGH',
                    'model_consensus': 'CONFLICTED'
                }
            }
        }

    async def _check_server_health(self) -> bool:
        """Check if LLM server is available and responding."""
        try:
            current_time = time.time()

            # Skip if recently checked
            if current_time - self.last_health_check < self.health_check_interval:
                return self.server_available

            self.last_health_check = current_time

            # Simple health check - try to connect to the API
            timeout = aiohttp.ClientTimeout(total=5)  # Short timeout for health check
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # Try a simple request to check if server is responding
                test_payload = {
                    "model": "phi-3.1-mini",
                    "messages": [{"role": "user", "content": "test"}],
                    "max_tokens": 1
                }

                async with session.post(self.api_url, json=test_payload) as response:
                    if response.status in [200, 400]:  # 400 is OK, means server is responding
                        self.server_available = True
                        self.consecutive_failures = 0
                        logger.info("[LLM] Server health check: AVAILABLE")
                        return True
                    else:
                        self.server_available = False
                        self.consecutive_failures += 1
                        logger.warning(f"[LLM] Server health check: UNAVAILABLE (status: {response.status})")
                        return False

        except Exception as e:
            self.server_available = False
            self.consecutive_failures += 1

            # Only log error on first few failures to avoid spam
            if self.consecutive_failures <= 2:
                logger.warning(f"[LLM] Server health check: FAILED ({e})")
            elif self.consecutive_failures == self.max_failures_before_disable:
                logger.info("[LLM] Server appears to be offline, disabling LLM calls to reduce log noise")

            return False
        finally:
            self.stats['server_available'] = self.server_available
            self.stats['last_health_check'] = current_time

    async def process_signal(self, signal: TradingSignal, features: MarketFeatures) -> Optional[LLMDecision]:
        """
        Process trading signal through LLM for final decision.

        Args:
            signal: Trading signal from AI models
            features: Current market features

        Returns:
            LLMDecision if successful, None otherwise
        """
        if not self.enabled:
            return None

        # Check server health before attempting API call
        if not await self._check_server_health():
            # Server is not available, skip silently after max failures
            if self.consecutive_failures >= self.max_failures_before_disable:
                return None
            else:
                logger.debug(f"[LLM] Skipping call for {signal.symbol} - server unavailable")
                return None

        try:
            symbol = signal.symbol
            current_time = time.time()

            # Check call interval
            if self._is_in_cooldown(symbol, current_time):
                return None

            # Prepare context for LLM
            context = await self._prepare_context(signal, features)

            # Call LLM API
            llm_response = await self._call_llm_api(context)

            if llm_response:
                # Parse LLM response
                decision = await self._parse_llm_response(llm_response, signal)

                if decision:
                    # Update call time
                    self.last_call_time[symbol] = current_time

                    # Store decision with enhanced self-awareness data
                    decision_data = {
                        'symbol': decision.symbol,
                        'action': decision.final_decision,
                        'confidence': decision.confidence / 100,  # Convert back to 0-1 for storage
                        'reasoning': decision.reasoning,
                        'risk_level': decision.risk_assessment,
                        'model_consensus': decision.model_consensus,
                        'timestamp': decision.timestamp,
                        'disagreement_detected': decision.disagreement_detected,
                        'conflicting_models': decision.conflicting_models,
                        'conviction_score': decision.conviction_score,
                        'reasoning_quality': decision.reasoning_quality,
                        'performance_context_applied': decision.performance_context_applied,
                        'market_regime_factor': decision.market_regime_factor,
                        'market_regime': decision.market_regime,
                        'performance_feedback': decision.performance_feedback,
                        'confidence_adjustment': decision.confidence_adjustment
                    }

                    self.data_store.store_llm_decision(decision_data)

                    # Update statistics
                    self.stats['decisions_made'] += 1
                    self.stats['last_decision_time'] = current_time

                    logger.info(f"🧠 LLM decision: {symbol} - {decision.final_decision} (confidence: {decision.confidence:.0f}%)")
                    return decision

            return None

        except Exception as e:
            logger.error(f"Error processing LLM signal: {e}")
            self.stats['errors'] += 1
            return None

    async def _prepare_context(self, signal: TradingSignal, features: MarketFeatures) -> Dict[str, Any]:
        """Prepare enhanced context data for LLM with performance analytics."""
        try:
            # Get analytics components if available
            signal_tracker = getattr(self.data_store, 'signal_tracker', None)
            performance_analyzer = getattr(self.data_store, 'performance_analyzer', None)

            # Basic market context
            market_context = {
                'symbol': signal.symbol,
                'current_price': features.last_price,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime()),
                'price_change_1m': getattr(features, 'price_change_1m', 0),
                'volume_1m': getattr(features, 'volume_1m', 0),
                'buy_volume_ratio': getattr(features, 'buy_volume_ratio', 0.5),
                'order_flow_imbalance': getattr(features, 'order_flow_imbalance', 0),
                'rsi': getattr(features, 'rsi', 50),
                'vwap': getattr(features, 'vwap', features.last_price),
                'volatility': getattr(features, 'volatility', 0)
            }

            # Get model outputs with enhanced formatting
            model_outputs = self.data_store.get_model_outputs(signal.symbol)
            formatted_model_outputs = self._format_model_outputs(model_outputs)

            # Get performance context from analytics
            performance_context = ""
            if signal_tracker and performance_analyzer:
                try:
                    # Get performance summary
                    perf_summary = performance_analyzer.get_llm_context_summary(signal.symbol)

                    # Get formatted signal history
                    signal_history = signal_tracker.get_formatted_signal_history(signal.symbol, limit=10)

                    # Get recent pattern analysis
                    recent_pattern = signal_tracker.detect_recent_pattern(limit=5)

                    # Format performance context
                    performance_context = self.prompt_templates.get('performance_context_template', '').format(
                        signal_count=perf_summary.get('total_signals', 0),
                        win_rate=perf_summary.get('win_rate', 0),
                        total_pnl=perf_summary.get('total_pnl', 0),
                        recent_trend=perf_summary.get('recent_trend', 'Unknown'),
                        best_model=perf_summary.get('best_model', 'Unknown'),
                        worst_model=perf_summary.get('worst_model', 'Unknown'),
                        signal_history=signal_history
                    )

                except Exception as e:
                    logger.warning(f"Error getting performance context: {e}")
                    performance_context = "Performance data unavailable"
            else:
                performance_context = "Performance tracking not available"

            # Enhanced model disagreement analysis
            disagreement_analysis = self._analyze_model_disagreements(model_outputs)

            # Get enhanced market regime analysis
            market_regime = self._analyze_market_regime(features)

            # Generate performance feedback
            performance_feedback = self._generate_performance_feedback()

            # Generate self-assessment guidance
            self_assessment_guidance = self._generate_self_assessment_guidance(disagreement_analysis, performance_context)

            return {
                'market_context': market_context,
                'signal_context': {
                    'action': signal.action,
                    'confidence': signal.confidence,
                    'score': signal.score,
                    'reasoning': signal.reasoning,
                    'model_contributions': signal.model_contributions or {}
                },
                'model_outputs': formatted_model_outputs,
                'performance_context': performance_context,
                'disagreement_analysis': disagreement_analysis['disagreement_text'],
                'market_regime': market_regime,
                'market_regime_context': self._format_market_regime_context(market_regime),
                'performance_feedback': performance_feedback,
                'self_assessment_guidance': self_assessment_guidance,
                'price_trend': self._determine_price_trend(features),
                'volatility_level': self._determine_volatility_level(features),
                'volume_context': self._analyze_volume_context(features),
                # Enhanced metadata for decision processing
                'disagreement_metadata': {
                    'detected': disagreement_analysis['disagreement_detected'],
                    'conflicting_models': disagreement_analysis['conflicting_models'],
                    'severity': disagreement_analysis['conflict_severity'],
                    'acknowledgment': disagreement_analysis.get('conflict_acknowledgment', '')
                }
            }

        except Exception as e:
            logger.error(f"Error preparing LLM context: {e}")
            return self._get_fallback_context(signal, features)

    def _format_model_outputs(self, model_outputs: Dict[str, Any]) -> str:
        """Format model outputs for LLM consumption."""
        if not model_outputs:
            return "No model outputs available"

        formatted_lines = []
        template = self.prompt_templates.get('model_output_template', '{model_name}: {action} (confidence: {confidence:.1f}%)')

        for model_name, output in model_outputs.items():
            try:
                line = template.format(
                    model_name=model_name.upper(),
                    action=output.get('action', 'UNKNOWN'),
                    confidence=output.get('confidence', 0) * 100,
                    signal=output.get('signal', 'N/A'),
                    value=output.get('value', 'N/A'),
                    model_reasoning=output.get('reasoning', 'No reasoning provided')
                )
                formatted_lines.append(line)
            except Exception as e:
                logger.warning(f"Error formatting model output for {model_name}: {e}")
                formatted_lines.append(f"{model_name.upper()}: Error formatting output")

        return "\n".join(formatted_lines)

    def _analyze_model_disagreements(self, model_outputs: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced model disagreement analysis with explicit conflict details."""
        if not model_outputs or len(model_outputs) < 2:
            return {
                'disagreement_detected': False,
                'conflicting_models': [],
                'disagreement_text': '',
                'conflict_severity': 'none',
                'resolution_guidance': ''
            }

        # Categorize model outputs with detailed analysis
        long_models = []
        short_models = []
        wait_models = []

        for model_name, output in model_outputs.items():
            action = output.get('action', 'WAIT').upper()
            confidence = output.get('confidence', 0) * 100  # Convert to percentage

            if action in ['LONG', 'BUY']:
                long_models.append((model_name.upper(), confidence, action))
            elif action in ['SHORT', 'SELL']:
                short_models.append((model_name.upper(), confidence, action))
            else:
                wait_models.append((model_name.upper(), confidence, action))

        # Determine disagreement severity
        active_directions = sum([len(long_models) > 0, len(short_models) > 0])
        disagreement_detected = active_directions > 1

        if not disagreement_detected:
            return {
                'disagreement_detected': False,
                'conflicting_models': [],
                'disagreement_text': '',
                'conflict_severity': 'none',
                'resolution_guidance': 'Models are in agreement'
            }

        # Build detailed conflict analysis
        conflicting_models = []
        conflict_details = []

        if long_models and short_models:
            # Direct opposition
            conflict_severity = 'high'
            for model, conf, action in long_models:
                conflicting_models.append(model.lower())
                conflict_details.append(f"{model} indicates {action} (confidence: {conf:.0f}%)")

            for model, conf, action in short_models:
                conflicting_models.append(model.lower())
                conflict_details.append(f"{model} suggests {action} (confidence: {conf:.0f}%)")

            # Generate explicit conflict acknowledgment
            if len(long_models) == 1 and len(short_models) == 1:
                long_model, long_conf, long_action = long_models[0]
                short_model, short_conf, short_action = short_models[0]

                conflict_acknowledgment = self.prompt_templates.get('conflict_acknowledgment_template', '').format(
                    model_1=long_model,
                    action_1=long_action,
                    confidence_1=long_conf,
                    model_2=short_model,
                    action_2=short_action,
                    confidence_2=short_conf,
                    resolution_reasoning="Resolution based on confidence levels and recent model performance"
                )
            else:
                conflict_acknowledgment = f"Multiple models disagree: {', '.join(conflict_details)}"

        elif len(set([action for _, _, action in long_models + short_models + wait_models])) > 1:
            # Moderate disagreement (some models suggest wait)
            conflict_severity = 'medium'
            conflicting_models = [model.lower() for model, _, _ in long_models + short_models + wait_models]
            conflict_acknowledgment = f"Mixed signals detected: {', '.join(conflict_details)}"
        else:
            conflict_severity = 'low'
            conflict_acknowledgment = "Minor disagreement in model outputs"

        # Get performance context for resolution
        try:
            signal_tracker = getattr(self.data_store, 'signal_tracker', None)
            if signal_tracker:
                model_performance = signal_tracker.get_model_performance_breakdown()
                best_model = max(model_performance.items(), key=lambda x: x[1].get('win_rate', 0))[0] if model_performance else 'unknown'
            else:
                best_model = 'unknown'
        except:
            best_model = 'unknown'

        # Format disagreement template
        template = self.prompt_templates.get('disagreement_template', '')
        disagreement_text = template.format(
            conflicts="; ".join(conflict_details),
            conflict_details=conflict_acknowledgment,
            best_performing_model=best_model,
            market_regime="current market conditions",
            recent_pattern="recent performance pattern"
        )

        return {
            'disagreement_detected': disagreement_detected,
            'conflicting_models': conflicting_models,
            'disagreement_text': disagreement_text,
            'conflict_severity': conflict_severity,
            'conflict_acknowledgment': conflict_acknowledgment,
            'resolution_guidance': f"Consider {best_model} model performance and current market regime"
        }

    def _analyze_market_regime(self, features: MarketFeatures) -> Dict[str, Any]:
        """Analyze current market regime."""
        try:
            volatility = getattr(features, 'volatility', 0)
            price_change = getattr(features, 'price_change_1m', 0)

            # Determine regime
            if volatility > 0.02:  # High volatility
                regime = 'high_volatility'
            elif abs(price_change) > 0.01:  # Strong directional move
                regime = 'trending_up' if price_change > 0 else 'trending_down'
            else:
                regime = 'ranging'

            # Get regime context from templates
            regime_info = self.prompt_templates.get('market_regimes', {}).get(regime, {})

            return {
                'regime': regime,
                'context': regime_info.get('context', 'Unknown market regime'),
                'bias': regime_info.get('bias', 'No specific bias'),
                'risk_adjustment': regime_info.get('risk_adjustment', 'Normal risk assessment')
            }

        except Exception as e:
            logger.warning(f"Error analyzing market regime: {e}")
            return {
                'regime': 'unknown',
                'context': 'Unable to determine market regime',
                'bias': 'No bias available',
                'risk_adjustment': 'Use default risk assessment'
            }

    def _generate_performance_feedback(self) -> str:
        """Generate performance-based feedback for LLM."""
        try:
            if len(self.recent_decisions) < 3:
                return "Insufficient decision history for performance feedback"

            # Analyze recent performance
            recent_outcomes = [d.get('outcome', 'unknown') for d in self.recent_decisions[-5:]]
            wins = recent_outcomes.count('profitable')
            losses = recent_outcomes.count('stopped_out')

            if wins >= 3:
                feedback_type = "Positive Performance"
                feedback_message = f"Recent winning streak detected ({wins} wins in last {len(recent_outcomes)} decisions)"
                pattern_analysis = "Models are performing well in current market conditions"
                adaptive_guidance = "Maintain current strategy with slightly increased confidence"
            elif losses >= 3:
                feedback_type = "Performance Warning"
                feedback_message = f"Recent losses detected ({losses} losses in last {len(recent_outcomes)} decisions)"
                pattern_analysis = "Models may be struggling with current market conditions"
                adaptive_guidance = "Exercise increased caution and consider reducing position sizes"
            else:
                feedback_type = "Mixed Performance"
                feedback_message = f"Mixed results: {wins} wins, {losses} losses in last {len(recent_outcomes)} decisions"
                pattern_analysis = "Performance is variable - market conditions may be challenging"
                adaptive_guidance = "Maintain standard risk management protocols"

            template = self.prompt_templates.get('performance_feedback_template', '{feedback_type}: {feedback_message}')
            return template.format(
                feedback_type=feedback_type,
                feedback_message=feedback_message,
                pattern_analysis=pattern_analysis,
                adaptive_guidance=adaptive_guidance
            )

        except Exception as e:
            logger.warning(f"Error generating performance feedback: {e}")
            return "Performance feedback unavailable"

    def _generate_self_assessment_guidance(self, disagreement_analysis: Dict[str, Any], performance_context: str) -> str:
        """Generate self-assessment guidance for LLM meta-reasoning."""
        try:
            guidance_parts = []

            # Conflict assessment guidance
            if disagreement_analysis['disagreement_detected']:
                severity = disagreement_analysis['conflict_severity']
                if severity == 'high':
                    guidance_parts.append("CRITICAL: Strong model disagreement detected - provide explicit conflict resolution")
                    guidance_parts.append("Required: Acknowledge conflicting models by name and confidence levels")
                    guidance_parts.append("Conviction: Reduce conviction score due to conflicting signals")
                elif severity == 'medium':
                    guidance_parts.append("MODERATE: Mixed model signals - explain reasoning for chosen direction")
                    guidance_parts.append("Conviction: Medium conviction appropriate given mixed signals")
                else:
                    guidance_parts.append("MINOR: Slight model disagreement - acknowledge in reasoning")
            else:
                guidance_parts.append("CONSENSUS: Models are in agreement - higher conviction appropriate")

            # Performance-based guidance
            if "winning streak" in performance_context.lower():
                guidance_parts.append("Performance: Recent wins support increased confidence")
            elif "losing streak" in performance_context.lower():
                guidance_parts.append("Performance: Recent losses warrant reduced confidence and caution")
            elif "mixed" in performance_context.lower():
                guidance_parts.append("Performance: Mixed results suggest moderate confidence levels")

            # Meta-reasoning requirements
            guidance_parts.append("Meta-reasoning: Include conviction score (1-5 stars) and reasoning quality assessment")
            guidance_parts.append("Self-awareness: Comment on decision strength and any limiting factors")

            return "\n".join(f"• {part}" for part in guidance_parts)

        except Exception as e:
            logger.warning(f"Error generating self-assessment guidance: {e}")
            return "Self-assessment guidance unavailable"

    def _format_market_regime_context(self, market_regime: Dict[str, Any]) -> str:
        """Format market regime context for prompt inclusion."""
        try:
            regime = market_regime.get('regime', 'unknown')
            context = market_regime.get('context', 'Unknown market regime')
            bias = market_regime.get('bias', 'No specific bias')

            # Get regime-specific template
            regime_templates = self.prompt_templates.get('market_regimes', {})
            regime_info = regime_templates.get(regime, {})

            adaptive_tone = regime_info.get('adaptive_tone', 'Standard market conditions')
            memory_context = regime_info.get('memory_context', 'No historical context available')

            # Format with placeholder values for now (could be enhanced with actual timing data)
            formatted_memory = memory_context.format(
                trend_duration="recent period",
                trend_strength="moderate",
                range_duration="current session",
                trend_reliability=60,
                volatility_duration="ongoing",
                stability_duration="current period"
            )

            return f"""
Regime: {regime.upper()}
Context: {context}
Bias: {bias}
Adaptive Tone: {adaptive_tone}
Memory: {formatted_memory}
"""

        except Exception as e:
            logger.warning(f"Error formatting market regime context: {e}")
            return "Market regime context unavailable"

    def _determine_price_trend(self, features: MarketFeatures) -> str:
        """Determine price trend from features."""
        try:
            price_change = getattr(features, 'price_change_1m', 0)
            if price_change > 0.005:  # 0.5% up
                return "Upward"
            elif price_change < -0.005:  # 0.5% down
                return "Downward"
            else:
                return "Sideways"
        except:
            return "Unknown"

    def _determine_volatility_level(self, features: MarketFeatures) -> str:
        """Determine volatility level."""
        try:
            volatility = getattr(features, 'volatility', 0)
            if volatility > 0.02:
                return "High"
            elif volatility > 0.01:
                return "Medium"
            else:
                return "Low"
        except:
            return "Unknown"

    def _analyze_volume_context(self, features: MarketFeatures) -> str:
        """Analyze volume context."""
        try:
            buy_ratio = getattr(features, 'buy_volume_ratio', 0.5)
            if buy_ratio > 0.6:
                return "Strong buying pressure"
            elif buy_ratio < 0.4:
                return "Strong selling pressure"
            else:
                return "Balanced volume"
        except:
            return "Volume data unavailable"

    def _get_fallback_context(self, signal: TradingSignal, features: MarketFeatures) -> Dict[str, Any]:
        """Fallback context when enhanced analytics fail."""
        return {
            'market_context': {
                'symbol': signal.symbol,
                'current_price': features.last_price,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
            },
            'signal_context': {
                'action': signal.action,
                'confidence': signal.confidence,
                'reasoning': signal.reasoning
            },
            'model_outputs': "Model outputs unavailable",
            'performance_context': "Performance context unavailable",
            'disagreement_analysis': "",
            'market_regime': {'regime': 'unknown', 'context': 'Unknown'},
            'performance_feedback': "Performance feedback unavailable",
            'price_trend': "Unknown",
            'volatility_level': "Unknown",
            'volume_context': "Unknown"
        }

    async def _call_llm_api(self, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Call LLM API with context data."""
        try:
            start_time = time.time()

            # Prepare prompt
            prompt = self._build_prompt(context)

            # Get system prompt from templates
            system_prompt = self.prompt_templates.get('system_prompt', 'You are a trading AI assistant.')

            # Prepare API request
            payload = {
                "model": "phi-3.1-mini",
                "messages": [
                    {
                        "role": "system",
                        "content": system_prompt.format(symbol=context.get('market_context', {}).get('symbol', 'UNKNOWN'))
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": self.llm_config['max_tokens'],
                "temperature": self.llm_config['temperature']
            }

            # Make API call
            timeout = aiohttp.ClientTimeout(total=self.llm_config['timeout'])
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(self.api_url, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()

                        # Update statistics
                        response_time = time.time() - start_time
                        self.stats['api_calls'] += 1
                        self.stats['avg_response_time'] = (
                            (self.stats['avg_response_time'] * (self.stats['api_calls'] - 1) + response_time) /
                            self.stats['api_calls']
                        )

                        return result
                    else:
                        # Mark server as unavailable on error
                        self.server_available = False
                        self.consecutive_failures += 1

                        # Only log first few errors to avoid spam
                        if self.consecutive_failures <= 2:
                            logger.error(f"LLM API error: {response.status}")

                        self.stats['errors'] += 1
                        return None

        except asyncio.TimeoutError:
            self.server_available = False
            self.consecutive_failures += 1

            if self.consecutive_failures <= 2:
                logger.error("LLM API timeout")

            self.stats['errors'] += 1
            return None
        except Exception as e:
            self.server_available = False
            self.consecutive_failures += 1

            if self.consecutive_failures <= 2:
                logger.error(f"LLM API call error: {e}")

            self.stats['errors'] += 1
            return None

    def _build_prompt(self, context: Dict[str, Any]) -> str:
        """Build dynamic prompt using templates and context."""
        try:
            # Get the main trading decision template
            template = self.prompt_templates.get('trading_decision_prompt', '')

            if not template:
                # Fallback to basic template
                return self._build_fallback_prompt(context)

            # Extract context components
            market = context['market_context']
            signal = context['signal_context']

            # Format the main prompt using the template
            prompt = template.format(
                symbol=market['symbol'],
                timestamp=market['timestamp'],
                current_price=market['current_price'],
                model_outputs=context['model_outputs'],
                performance_context=context['performance_context'],
                price_trend=context['price_trend'],
                volatility_level=context['volatility_level'],
                volume_context=context['volume_context'],
                disagreement_analysis=context['disagreement_analysis'],
                performance_feedback=context['performance_feedback']
            )

            return prompt

        except Exception as e:
            logger.error(f"Error building dynamic prompt: {e}")
            return self._build_fallback_prompt(context)

    def _build_fallback_prompt(self, context: Dict[str, Any]) -> str:
        """Build fallback prompt when template system fails."""
        market = context['market_context']
        signal = context['signal_context']

        return f"""
MARKET ANALYSIS REQUEST for {market['symbol']}
Current Time: {market['timestamp']}
Current Price: ${market['current_price']:.2f}

AI MODEL OUTPUTS:
{context['model_outputs']}

PERFORMANCE CONTEXT:
{context['performance_context']}

{context['disagreement_analysis']}

{context['performance_feedback']}

Based on this analysis, provide your trading decision as a JSON object with:
- final_decision: "LONG", "SHORT", or "WAIT"
- confidence: integer between 0-100
- reasoning: brief explanation (2-3 sentences)
- risk_assessment: "LOW", "MEDIUM", or "HIGH"
- model_consensus: "STRONG", "WEAK", or "CONFLICTED"
"""

    async def _parse_llm_response(self, response: Dict[str, Any], signal: TradingSignal) -> Optional[LLMDecision]:
        """Parse LLM API response into enhanced decision structure with validation."""
        try:
            # Extract content from response
            content = response.get('choices', [{}])[0].get('message', {}).get('content', '')

            if not content:
                logger.warning("Empty content in LLM response")
                return self._create_fallback_decision(signal, "Empty LLM response")

            # Try to parse JSON from content
            json_content = self._extract_json_from_content(content)
            if not json_content:
                logger.error("No valid JSON found in LLM response")
                return self._create_fallback_decision(signal, "Invalid JSON format")

            # Parse JSON
            decision_data = json.loads(json_content)

            # Validate and normalize fields
            validated_data = self._validate_llm_decision_data(decision_data)
            if not validated_data:
                return self._create_fallback_decision(signal, "Invalid decision data")

            # Apply confidence adjustments based on performance
            adjusted_confidence = self._apply_confidence_adjustments(validated_data['confidence'], signal)

            # Determine model consensus strength
            model_consensus = self._determine_consensus_strength(signal)

            # Create enhanced decision with self-awareness fields
            decision = LLMDecision(
                symbol=signal.symbol,
                final_decision=validated_data['final_decision'],
                confidence=adjusted_confidence,
                reasoning=validated_data['reasoning'],
                risk_assessment=validated_data['risk_assessment'],
                model_consensus=validated_data['model_consensus'],
                timestamp=time.time(),
                disagreement_detected=validated_data['disagreement_detected'],
                conflicting_models=validated_data['conflicting_models'],
                conviction_score=validated_data['conviction_score'],
                reasoning_quality=validated_data['reasoning_quality'],
                performance_context_applied=validated_data['performance_context_applied'],
                market_regime_factor=validated_data['market_regime_factor'],
                market_regime=validated_data.get('market_regime', 'unknown'),
                performance_feedback=validated_data.get('performance_feedback', ''),
                confidence_adjustment=adjusted_confidence - validated_data['confidence']
            )

            # Track this decision for future performance feedback
            self._track_decision(decision)

            return decision

        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error in LLM response: {e}")
            return self._create_fallback_decision(signal, f"JSON parse error: {str(e)}")
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            return self._create_fallback_decision(signal, f"Parse error: {str(e)}")

    def _extract_json_from_content(self, content: str) -> Optional[str]:
        """Extract JSON content from LLM response."""
        try:
            # Try markdown code block first
            if '```json' in content:
                json_start = content.find('```json') + 7
                json_end = content.find('```', json_start)
                if json_end > json_start:
                    return content[json_start:json_end].strip()

            # Try regular JSON block
            if '{' in content and '}' in content:
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                if json_end > json_start:
                    return content[json_start:json_end]

            return None

        except Exception as e:
            logger.warning(f"Error extracting JSON: {e}")
            return None

    def _validate_llm_decision_data(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Enhanced validation for LLM decision data with self-awareness fields."""
        try:
            # Check for required fields (support both old and new format)
            final_decision = data.get('final_decision') or data.get('action')
            confidence = data.get('confidence')
            reasoning = data.get('reasoning')
            risk_assessment = data.get('risk_assessment') or data.get('risk_level')

            if not all([final_decision, confidence is not None, reasoning, risk_assessment]):
                logger.error("Missing required fields in LLM decision")
                return None

            # Normalize action
            final_decision = str(final_decision).upper()
            if final_decision not in ['LONG', 'SHORT', 'WAIT']:
                logger.warning(f"Invalid action '{final_decision}', defaulting to WAIT")
                final_decision = 'WAIT'

            # Normalize confidence (handle both 0-1 and 0-100 ranges)
            confidence = float(confidence)
            if confidence > 100:
                logger.warning(f"Confidence {confidence}% exceeds 100%, clamping to 100%")
                confidence = 100.0
            elif confidence > 1 and confidence <= 100:
                # Already in 0-100 range
                pass
            elif confidence <= 1:
                # Convert from 0-1 to 0-100 range
                confidence = confidence * 100
            else:
                logger.warning(f"Invalid confidence {confidence}, defaulting to 0")
                confidence = 0.0

            # Normalize risk assessment
            risk_assessment = str(risk_assessment).upper()
            if risk_assessment not in ['LOW', 'MEDIUM', 'HIGH']:
                logger.warning(f"Invalid risk assessment '{risk_assessment}', defaulting to HIGH")
                risk_assessment = 'HIGH'

            # Validate and normalize enhanced fields
            model_consensus = str(data.get('model_consensus', 'UNKNOWN')).upper()
            if model_consensus not in ['STRONG', 'WEAK', 'CONFLICTED']:
                model_consensus = 'UNKNOWN'

            # Validate disagreement detection
            disagreement_detected = data.get('disagreement_detected', False)
            if isinstance(disagreement_detected, str):
                disagreement_detected = disagreement_detected.lower() in ['true', 'yes', '1']

            # Validate conflicting models
            conflicting_models = data.get('conflicting_models', [])
            if isinstance(conflicting_models, str):
                conflicting_models = [m.strip() for m in conflicting_models.split(',') if m.strip()]
            elif not isinstance(conflicting_models, list):
                conflicting_models = []

            # Validate conviction score (1-5)
            conviction_score = data.get('conviction_score', 3)
            try:
                conviction_score = int(conviction_score)
                conviction_score = max(1, min(5, conviction_score))  # Clamp to 1-5
            except (ValueError, TypeError):
                conviction_score = 3

            # Validate reasoning quality
            reasoning_quality = str(data.get('reasoning_quality', 'MEDIUM')).upper()
            if reasoning_quality not in ['LOW', 'MEDIUM', 'HIGH']:
                reasoning_quality = 'MEDIUM'

            return {
                'final_decision': final_decision,
                'confidence': confidence,
                'reasoning': str(reasoning)[:1000],  # Increased limit for enhanced reasoning
                'risk_assessment': risk_assessment,
                'model_consensus': model_consensus,
                'disagreement_detected': disagreement_detected,
                'conflicting_models': conflicting_models,
                'conviction_score': conviction_score,
                'reasoning_quality': reasoning_quality,
                'performance_context_applied': data.get('performance_context_applied', False),
                'market_regime_factor': data.get('market_regime_factor', ''),
                'market_regime': data.get('market_regime', 'unknown'),
                'performance_feedback': data.get('performance_feedback', '')
            }

        except Exception as e:
            logger.error(f"Error validating enhanced LLM decision data: {e}")
            return None

    def _apply_confidence_adjustments(self, base_confidence: float, signal: TradingSignal) -> float:
        """Apply confidence adjustments based on recent performance."""
        try:
            adjusted_confidence = base_confidence

            # Get confidence adjustment rules from templates
            adjustment_rules = self.prompt_templates.get('confidence_adjustments', {})

            # Recent performance adjustment
            if len(self.recent_decisions) >= 3:
                recent_outcomes = [d.get('outcome', 'unknown') for d in self.recent_decisions[-3:]]

                # Check for winning/losing streaks
                if all(outcome == 'profitable' for outcome in recent_outcomes):
                    adjustment = adjustment_rules.get('recent_wins', {}).get('adjustment', 5)
                    adjusted_confidence += adjustment
                    logger.debug(f"Applied winning streak bonus: +{adjustment}%")
                elif all(outcome in ['stopped_out', 'expired'] for outcome in recent_outcomes):
                    adjustment = adjustment_rules.get('recent_losses', {}).get('adjustment', -10)
                    adjusted_confidence += adjustment
                    logger.debug(f"Applied losing streak penalty: {adjustment}%")

            # Model consensus adjustment
            consensus_strength = self._determine_consensus_strength(signal)
            if consensus_strength == 'WEAK':
                adjustment = adjustment_rules.get('model_disagreement', {}).get('high_conflict', -15)
                adjusted_confidence += adjustment
                logger.debug(f"Applied weak consensus penalty: {adjustment}%")

            # Clamp to valid range
            adjusted_confidence = max(0, min(100, adjusted_confidence))

            return adjusted_confidence

        except Exception as e:
            logger.warning(f"Error applying confidence adjustments: {e}")
            return base_confidence

    def _track_decision(self, decision: LLMDecision):
        """Track decision for future performance feedback."""
        try:
            decision_record = {
                'timestamp': decision.timestamp,
                'symbol': decision.symbol,
                'action': decision.final_decision,
                'confidence': decision.confidence,
                'reasoning': decision.reasoning,
                'outcome': 'pending'  # Will be updated when signal resolves
            }

            self.recent_decisions.append(decision_record)

            # Keep only last 20 decisions
            if len(self.recent_decisions) > 20:
                self.recent_decisions = self.recent_decisions[-20:]

        except Exception as e:
            logger.warning(f"Error tracking decision: {e}")

    def _create_fallback_decision(self, signal: TradingSignal, reason: str) -> LLMDecision:
        """Create fallback decision when LLM analysis fails."""
        try:
            fallback_template = self.prompt_templates.get('fallback_templates', {}).get('llm_error', {})

            return LLMDecision(
                symbol=signal.symbol,
                final_decision=fallback_template.get('final_decision', 'WAIT'),
                confidence=fallback_template.get('confidence', 0),
                reasoning=f"{fallback_template.get('reasoning', 'LLM analysis failed')}: {reason}",
                risk_assessment=fallback_template.get('risk_assessment', 'HIGH'),
                model_consensus=fallback_template.get('model_consensus', 'CONFLICTED'),
                timestamp=time.time(),
                market_regime='unknown',
                performance_feedback=f"Fallback decision due to: {reason}"
            )

        except Exception as e:
            logger.error(f"Error creating fallback decision: {e}")
            # Ultimate fallback
            return LLMDecision(
                symbol=signal.symbol,
                final_decision='WAIT',
                confidence=0,
                reasoning=f"System error: {reason}",
                risk_assessment='HIGH',
                model_consensus='CONFLICTED',
                timestamp=time.time()
            )

    def _determine_consensus_strength(self, signal: TradingSignal) -> str:
        """Determine consensus strength from model contributions."""
        contributions = signal.model_contributions
        if not contributions:
            return 'WEAK'

        # Calculate agreement level
        positive_models = sum(1 for score in contributions.values() if score > 0.3)
        negative_models = sum(1 for score in contributions.values() if score < -0.3)
        total_models = len(contributions)

        agreement_ratio = max(positive_models, negative_models) / total_models

        if agreement_ratio >= 0.8:
            return 'STRONG'
        elif agreement_ratio >= 0.6:
            return 'MODERATE'
        else:
            return 'WEAK'

    def _is_in_cooldown(self, symbol: str, current_time: float) -> bool:
        """Check if symbol is in LLM call cooldown period."""
        last_time = self.last_call_time.get(symbol, 0)
        return (current_time - last_time) < self.call_interval

    async def start_background_processing(self):
        """Start background LLM processing task."""
        if self.enabled:
            asyncio.create_task(self._periodic_analysis())
            logger.info("LLM background processing started")
        else:
            logger.info("LLM background processing skipped (disabled)")

    async def _periodic_analysis(self):
        """Periodic analysis of market conditions."""
        while True:
            try:
                await asyncio.sleep(self.call_interval)

                # Check server health first
                if not await self._check_server_health():
                    # Skip this cycle if server is not available
                    continue

                # Get symbols with recent activity
                symbols = self.config['symbols']['enabled']

                for symbol in symbols:
                    # Check if we have recent features
                    features = self.data_store.get_features(symbol)
                    if features:
                        # Get recent signals
                        recent_signals = self.data_store.get_signals(symbol, limit=1)
                        if recent_signals:
                            latest_signal = recent_signals[-1]

                            # Create mock signal for analysis
                            from models.smart_strategy import TradingSignal
                            signal = TradingSignal(
                                symbol=symbol,
                                action=latest_signal.get('action', 'WAIT'),
                                confidence=latest_signal.get('confidence', 0.5),
                                score=latest_signal.get('score', 0.0),
                                reasoning=latest_signal.get('reasoning', ''),
                                model_contributions=latest_signal.get('model_contributions', {}),
                                timestamp=time.time(),
                                price=features.last_price
                            )

                            # Process through LLM
                            await self.process_signal(signal, features)

            except Exception as e:
                logger.error(f"Error in periodic LLM analysis: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """Get LLM integration statistics."""
        return dict(self.stats)
