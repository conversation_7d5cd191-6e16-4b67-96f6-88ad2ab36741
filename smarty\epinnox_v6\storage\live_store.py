#!/usr/bin/env python3
"""
Live Data Store for Real-Time Market Data
Epinnox V6 - Standalone AI Strategy Tuner
"""

import json
import logging
import time
import asyncio
from typing import Dict, List, Optional, Any
from collections import defaultdict, deque
from dataclasses import asdict
import os

from feeds.trade_parser import MarketFeatures, TradeData

logger = logging.getLogger(__name__)

class LiveDataStore:
    """
    In-memory real-time data storage for market features and trading signals.
    Provides fast access to recent data for AI models and dashboard.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.cache_file = "storage/symbol_cache.json"

        # Feature storage
        self.features: Dict[str, MarketFeatures] = {}
        self.feature_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))

        # Signal storage
        self.signals: Dict[str, deque] = defaultdict(lambda: deque(maxlen=50))
        self.signal_timeline: deque = deque(maxlen=200)

        # LLM decision storage
        self.llm_decisions: Dict[str, deque] = defaultdict(lambda: deque(maxlen=20))

        # Performance metrics
        self.performance_metrics = {
            'total_signals': 0,
            'profitable_signals': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'per_symbol': defaultdict(lambda: {
                'total_signals': 0,
                'profitable_signals': 0,
                'total_pnl': 0.0,
                'win_rate': 0.0
            })
        }

        # Model outputs storage
        self.model_outputs: Dict[str, Dict[str, Any]] = defaultdict(dict)

        # Statistics
        self.stats = {
            'features_stored': 0,
            'signals_generated': 0,
            'llm_decisions': 0,
            'last_update': None,
            'memory_usage_mb': 0
        }

        # Load persisted state if enabled
        if config['data_storage']['persist_state']:
            self._load_state()

        logger.info("[STORE] Live Data Store initialized")

    def store_features(self, features: MarketFeatures):
        """Store market features for a symbol."""
        try:
            symbol = features.symbol

            # Store current features
            self.features[symbol] = features

            # Add to history
            self.feature_history[symbol].append(features)

            # Update statistics
            self.stats['features_stored'] += 1
            self.stats['last_update'] = time.time()

            logger.debug(f"Stored features for {symbol}: price={features.last_price}")

        except Exception as e:
            logger.error(f"Error storing features: {e}")

    def store_signal(self, signal_data: Dict[str, Any]):
        """Store a trading signal."""
        try:
            symbol = signal_data.get('symbol', 'UNKNOWN')
            timestamp = signal_data.get('timestamp', time.time())

            # Add formatted time
            signal_data['formatted_time'] = time.strftime('%H:%M:%S', time.localtime(timestamp))

            # Add current price for charting if available
            if symbol in self.features and self.features[symbol]:
                signal_data['price'] = self.features[symbol].last_price

            # Add timestamp in milliseconds for Chart.js
            signal_data['timestamp_ms'] = int(timestamp * 1000)

            # Store in symbol-specific queue
            self.signals[symbol].append(signal_data)

            # Store in global timeline
            self.signal_timeline.append(signal_data)

            # Update performance metrics
            self._update_performance_metrics(signal_data)

            # Update statistics
            self.stats['signals_generated'] += 1

            logger.info(f"[SIGNAL] Signal stored: {symbol} - {signal_data.get('action', 'UNKNOWN')}")

        except Exception as e:
            logger.error(f"Error storing signal: {e}")

    def store_llm_decision(self, decision_data: Dict[str, Any]):
        """Store an LLM decision."""
        try:
            symbol = decision_data.get('symbol', 'UNKNOWN')
            timestamp = decision_data.get('timestamp', time.time())

            # Add formatted time
            decision_data['formatted_time'] = time.strftime('%H:%M:%S', time.localtime(timestamp))

            # Store in symbol-specific queue
            self.llm_decisions[symbol].append(decision_data)

            # Update statistics
            self.stats['llm_decisions'] += 1

            logger.info(f"🧠 LLM decision stored: {symbol} - {decision_data.get('action', 'UNKNOWN')}")

        except Exception as e:
            logger.error(f"Error storing LLM decision: {e}")

    def store_model_output(self, symbol: str, model_name: str, output_data: Dict[str, Any]):
        """Store AI model output."""
        try:
            output_data['timestamp'] = time.time()
            output_data['symbol'] = symbol

            self.model_outputs[symbol][model_name] = output_data

            logger.debug(f"Model output stored: {symbol} - {model_name}")

        except Exception as e:
            logger.error(f"Error storing model output: {e}")

    def get_features(self, symbol: str) -> Optional[MarketFeatures]:
        """Get latest features for a symbol."""
        return self.features.get(symbol)

    def get_feature_history(self, symbol: str, limit: int = 50) -> List[MarketFeatures]:
        """Get feature history for a symbol."""
        history = list(self.feature_history.get(symbol, []))
        return history[-limit:] if limit else history

    def get_signals(self, symbol: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent signals for a symbol or all symbols."""
        if symbol:
            signals = list(self.signals.get(symbol, []))
            return signals[-limit:] if limit else signals
        else:
            # Return from global timeline
            signals = list(self.signal_timeline)
            return signals[-limit:] if limit else signals

    def get_llm_decisions(self, symbol: str = None, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent LLM decisions for a symbol or all symbols."""
        if symbol:
            decisions = list(self.llm_decisions.get(symbol, []))
            return decisions[-limit:] if limit else decisions
        else:
            # Combine all decisions and sort by timestamp
            all_decisions = []
            for symbol_decisions in self.llm_decisions.values():
                all_decisions.extend(symbol_decisions)

            all_decisions.sort(key=lambda x: x.get('timestamp', 0), reverse=True)
            return all_decisions[:limit] if limit else all_decisions

    def get_model_outputs(self, symbol: str) -> Dict[str, Any]:
        """Get latest model outputs for a symbol."""
        return dict(self.model_outputs.get(symbol, {}))

    def get_performance_metrics(self, symbol: str = None) -> Dict[str, Any]:
        """Get performance metrics for a symbol or overall."""
        if symbol:
            return dict(self.performance_metrics['per_symbol'].get(symbol, {}))
        else:
            return dict(self.performance_metrics)

    def get_dashboard_data(self, symbol: str) -> Dict[str, Any]:
        """Get all data needed for dashboard display."""
        return {
            'timestamp': time.time(),
            'current_symbol': symbol,
            'strategy_running': True,  # Assume running if data is being stored
            'model_outputs': self.get_model_outputs(symbol),
            'recent_signals': self.get_signals(symbol, limit=10),
            'signal_timeline': self.get_signals(limit=20),
            'llm_decisions': self.get_llm_decisions(symbol, limit=5),
            'performance': self.get_performance_metrics(symbol),
            'features': asdict(self.features[symbol]) if symbol in self.features else {},
            'stats': self.get_stats()
        }

    def _update_performance_metrics(self, signal_data: Dict[str, Any]):
        """Update performance metrics with new signal."""
        symbol = signal_data.get('symbol', 'UNKNOWN')
        pnl = signal_data.get('pnl', 0.0)

        # Update global metrics
        self.performance_metrics['total_signals'] += 1
        if pnl > 0:
            self.performance_metrics['profitable_signals'] += 1
        self.performance_metrics['total_pnl'] += pnl

        # Calculate win rate
        total = self.performance_metrics['total_signals']
        profitable = self.performance_metrics['profitable_signals']
        self.performance_metrics['win_rate'] = profitable / total if total > 0 else 0.0

        # Update per-symbol metrics - ensure symbol exists
        if symbol not in self.performance_metrics['per_symbol']:
            self.performance_metrics['per_symbol'][symbol] = {
                'total_signals': 0,
                'profitable_signals': 0,
                'total_pnl': 0.0,
                'win_rate': 0.0
            }

        symbol_metrics = self.performance_metrics['per_symbol'][symbol]
        symbol_metrics['total_signals'] += 1
        if pnl > 0:
            symbol_metrics['profitable_signals'] += 1
        symbol_metrics['total_pnl'] += pnl

        # Calculate symbol win rate
        symbol_total = symbol_metrics['total_signals']
        symbol_profitable = symbol_metrics['profitable_signals']
        symbol_metrics['win_rate'] = symbol_profitable / symbol_total if symbol_total > 0 else 0.0

    def cleanup_old_data(self):
        """Clean up old data to manage memory usage."""
        try:
            current_time = time.time()
            cutoff_time = current_time - 3600  # Keep last hour of data

            # Clean up old features from history
            for symbol in self.feature_history:
                history = self.feature_history[symbol]
                # Keep only recent features
                while history and history[0].timestamp < cutoff_time * 1000:
                    history.popleft()

            logger.info("Old data cleanup completed")

        except Exception as e:
            logger.error(f"Error during data cleanup: {e}")

    def _load_state(self):
        """Load persisted state from cache file."""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r') as f:
                    state = json.load(f)

                # Load performance metrics
                if 'performance_metrics' in state:
                    self.performance_metrics.update(state['performance_metrics'])

                logger.info("State loaded from cache file")

        except Exception as e:
            logger.error(f"Error loading state: {e}")

    def _save_state(self):
        """Save current state to cache file."""
        try:
            if not self.config['data_storage']['persist_state']:
                return

            # Ensure directory exists
            os.makedirs(os.path.dirname(self.cache_file), exist_ok=True)

            state = {
                'performance_metrics': self.performance_metrics,
                'stats': self.stats,
                'timestamp': time.time()
            }

            with open(self.cache_file, 'w') as f:
                json.dump(state, f, indent=2, default=str)

            logger.debug("State saved to cache file")

        except Exception as e:
            logger.error(f"Error saving state: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        # Calculate memory usage estimate
        memory_usage = 0
        memory_usage += len(str(self.features)) * 8  # Rough estimate
        memory_usage += len(str(self.signals)) * 8
        memory_usage += len(str(self.llm_decisions)) * 8
        memory_usage += len(str(self.model_outputs)) * 8

        self.stats['memory_usage_mb'] = memory_usage / (1024 * 1024)

        return {
            **self.stats,
            'symbols_with_features': len(self.features),
            'symbols_with_signals': len(self.signals),
            'symbols_with_llm_decisions': len(self.llm_decisions),
            'total_signal_timeline': len(self.signal_timeline)
        }

    async def start_background_tasks(self):
        """Start background maintenance tasks."""
        asyncio.create_task(self._periodic_cleanup())
        asyncio.create_task(self._periodic_save())
        logger.info("Background tasks started")

    async def _periodic_cleanup(self):
        """Periodic data cleanup task."""
        while True:
            try:
                await asyncio.sleep(self.config['data_storage']['memory_cleanup_interval'])
                self.cleanup_old_data()
            except Exception as e:
                logger.error(f"Error in periodic cleanup: {e}")

    async def _periodic_save(self):
        """Periodic state saving task."""
        while True:
            try:
                await asyncio.sleep(300)  # Save every 5 minutes
                self._save_state()
            except Exception as e:
                logger.error(f"Error in periodic save: {e}")

    def shutdown(self):
        """Shutdown and save final state."""
        self._save_state()
        logger.info("Live Data Store shutdown complete")
