#!/usr/bin/env python3
"""
AI Strategy Tuner - Dedicated Interface for Smart Model Integrated Strategy

A lightweight, focused tool for real-time parameter tuning and AI decision monitoring
specifically designed for the Smart Model Integrated strategy (orchestrator.py).

Features:
- Real-time parameter modification
- Live AI model output visualization
- LLM reasoning display
- Performance tracking
- Model contribution analysis
"""

import asyncio
import json
import logging
import sqlite3
import subprocess
import sys
import time
import yaml
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import aiohttp
from aiohttp import web, WSMsgType
import aiohttp_cors

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AIStrategyTuner:
    """Dedicated interface for Smart Model Integrated strategy tuning and monitoring."""

    def __init__(self):
        self.config_path = "config.yaml"
        self.bus_path = "data/bus.db"
        self.orchestrator_process = None
        self.config = {}
        self.websocket_clients = set()
        self.last_update_time = time.time()

        # Strategy state tracking
        self.strategy_running = False
        self.model_outputs = {}
        self.llm_decisions = []
        self.performance_metrics = {
            "total_signals": 0,
            "profitable_signals": 0,
            "total_pnl": 0.0,
            "win_rate": 0.0,
            "model_accuracy": {}
        }

        # Load initial configuration
        self.load_config()

    def load_config(self):
        """Load configuration from config.yaml."""
        try:
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)
            logger.info("✅ Configuration loaded successfully")
        except Exception as e:
            logger.error(f"❌ Failed to load configuration: {e}")
            # Create default config if not found
            self.create_default_config()

    def create_default_config(self):
        """Create a default configuration for the AI strategy."""
        self.config = {
            "llm": {
                "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\lmstudio-community\\Phi-3.1-mini-128k-instruct-GGUF\\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf",
                "prompt_path": "llm/prompts/trading_prompt_phi.yaml",
                "n_ctx": 2048,
                "n_threads": 4,
                "n_gpu_layers": 0,
                "max_tokens": 128,
                "temperature": 0.0,
                "call_interval_s": 30,
                "dummy_mode": False
            },
            "ensemble_model_config": {
                "model_weights": {
                    "rsi": 1.0,
                    "orderflow": 1.5,
                    "volatility_regime": 1.2,
                    "vwap_deviation": 1.0,
                    "liquidity_imbalance": 1.0,
                    "garch_volatility": 1.3,
                    "funding_momentum": 1.2,
                    "open_interest_momentum": 1.1,
                    "social_sentiment": 0.8
                },
                "performance_window": 24,
                "min_weight": 0.1,
                "max_weight": 3.0,
                "learning_rate": 0.05
            },
            "trading": {
                "base_buy_threshold": 0.3,
                "base_sell_threshold": -0.3,
                "max_position_size": 1000.0,
                "stop_loss_pct": 2.0,
                "simulation_mode": True
            }
        }
        self.save_config()

    def save_config(self):
        """Save current configuration to config.yaml."""
        try:
            with open(self.config_path, 'w') as f:
                yaml.dump(self.config, f, default_flow_style=False, indent=2)
            logger.info("✅ Configuration saved successfully")
        except Exception as e:
            logger.error(f"❌ Failed to save configuration: {e}")

    async def start_strategy(self):
        """Start the Smart Model Integrated strategy (orchestrator)."""
        if self.strategy_running:
            return {"success": False, "message": "Strategy already running"}

        try:
            logger.info("🚀 Starting Smart Model Integrated strategy...")

            # Start orchestrator with debug mode
            self.orchestrator_process = subprocess.Popen(
                [sys.executable, "orchestrator.py", "--debug"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Wait for startup
            await asyncio.sleep(3)

            if self.orchestrator_process.poll() is None:
                self.strategy_running = True
                logger.info(f"✅ Strategy started successfully (PID: {self.orchestrator_process.pid})")
                return {
                    "success": True,
                    "message": "Smart Model Integrated strategy started",
                    "pid": self.orchestrator_process.pid
                }
            else:
                stdout, stderr = self.orchestrator_process.communicate()
                logger.error(f"❌ Strategy failed to start")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                return {"success": False, "message": "Strategy failed to start"}

        except Exception as e:
            logger.error(f"❌ Error starting strategy: {e}")
            return {"success": False, "message": str(e)}

    async def stop_strategy(self):
        """Stop the Smart Model Integrated strategy."""
        if not self.strategy_running or not self.orchestrator_process:
            return {"success": False, "message": "Strategy not running"}

        try:
            self.orchestrator_process.terminate()
            await asyncio.sleep(2)

            if self.orchestrator_process.poll() is None:
                self.orchestrator_process.kill()

            self.strategy_running = False
            self.orchestrator_process = None
            logger.info("✅ Strategy stopped successfully")
            return {"success": True, "message": "Strategy stopped"}

        except Exception as e:
            logger.error(f"❌ Error stopping strategy: {e}")
            return {"success": False, "message": str(e)}

    def get_live_data(self) -> Dict[str, Any]:
        """Get live data from the SQLite bus."""
        try:
            conn = sqlite3.connect(self.bus_path, timeout=1.0)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get recent signals and model outputs
            cursor.execute("""
                SELECT ts, stream, payload FROM messages
                WHERE ts > ?
                ORDER BY ts DESC LIMIT 50
            """, (time.time() - 300,))  # Last 5 minutes

            messages = cursor.fetchall()
            conn.close()

            # Parse messages for AI model outputs
            model_data = {}
            signals = []
            llm_data = []

            for row in messages:
                try:
                    payload = json.loads(row['payload']) if isinstance(row['payload'], str) else row['payload']
                    stream = row['stream']

                    if 'model' in stream:
                        model_name = stream.split('.')[-1]
                        model_data[model_name] = payload
                    elif 'signal' in stream:
                        signals.append(payload)
                    elif 'llm' in stream:
                        llm_data.append(payload)

                except Exception:
                    continue

            return {
                "timestamp": datetime.now().isoformat(),
                "strategy_running": self.strategy_running,
                "model_outputs": model_data,
                "recent_signals": signals[:10],
                "llm_decisions": llm_data[:5],
                "performance": self.performance_metrics
            }

        except Exception as e:
            logger.error(f"Error getting live data: {e}")
            return {
                "timestamp": datetime.now().isoformat(),
                "strategy_running": self.strategy_running,
                "error": str(e)
            }

    async def update_parameter(self, category: str, parameter: str, value: Any):
        """Update a strategy parameter in real-time."""
        try:
            # Update configuration
            if category == "llm":
                self.config["llm"][parameter] = value
            elif category == "model_weights":
                self.config["ensemble_model_config"]["model_weights"][parameter] = value
            elif category == "trading":
                self.config["trading"][parameter] = value
            elif category == "ensemble":
                self.config["ensemble_model_config"][parameter] = value

            # Save configuration
            self.save_config()

            # If strategy is running, we could implement hot-reloading here
            # For now, we'll just update the config file

            logger.info(f"✅ Updated {category}.{parameter} = {value}")
            return {"success": True, "message": f"Parameter {parameter} updated"}

        except Exception as e:
            logger.error(f"❌ Error updating parameter: {e}")
            return {"success": False, "message": str(e)}

    async def broadcast_update(self, data: Dict[str, Any]):
        """Broadcast updates to all connected WebSocket clients."""
        if not self.websocket_clients:
            return

        message = json.dumps(data)
        disconnected_clients = set()

        for ws in self.websocket_clients:
            try:
                await ws.send_str(message)
            except Exception:
                disconnected_clients.add(ws)

        # Remove disconnected clients
        self.websocket_clients -= disconnected_clients

    async def start_server(self, host: str = "localhost", port: int = 8084):
        """Start the AI Strategy Tuner web server."""
        app = web.Application()

        # Setup CORS
        cors = aiohttp_cors.setup(app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })

        # Routes
        app.router.add_get('/', self.serve_dashboard)
        app.router.add_get('/ws', self.websocket_handler)
        app.router.add_get('/api/status', self.api_status)
        app.router.add_get('/api/config', self.api_get_config)
        app.router.add_post('/api/config', self.api_update_config)
        app.router.add_post('/api/strategy/start', self.api_start_strategy)
        app.router.add_post('/api/strategy/stop', self.api_stop_strategy)
        app.router.add_get('/api/data', self.api_get_data)

        # Add CORS to all routes
        for route in list(app.router.routes()):
            cors.add(route)

        # Start background data updater
        asyncio.create_task(self.background_updater())

        logger.info(f"🌐 AI Strategy Tuner starting on http://{host}:{port}")

        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, host, port)
        await site.start()

        logger.info(f"✅ AI Strategy Tuner running at http://{host}:{port}")
        logger.info("🎯 Focus: Smart Model Integrated Strategy Tuning & Monitoring")

        # Keep running
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("🛑 AI Strategy Tuner shutting down...")
        finally:
            if self.strategy_running:
                await self.stop_strategy()
            await runner.cleanup()

    async def serve_dashboard(self, request):
        """Serve the main dashboard HTML."""
        html = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 AI Strategy Tuner - Smart Model Integrated</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        :root {
            --bg-primary: #0a0e1a;
            --bg-secondary: #1a1f2e;
            --bg-card: #2a2f3e;
            --accent-blue: #3498db;
            --accent-green: #00d4aa;
            --accent-red: #e74c3c;
            --accent-gold: #d4af37;
            --text-primary: #ffffff;
            --text-secondary: #b0bec5;
            --text-muted: #78909c;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            background: var(--bg-card);
            padding: 1rem 2rem;
            border-bottom: 2px solid var(--accent-blue);
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .header h1 {
            color: var(--accent-gold);
            font-size: 1.8rem;
            font-weight: 600;
        }

        .strategy-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-start {
            background: var(--accent-green);
            color: white;
        }

        .btn-stop {
            background: var(--accent-red);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 500;
        }

        .status-running {
            background: rgba(0, 212, 170, 0.2);
            color: var(--accent-green);
            border: 1px solid var(--accent-green);
        }

        .status-stopped {
            background: rgba(231, 76, 60, 0.2);
            color: var(--accent-red);
            border: 1px solid var(--accent-red);
        }

        .main-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            padding: 2rem;
            max-width: 1600px;
            margin: 0 auto;
        }

        .panel {
            background: var(--bg-card);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            border: 1px solid rgba(255,255,255,0.1);
        }

        .panel h2 {
            color: var(--accent-blue);
            margin-bottom: 1rem;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .parameter-group {
            margin-bottom: 1.5rem;
        }

        .parameter-group h3 {
            color: var(--accent-gold);
            margin-bottom: 0.8rem;
            font-size: 1.1rem;
        }

        .parameter-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.8rem;
            padding: 0.5rem;
            background: rgba(255,255,255,0.05);
            border-radius: 6px;
        }

        .parameter-label {
            color: var(--text-secondary);
            font-weight: 500;
        }

        .parameter-input {
            background: var(--bg-primary);
            border: 1px solid rgba(255,255,255,0.2);
            color: var(--text-primary);
            padding: 0.3rem 0.6rem;
            border-radius: 4px;
            width: 100px;
            text-align: center;
        }

        .parameter-input:focus {
            outline: none;
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .model-output {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            background: rgba(255,255,255,0.05);
            border-radius: 6px;
            border-left: 4px solid var(--accent-blue);
        }

        .model-name {
            color: var(--text-primary);
            font-weight: 500;
        }

        .model-value {
            color: var(--accent-green);
            font-weight: 600;
        }

        .confidence-bar {
            width: 60px;
            height: 6px;
            background: rgba(255,255,255,0.1);
            border-radius: 3px;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-red), var(--accent-gold), var(--accent-green));
            transition: width 0.3s ease;
        }

        .llm-decision {
            background: rgba(212, 175, 55, 0.1);
            border: 1px solid var(--accent-gold);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .llm-timestamp {
            color: var(--text-muted);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .llm-reasoning {
            color: var(--text-primary);
            line-height: 1.5;
        }

        .performance-metric {
            display: flex;
            justify-content: space-between;
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            background: rgba(255,255,255,0.05);
            border-radius: 6px;
        }

        .metric-label {
            color: var(--text-secondary);
        }

        .metric-value {
            color: var(--accent-green);
            font-weight: 600;
        }

        .chart-container {
            height: 300px;
            margin-top: 1rem;
        }

        @media (max-width: 1200px) {
            .main-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 AI Strategy Tuner</h1>
        <div class="strategy-controls">
            <div id="status-indicator" class="status-indicator status-stopped">
                <span>●</span>
                <span>Strategy Stopped</span>
            </div>
            <button id="start-btn" class="btn btn-start">Start Strategy</button>
            <button id="stop-btn" class="btn btn-stop">Stop Strategy</button>
        </div>
    </div>

    <div class="main-container">
        <!-- Parameter Tuning Panel -->
        <div class="panel">
            <h2>⚙️ Strategy Parameters</h2>

            <div class="parameter-group">
                <h3>Model Weights</h3>
                <div id="model-weights"></div>
            </div>

            <div class="parameter-group">
                <h3>Trading Thresholds</h3>
                <div id="trading-params"></div>
            </div>

            <div class="parameter-group">
                <h3>LLM Settings</h3>
                <div id="llm-params"></div>
            </div>
        </div>

        <!-- AI Output Visualization Panel -->
        <div class="panel">
            <h2>🤖 AI Model Outputs</h2>
            <div id="model-outputs"></div>

            <h2 style="margin-top: 2rem;">🧠 LLM Decisions</h2>
            <div id="llm-decisions"></div>
        </div>

        <!-- Performance Metrics Panel -->
        <div class="panel">
            <h2>📊 Performance Metrics</h2>
            <div id="performance-metrics"></div>
            <div class="chart-container">
                <canvas id="performance-chart"></canvas>
            </div>
        </div>

        <!-- Signal Timeline Panel -->
        <div class="panel">
            <h2>📈 Signal Timeline</h2>
            <div id="signal-timeline"></div>
            <div class="chart-container">
                <canvas id="signal-chart"></canvas>
            </div>
        </div>
    </div>

    <script>
        // WebSocket connection for real-time updates
        let ws = null;
        let performanceChart = null;
        let signalChart = null;

        function connectWebSocket() {
            ws = new WebSocket(`ws://${window.location.host}/ws`);

            ws.onopen = function() {
                console.log('WebSocket connected');
            };

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                updateDashboard(data);
            };

            ws.onclose = function() {
                console.log('WebSocket disconnected, reconnecting...');
                setTimeout(connectWebSocket, 3000);
            };
        }

        function updateDashboard(data) {
            // Update status indicator
            const statusIndicator = document.getElementById('status-indicator');
            if (data.strategy_running) {
                statusIndicator.className = 'status-indicator status-running';
                statusIndicator.innerHTML = '<span>●</span><span>Strategy Running</span>';
            } else {
                statusIndicator.className = 'status-indicator status-stopped';
                statusIndicator.innerHTML = '<span>●</span><span>Strategy Stopped</span>';
            }

            // Update model outputs
            if (data.model_outputs) {
                updateModelOutputs(data.model_outputs);
            }

            // Update LLM decisions
            if (data.llm_decisions) {
                updateLLMDecisions(data.llm_decisions);
            }

            // Update performance metrics
            if (data.performance) {
                updatePerformanceMetrics(data.performance);
            }
        }

        function updateModelOutputs(outputs) {
            const container = document.getElementById('model-outputs');
            container.innerHTML = '';

            Object.entries(outputs).forEach(([model, data]) => {
                const div = document.createElement('div');
                div.className = 'model-output';

                const confidence = data.confidence || Math.random();

                div.innerHTML = `
                    <span class="model-name">${model}</span>
                    <span class="model-value">${data.signal || data.action || 'N/A'}</span>
                    <div class="confidence-bar">
                        <div class="confidence-fill" style="width: ${confidence * 100}%"></div>
                    </div>
                `;

                container.appendChild(div);
            });
        }

        function updateLLMDecisions(decisions) {
            const container = document.getElementById('llm-decisions');
            container.innerHTML = '';

            decisions.slice(0, 3).forEach(decision => {
                const div = document.createElement('div');
                div.className = 'llm-decision';

                div.innerHTML = `
                    <div class="llm-timestamp">${new Date(decision.timestamp || Date.now()).toLocaleTimeString()}</div>
                    <div class="llm-reasoning">${decision.reasoning || decision.message || 'Processing...'}</div>
                `;

                container.appendChild(div);
            });
        }

        function updatePerformanceMetrics(metrics) {
            const container = document.getElementById('performance-metrics');
            container.innerHTML = '';

            const metricsToShow = [
                { label: 'Total Signals', value: metrics.total_signals || 0 },
                { label: 'Win Rate', value: `${(metrics.win_rate * 100 || 0).toFixed(1)}%` },
                { label: 'Total P&L', value: `$${(metrics.total_pnl || 0).toFixed(2)}` },
                { label: 'Profitable Signals', value: metrics.profitable_signals || 0 }
            ];

            metricsToShow.forEach(metric => {
                const div = document.createElement('div');
                div.className = 'performance-metric';
                div.innerHTML = `
                    <span class="metric-label">${metric.label}</span>
                    <span class="metric-value">${metric.value}</span>
                `;
                container.appendChild(div);
            });
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            connectWebSocket();
            loadConfiguration();

            // Setup event listeners
            document.getElementById('start-btn').addEventListener('click', startStrategy);
            document.getElementById('stop-btn').addEventListener('click', stopStrategy);
        });

        async function loadConfiguration() {
            try {
                const response = await fetch('/api/config');
                const config = await response.json();
                populateParameters(config);
            } catch (error) {
                console.error('Error loading configuration:', error);
            }
        }

        function populateParameters(config) {
            // Populate model weights
            const modelWeightsContainer = document.getElementById('model-weights');
            if (config.ensemble_model_config && config.ensemble_model_config.model_weights) {
                Object.entries(config.ensemble_model_config.model_weights).forEach(([model, weight]) => {
                    const div = document.createElement('div');
                    div.className = 'parameter-row';
                    div.innerHTML = `
                        <span class="parameter-label">${model}</span>
                        <input type="number" class="parameter-input" value="${weight}"
                               step="0.1" min="0" max="5"
                               onchange="updateParameter('model_weights', '${model}', this.value)">
                    `;
                    modelWeightsContainer.appendChild(div);
                });
            }

            // Populate trading parameters
            const tradingContainer = document.getElementById('trading-params');
            if (config.trading) {
                Object.entries(config.trading).forEach(([param, value]) => {
                    if (typeof value === 'number') {
                        const div = document.createElement('div');
                        div.className = 'parameter-row';
                        div.innerHTML = `
                            <span class="parameter-label">${param}</span>
                            <input type="number" class="parameter-input" value="${value}"
                                   step="0.1"
                                   onchange="updateParameter('trading', '${param}', this.value)">
                        `;
                        tradingContainer.appendChild(div);
                    }
                });
            }

            // Populate LLM parameters
            const llmContainer = document.getElementById('llm-params');
            if (config.llm) {
                const llmParams = ['call_interval_s', 'temperature', 'max_tokens', 'n_threads'];
                llmParams.forEach(param => {
                    if (config.llm[param] !== undefined) {
                        const div = document.createElement('div');
                        div.className = 'parameter-row';
                        div.innerHTML = `
                            <span class="parameter-label">${param}</span>
                            <input type="number" class="parameter-input" value="${config.llm[param]}"
                                   step="${param === 'temperature' ? '0.1' : '1'}"
                                   min="0"
                                   onchange="updateParameter('llm', '${param}', this.value)">
                        `;
                        llmContainer.appendChild(div);
                    }
                });
            }
        }

        async function updateParameter(category, parameter, value) {
            try {
                const response = await fetch('/api/config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        category: category,
                        parameter: parameter,
                        value: parseFloat(value) || value
                    })
                });

                const result = await response.json();
                if (result.success) {
                    console.log(`Updated ${category}.${parameter} = ${value}`);
                } else {
                    console.error('Error updating parameter:', result.message);
                }
            } catch (error) {
                console.error('Error updating parameter:', error);
            }
        }

        async function startStrategy() {
            try {
                const response = await fetch('/api/strategy/start', { method: 'POST' });
                const result = await response.json();
                console.log('Start strategy result:', result);
            } catch (error) {
                console.error('Error starting strategy:', error);
            }
        }

        async function stopStrategy() {
            try {
                const response = await fetch('/api/strategy/stop', { method: 'POST' });
                const result = await response.json();
                console.log('Stop strategy result:', result);
            } catch (error) {
                console.error('Error stopping strategy:', error);
            }
        }
    </script>
</body>
</html>
        """
        return web.Response(text=html, content_type='text/html')

    async def websocket_handler(self, request):
        """Handle WebSocket connections for real-time updates."""
        ws = web.WebSocketResponse()
        await ws.prepare(request)

        self.websocket_clients.add(ws)
        logger.info(f"WebSocket client connected. Total clients: {len(self.websocket_clients)}")

        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    # Handle incoming messages if needed
                    pass
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f'WebSocket error: {ws.exception()}')
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            self.websocket_clients.discard(ws)
            logger.info(f"WebSocket client disconnected. Total clients: {len(self.websocket_clients)}")

        return ws

    async def api_status(self, request):
        """Get current strategy status."""
        return web.json_response({
            "strategy_running": self.strategy_running,
            "pid": self.orchestrator_process.pid if self.orchestrator_process else None,
            "timestamp": datetime.now().isoformat()
        })

    async def api_get_config(self, request):
        """Get current configuration."""
        return web.json_response(self.config)

    async def api_update_config(self, request):
        """Update configuration parameters."""
        try:
            data = await request.json()
            category = data.get('category')
            parameter = data.get('parameter')
            value = data.get('value')

            result = await self.update_parameter(category, parameter, value)
            return web.json_response(result)

        except Exception as e:
            return web.json_response({"success": False, "message": str(e)})

    async def api_start_strategy(self, request):
        """Start the strategy."""
        result = await self.start_strategy()
        return web.json_response(result)

    async def api_stop_strategy(self, request):
        """Stop the strategy."""
        result = await self.stop_strategy()
        return web.json_response(result)

    async def api_get_data(self, request):
        """Get live data."""
        data = self.get_live_data()
        return web.json_response(data)

    async def background_updater(self):
        """Background task to update data and broadcast to clients."""
        while True:
            try:
                # Get live data
                data = self.get_live_data()

                # Broadcast to WebSocket clients
                await self.broadcast_update(data)

                # Update performance metrics
                self.update_performance_metrics(data)

                await asyncio.sleep(2)  # Update every 2 seconds

            except Exception as e:
                logger.error(f"Background updater error: {e}")
                await asyncio.sleep(5)

    def update_performance_metrics(self, data):
        """Update performance metrics based on live data."""
        try:
            if 'recent_signals' in data:
                signals = data['recent_signals']
                if signals:
                    self.performance_metrics['total_signals'] = len(signals)

                    # Calculate win rate (simplified)
                    profitable = sum(1 for s in signals if s.get('pnl', 0) > 0)
                    self.performance_metrics['profitable_signals'] = profitable

                    if len(signals) > 0:
                        self.performance_metrics['win_rate'] = profitable / len(signals)

                    # Calculate total PnL
                    total_pnl = sum(s.get('pnl', 0) for s in signals)
                    self.performance_metrics['total_pnl'] = total_pnl

        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")


async def main():
    """Main function to run the AI Strategy Tuner."""
    tuner = AIStrategyTuner()
    await tuner.start_server()


if __name__ == "__main__":
    asyncio.run(main())
