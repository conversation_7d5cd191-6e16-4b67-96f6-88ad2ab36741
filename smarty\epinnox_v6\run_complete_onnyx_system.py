#!/usr/bin/env python3
"""
Complete Onnyx Trading System - Phase 9.1
Runs the full trading system with UI, data feeds, AI models, and signal generation
"""

import asyncio
import logging
import time
import yaml
import signal
import sys
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

from ui.ai_strategy_tuner import AIStrategyTunerDashboard
from storage.live_store import LiveDataStore
from feeds.trade_parser import MarketFeatures
from execution.execution_controller import ExecutionController
from models.smart_strategy import SmartStrategy
from feeds.htx_ws_client import HTXWebSocketClient
from feeds.binance_ws_client import BinanceWebSocketClient

class StrategyRunner:
    """Wrapper to run the SmartStrategy continuously."""

    def __init__(self, strategy: SmartStrategy, data_store: LiveDataStore):
        self.strategy = strategy
        self.data_store = data_store
        self.running = False

    async def run(self):
        """Run the strategy continuously."""
        self.running = True
        logger.info("🎯 Strategy runner started")

        while self.running:
            try:
                # Get latest features for all symbols
                symbols = ['DOGE/USDT:USDT']  # Default symbol

                for symbol in symbols:
                    features = self.data_store.get_features(symbol)
                    if features:
                        # Process features through strategy
                        signal = await self.strategy.process_features(features)
                        if signal:
                            logger.info(f"📈 Signal: {signal.action} {symbol} (confidence: {signal.confidence:.2%})")

                # Wait before next iteration
                await asyncio.sleep(5)  # Process every 5 seconds

            except Exception as e:
                logger.error(f"Error in strategy runner: {e}")
                await asyncio.sleep(10)  # Wait longer on error

    async def stop(self):
        """Stop the strategy runner."""
        self.running = False
        logger.info("🛑 Strategy runner stopped")

class CompleteOnnxSystem:
    """Complete Onnyx trading system with all components."""

    def __init__(self):
        self.ui = None
        self.data_store = None
        self.execution_controller = None
        self.strategy = None
        self.htx_client = None
        self.binance_client = None  # 🔧 ADD: Binance fallback client
        self.active_ws_client = None  # 🔧 ADD: Track which client is active
        self.runner = None
        self.shutdown_event = asyncio.Event()
        self.tasks = []

    async def start(self):
        """Start the complete trading system."""
        try:
            logger.info("🚀 Starting Complete Onnyx Trading System - Phase 9.1")

            # Load configuration
            script_dir = Path(__file__).parent
            config_path = script_dir / "config" / "strategy.yaml"
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)

            # Initialize data store
            logger.info("📊 Initializing live data store...")
            self.data_store = LiveDataStore(config)

            # Initialize execution controller
            logger.info("🎯 Initializing execution controller...")
            self.execution_controller = ExecutionController(config)

            # Initialize WebSocket clients for market data (HTX primary, Binance fallback)
            logger.info("📡 Initializing WebSocket clients...")
            self.htx_client = HTXWebSocketClient(config)
            self.binance_client = BinanceWebSocketClient(config)

            # Set up data handlers for the WebSocket client
            async def trade_handler(trade_data):
                """🔧 FIX: Process trade data into market features and store them."""
                try:
                    symbol = trade_data.get('symbol', 'DOGE/USDT:USDT')
                    price = float(trade_data.get('price', 0))
                    volume = float(trade_data.get('volume', 0))
                    timestamp = trade_data.get('timestamp', time.time())

                    if price > 0:
                        # Create market features from trade data

                        features = MarketFeatures(
                            symbol=symbol,
                            timestamp=int(timestamp * 1000),  # Convert to milliseconds
                            # Required price features
                            last_price=price,
                            price_change_1m=0.001,  # Mock 0.1% change
                            price_velocity=0.0001,  # Mock velocity
                            # Required volume features
                            volume_1m=volume,
                            buy_volume_ratio=0.55,  # Mock slight buy pressure
                            volume_delta=volume * 0.1,  # Mock volume delta
                            # Required order flow features
                            order_flow_imbalance=0.1,  # Mock order flow
                            trade_intensity=5.0,  # Mock trade intensity
                            avg_trade_size=volume / 10,  # Mock average trade size
                            # Optional technical indicators
                            rsi=50.0 + (price % 10 - 5) * 4,  # Mock RSI based on price
                            vwap=price * 0.999,  # Mock VWAP slightly below price
                            volatility=0.02  # Mock volatility
                        )

                        # Store features in data store
                        self.data_store.store_features(features)
                        logger.info(f"📊 Trade processed: {symbol} @ ${price:.4f} (features stored)")

                except Exception as e:
                    logger.error(f"❌ Error processing trade data: {e}")

            async def depth_handler(depth_data):
                """Handle order book depth data."""
                try:
                    symbol = depth_data.get('symbol', 'DOGE/USDT:USDT')
                    logger.debug(f"📈 Depth data received: {symbol}")
                    # Additional depth processing can be added here
                except Exception as e:
                    logger.error(f"❌ Error processing depth data: {e}")

            # Set handlers for both clients
            self.htx_client.set_trade_handler(trade_handler)
            self.htx_client.set_depth_handler(depth_handler)
            self.binance_client.set_trade_handler(trade_handler)
            self.binance_client.set_depth_handler(depth_handler)

            # Initialize AI trading strategy
            logger.info("🧠 Initializing Smart AI Strategy...")
            smart_strategy = SmartStrategy(config, self.data_store)

            # 🔌 CRITICAL: Inject HTX exchange client for Multi-Timeframe Analysis
            logger.info("🔗 Injecting HTX exchange client into Strategic Intelligence...")
            smart_strategy.set_exchange_client(self.execution_controller.htx_client)

            # 💰 CRITICAL: Inject account tracker for real balance data
            logger.info("💰 Injecting account tracker for real balance data...")
            smart_strategy.set_account_tracker(self.execution_controller.account_tracker)

            # 🎯 CRITICAL: Inject Smart Strategy reference into execution controller
            logger.info("🎯 Injecting Smart Strategy reference into execution controller...")
            self.execution_controller.set_smart_strategy(smart_strategy)

            # 🎯 Set initial strategy mode from config (default to scalping)
            initial_strategy_mode = config.get('strategy_mode', 'scalping')
            logger.info(f"🎯 Setting initial strategy mode: {initial_strategy_mode}")
            smart_strategy.set_strategy_mode(initial_strategy_mode)

            self.strategy = StrategyRunner(smart_strategy, self.data_store)

            # Initialize Onnyx UI
            logger.info("🎨 Initializing Onnyx UI...")
            self.ui = AIStrategyTunerDashboard(config, self.data_store, self.execution_controller)

            # Start all components
            logger.info("🔄 Starting all system components...")

            # Start data store background tasks
            await self.data_store.start_background_tasks()

            # 🧠 CRITICAL: Start Strategic Intelligence background tasks
            logger.info("🧠 Starting Strategic Intelligence background tasks...")
            await smart_strategy.start_background_tasks()
            logger.info("✅ Strategic Intelligence background tasks started")

            # 🔧 ENHANCED: Try HTX first, fallback to Binance if HTX fails
            logger.info("🔄 Attempting to connect to market data sources...")

            # Try HTX first (primary)
            logger.info("📡 Trying HTX WebSocket (primary)...")
            if await self.htx_client.connect():
                htx_task = asyncio.create_task(self.htx_client.listen())
                self.tasks.append(htx_task)
                self.active_ws_client = self.htx_client
                logger.info("✅ HTX WebSocket connected successfully (primary source)")
            else:
                # HTX failed, try Binance fallback
                logger.warning("⚠️ HTX WebSocket failed, trying Binance fallback...")
                if await self.binance_client.connect():
                    binance_task = asyncio.create_task(self.binance_client.listen())
                    self.tasks.append(binance_task)
                    self.active_ws_client = self.binance_client
                    logger.info("✅ Binance WebSocket connected successfully (fallback source)")
                else:
                    logger.error("❌ Both HTX and Binance WebSocket connections failed - continuing without live data")
                    self.active_ws_client = None

            # Start AI strategy
            strategy_task = asyncio.create_task(self.strategy.run())
            self.tasks.append(strategy_task)
            logger.info("✅ Smart AI Strategy started")

            # Start the dashboard
            self.runner = await self.ui.start_server(host='localhost', port=8086)
            logger.info("✅ Onnyx UI started")

            # Mark strategy as running in UI
            self.ui.set_strategy_running(True)

            logger.info("\n🎉 Complete Onnyx Trading System Online!")
            logger.info("🌐 Dashboard URL: http://localhost:8086")
            logger.info("\n🎯 Active Components:")
            logger.info("   ✅ Onnyx UI with live account metrics")

            # 🔧 ENHANCED: Show which data source is active
            if self.active_ws_client == self.htx_client:
                logger.info("   ✅ HTX WebSocket market data feed (primary)")
            elif self.active_ws_client == self.binance_client:
                logger.info("   ✅ Binance WebSocket market data feed (fallback)")
            else:
                logger.info("   ⚠️ No live market data feed (offline mode)")

            logger.info("   ✅ Smart AI Strategy with multiple models")
            logger.info("   ✅ LLM integration for trading decisions")
            logger.info("   ✅ Real-time signal generation")
            logger.info("   ✅ Account monitoring and risk management")
            logger.info("   🧠 Strategic Intelligence modules:")
            logger.info("      ✅ Multi-Timeframe Analyzer (1m, 5m, 15m, 1h, 4h)")
            logger.info("      ✅ Strategy Evaluator with signal filtering")
            logger.info("      ✅ Performance Tracker with health monitoring")
            logger.info("      ✅ LLM Strategy Critic for feedback")

            # 💰 Display real account information
            try:
                account_summary = self.execution_controller.account_tracker.get_account_summary()
                balance = account_summary.get('available_balance', 0.0)
                total_balance = account_summary.get('total_balance', 0.0)
                margin_used = account_summary.get('margin_used_pct', 0.0)
                risk_level = account_summary.get('risk_level', 'UNKNOWN')

                logger.info("\n💰 Live Trading Configuration:")
                logger.info(f"   Available Balance: ${balance:.2f} | Total: ${total_balance:.2f}")
                logger.info(f"   Margin Used: {margin_used:.1f}% | Risk Level: {risk_level.upper()}")
                logger.info("   Leverage: 20x | Symbol: DOGE/USDT:USDT | Trade Capability: ENABLED")
            except Exception as e:
                logger.warning(f"⚠️ Could not fetch account data: {e}")
                logger.info("\n💰 Trading Configuration:")
                logger.info("   Account: Connecting... | Leverage: 20x | Symbol: DOGE/USDT:USDT")
                logger.info("   Risk Level: SAFE | Trade Capability: ENABLED")

            logger.info("\n⌨️ Press Ctrl+C to stop the system")

            # Wait for shutdown signal
            await self.shutdown_event.wait()

        except Exception as e:
            logger.error(f"❌ Error starting complete system: {e}")
            raise

    async def stop(self):
        """Stop all components with proper cleanup."""
        try:
            logger.info("🛑 Shutting down Complete Onnyx Trading System...")

            # Stop all background tasks
            for task in self.tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

            # Stop strategy
            if self.strategy:
                await self.strategy.stop()
                # 🧠 Shutdown Strategic Intelligence modules
                if hasattr(self.strategy, 'smart_strategy'):
                    self.strategy.smart_strategy.shutdown()
                    logger.info("✅ Strategic Intelligence modules shutdown")
                logger.info("✅ AI Strategy stopped")

            # Stop WebSocket clients
            if self.htx_client:
                await self.htx_client.disconnect()
                logger.info("✅ HTX WebSocket client stopped")
            if self.binance_client:
                await self.binance_client.disconnect()
                logger.info("✅ Binance WebSocket client stopped")

            # Stop account monitoring
            if self.execution_controller and hasattr(self.execution_controller, 'account_tracker'):
                await self.execution_controller.account_tracker.stop_monitoring()
                logger.info("✅ Account monitoring stopped")

            # Close CCXT connections
            if self.execution_controller and hasattr(self.execution_controller, 'htx_client'):
                await self.execution_controller.htx_client.disconnect()
                logger.info("✅ HTX client disconnected")

            # Stop the web server
            if self.runner:
                await self.runner.cleanup()
                logger.info("✅ Web server stopped")

            # Shutdown data store
            if self.data_store:
                self.data_store.shutdown()
                logger.info("✅ Data store shutdown")

            logger.info("✅ Complete system shutdown successful")

        except Exception as e:
            logger.error(f"❌ Error during shutdown: {e}")

    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"📡 Received signal {signum}, initiating system shutdown...")
        self.shutdown_event.set()

async def main():
    """Main function with proper signal handling."""
    system = CompleteOnnxSystem()

    # Setup signal handlers
    signal.signal(signal.SIGINT, system.signal_handler)
    signal.signal(signal.SIGTERM, system.signal_handler)

    try:
        await system.start()
    except KeyboardInterrupt:
        logger.info("⌨️ Keyboard interrupt received")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
    finally:
        await system.stop()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("👋 System shutdown complete!")
    except Exception as e:
        logger.error(f"❌ Application error: {e}")
        sys.exit(1)
