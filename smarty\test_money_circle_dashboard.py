#!/usr/bin/env python3
"""
Money Circle Enhanced Dashboard Component Testing Script
Tests all dashboard components for full functionality and diagnoses issues.
"""

import requests
import json
import time
import websocket
import threading
from datetime import datetime

def test_authentication_flow():
    """Test the complete authentication flow."""
    base_url = "http://localhost:8085"

    print("🔐 Testing Authentication Flow...")

    # Create session
    session = requests.Session()

    # Test 1: Login
    print("  1. Testing login...")
    login_data = {
        'username': 'epinnox',
        'password': 'securepass123'
    }

    response = session.post(f"{base_url}/login", data=login_data)
    print(f"     Login response: {response.status_code}")

    if response.status_code in [200, 302]:
        print("     ✅ Login successful")

        # Check session cookie
        session_cookie = session.cookies.get('session_id')
        if session_cookie:
            print(f"     ✅ Session cookie received: {session_cookie[:20]}...")
        else:
            print("     ❌ No session cookie received")
            return False, session
    else:
        print(f"     ❌ Login failed: {response.status_code}")
        return False, session

    # Test 2: Dashboard access
    print("  2. Testing dashboard access...")
    response = session.get(f"{base_url}/dashboard")
    print(f"     Dashboard response: {response.status_code}")

    if response.status_code == 200:
        print("     ✅ Dashboard accessible")
    else:
        print(f"     ❌ Dashboard not accessible: {response.status_code}")
        return False, session

    return True, session

def test_api_endpoints(session):
    """Test all API endpoints with authenticated session."""
    base_url = "http://localhost:8085"

    print("\n📊 Testing API Endpoints...")

    api_tests = [
        ("/api/portfolio", "Portfolio Overview"),
        ("/api/portfolio/enhanced", "Enhanced Portfolio"),
        ("/api/portfolio/risk-metrics", "Risk Metrics"),
        ("/api/portfolio/performance", "Performance Metrics"),
        ("/api/portfolio/real-time", "Real-time Portfolio")
    ]

    results = []

    for endpoint, name in api_tests:
        print(f"  Testing {name}...")
        try:
            response = session.get(f"{base_url}{endpoint}")
            print(f"    Status: {response.status_code}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"    ✅ {name}: Success")
                    print(f"    Data keys: {list(data.keys())}")
                    results.append((name, True, response.status_code))
                except json.JSONDecodeError:
                    print(f"    ⚠️ {name}: Invalid JSON response")
                    results.append((name, False, response.status_code))
            elif response.status_code == 401:
                print(f"    ❌ {name}: Authentication failed")
                results.append((name, False, response.status_code))
            else:
                print(f"    ❌ {name}: Failed with status {response.status_code}")
                results.append((name, False, response.status_code))

        except Exception as e:
            print(f"    ❌ {name}: Exception - {e}")
            results.append((name, False, "Exception"))

    return results

def test_websocket_connection():
    """Test WebSocket connection."""
    print("\n🔄 Testing WebSocket Connection...")

    # Get session cookie first
    base_url = "http://localhost:8085"
    session = requests.Session()

    # Login to get session
    login_data = {
        'username': 'epinnox',
        'password': 'securepass123'
    }

    response = session.post(f"{base_url}/login", data=login_data)
    if response.status_code not in [200, 302]:
        print("  ❌ Cannot test WebSocket - login failed")
        return False

    session_cookie = session.cookies.get('session_id')
    if not session_cookie:
        print("  ❌ Cannot test WebSocket - no session cookie")
        return False

    # Test WebSocket connection
    ws_url = "ws://localhost:8085/ws"
    headers = {
        'Cookie': f'session_id={session_cookie}'
    }

    try:
        print(f"  Connecting to {ws_url}...")

        def on_message(ws, message):
            print(f"  📨 WebSocket message: {message}")

        def on_error(ws, error):
            print(f"  ❌ WebSocket error: {error}")

        def on_close(ws, close_status_code, close_msg):
            print(f"  🔌 WebSocket closed: {close_status_code}")

        def on_open(ws):
            print("  ✅ WebSocket connected successfully")
            # Send a test message
            ws.send(json.dumps({'type': 'ping'}))
            # Close after 3 seconds
            time.sleep(3)
            ws.close()

        ws = websocket.WebSocketApp(ws_url,
                                  header=headers,
                                  on_open=on_open,
                                  on_message=on_message,
                                  on_error=on_error,
                                  on_close=on_close)

        # Run WebSocket in a separate thread with timeout
        ws_thread = threading.Thread(target=ws.run_forever)
        ws_thread.daemon = True
        ws_thread.start()
        ws_thread.join(timeout=10)

        return True

    except Exception as e:
        print(f"  ❌ WebSocket connection failed: {e}")
        return False

def test_money_circle_dashboard():
    """Test Money Circle enhanced dashboard functionality."""
    print("🚀 Money Circle Enhanced Dashboard Diagnostic Test")
    print("=" * 60)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Test 1: Authentication Flow
    auth_success, session = test_authentication_flow()

    if not auth_success:
        print("\n❌ CRITICAL: Authentication failed - cannot proceed with other tests")
        return False

    # Test 2: API Endpoints
    api_results = test_api_endpoints(session)

    # Test 3: WebSocket Connection
    ws_success = test_websocket_connection()

    # Test 4: Dashboard Page Components
    print("\n🌐 Testing Dashboard Page Components...")
    try:
        response = session.get("http://localhost:8085/dashboard")
        if response.status_code == 200:
            html_content = response.text

            # Check for key dashboard elements
            key_elements = [
                'portfolio-overview',
                'risk-management',
                'performance-analytics',
                'trading-interface',
                'market-data'
            ]

            present_elements = [elem for elem in key_elements if elem in html_content]

            print(f"  ✅ Dashboard loaded successfully")
            print(f"  📊 Key elements found: {len(present_elements)}/{len(key_elements)}")

            for element in present_elements:
                print(f"    • {element}")

            if len(present_elements) < 3:
                print(f"  ⚠️ Missing elements: {set(key_elements) - set(present_elements)}")
        else:
            print(f"  ❌ Dashboard page failed: {response.status_code}")
    except Exception as e:
        print(f"  ❌ Dashboard page error: {e}")

    # Generate Summary Report
    print("\n" + "=" * 60)
    print("📋 DIAGNOSTIC SUMMARY")
    print("=" * 60)

    # Authentication
    auth_status = "✅ PASS" if auth_success else "❌ FAIL"
    print(f"🔐 Authentication: {auth_status}")

    # API Endpoints
    api_passed = sum(1 for _, success, _ in api_results if success)
    api_total = len(api_results)
    api_status = "✅ PASS" if api_passed == api_total else f"⚠️ PARTIAL ({api_passed}/{api_total})"
    print(f"📊 API Endpoints: {api_status}")

    for name, success, status in api_results:
        status_icon = "✅" if success else "❌"
        print(f"   {status_icon} {name}: {status}")

    # WebSocket
    ws_status = "✅ PASS" if ws_success else "❌ FAIL"
    print(f"🔄 WebSocket: {ws_status}")

    # Overall Status
    overall_success = auth_success and api_passed >= (api_total * 0.8) and ws_success
    overall_status = "✅ HEALTHY" if overall_success else "❌ ISSUES DETECTED"
    print(f"\n🎯 Overall Status: {overall_status}")

    if not overall_success:
        print("\n🔧 RECOMMENDED ACTIONS:")
        if not auth_success:
            print("   1. Check authentication middleware configuration")
            print("   2. Verify session management and cookie settings")
        if api_passed < api_total:
            print("   3. Review API endpoint authentication")
            print("   4. Check database connections and data access")
        if not ws_success:
            print("   5. Fix WebSocket authentication and upgrade handling")
            print("   6. Verify WebSocket middleware configuration")

    return overall_success

if __name__ == "__main__":
    success = test_money_circle_dashboard()
    exit(0 if success else 1)
