2025-06-06 11:45:31,492 - storage.live_store - INFO - Live Data Store initialized
2025-06-06 11:45:31,492 - storage.live_store - INFO - Background tasks started
2025-06-06 11:45:31,494 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 11:45:31,495 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 11:45:31,495 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 11:45:31,496 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 11:45:31,496 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 11:45:31,498 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 11:45:31,500 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 11:45:31,500 - models.llm_integration - INFO - LLM background processing started
2025-06-06 11:45:31,503 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 11:45:31,506 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 11:55:43,009 - storage.live_store - INFO - Live Data Store initialized
2025-06-06 11:55:43,010 - storage.live_store - INFO - Background tasks started
2025-06-06 11:55:43,012 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 11:55:43,014 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 11:55:43,014 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 11:55:43,014 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 11:55:43,014 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 11:55:43,014 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 11:55:43,014 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 11:55:43,014 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 11:55:43,014 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 11:55:43,016 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 11:55:43,020 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 11:55:43,020 - models.llm_integration - INFO - LLM background processing started
2025-06-06 11:55:43,022 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 11:55:43,026 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 11:55:43,219 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 11:55:45,328 - __main__ - ERROR - WebSocket error: server rejected WebSocket connection: HTTP 404
2025-06-06 11:55:45,346 - __main__ - INFO - ============================================================
2025-06-06 11:55:45,354 - __main__ - INFO - ============================================================
2025-06-06 11:56:24,432 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:56:24 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:56:24,636 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:56:24 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:56:24,637 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 11:56:24,642 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:56:24 -0600] "GET /favicon.ico HTTP/1.1" 404 172 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:57:59,254 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:57:59 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:57:59,469 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 11:57:59,469 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:57:59 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:03,679 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 11:58:03,679 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:03 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:31,826 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:31 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:31,829 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 11:58:31,830 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:56:24 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:31,904 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:31 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:31,904 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 11:58:32,483 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:32 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:32,486 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 11:58:32,487 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:31 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:32,547 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 11:58:32,547 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:32 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:00:43,056 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 12:01:25,836 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:25 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:25,845 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:01:25,845 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:32 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:25,937 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:01:25,938 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:25 -0600] "GET /api/data HTTP/1.1" 200 706 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:27,602 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:27 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:27,606 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:01:27,606 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:25 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:27,680 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:01:27,681 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:27 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:28,223 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:28 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:28,228 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:01:28,228 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:27 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:28,315 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:01:28,316 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:28 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:31,414 - ui.ai_strategy_tuner - INFO - Applied Conservative preset to BTC-USDT
2025-06-06 12:01:31,415 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:31 -0600] "POST /api/presets/apply HTTP/1.1" 200 351 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:02:23,083 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:02:23,084 - storage.live_store - INFO - Live Data Store initialized
2025-06-06 12:02:23,084 - storage.live_store - INFO - Background tasks started
2025-06-06 12:02:23,087 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:02:23,093 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:02:23,094 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:02:23,094 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:02:23,094 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:02:23,094 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:02:23,094 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:02:23,094 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:02:23,094 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:02:23,099 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:02:23,102 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 12:02:23,102 - models.llm_integration - INFO - LLM background processing started
2025-06-06 12:02:23,104 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:02:23,108 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:02:23,274 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:02:25,299 - __main__ - ERROR - WebSocket error: server rejected WebSocket connection: HTTP 404
2025-06-06 12:02:25,312 - __main__ - INFO - ============================================================
2025-06-06 12:02:25,317 - __main__ - INFO - ============================================================
2025-06-06 12:02:25,493 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:03:05,942 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:05 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:03:06,132 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:03:06,133 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:06 -0600] "GET /api/data HTTP/1.1" 200 706 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:03:45,335 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:45 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:03:46,114 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:03:46,115 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:46 -0600] "GET /api/data HTTP/1.1" 200 706 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:03:49,229 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 12:03:49,230 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:49 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:05:48,226 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:05:48 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:05:48,231 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (2 total)
2025-06-06 12:05:48,231 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:46 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:05:48,315 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:05:48,316 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:05:48 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:05:55,331 - ui.ai_strategy_tuner - INFO - Applied Aggressive preset to BTC-USDT
2025-06-06 12:05:55,332 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:05:55 -0600] "POST /api/presets/apply HTTP/1.1" 200 349 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:06:01,379 - ui.ai_strategy_tuner - INFO - Switched to symbol: ETH-USDT
2025-06-06 12:06:01,379 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:06:01 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:06:08,711 - __main__ - INFO - Received signal 2
2025-06-06 12:06:09,634 - storage.live_store - INFO - Live Data Store shutdown complete
2025-06-06 12:06:09,640 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (2 total)
2025-06-06 12:06:09,640 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:06:09,641 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (0 total)
2025-06-06 12:07:47,711 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 12:07:47,711 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 12:07:47,716 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 12:07:47,716 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 12:07:47,717 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:07:47,717 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 12:07:47,717 - storage.live_store - INFO - Background tasks started
2025-06-06 12:07:47,717 - __main__ - INFO - [OK] Data store initialized
2025-06-06 12:07:47,717 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:07:47,717 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 12:07:47,717 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:07:47,717 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:07:47,717 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:07:47,717 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:07:47,717 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:07:47,717 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:07:47,717 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:07:47,717 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:07:47,717 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance backup)
2025-06-06 12:07:47,717 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:07:47,717 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 12:07:47,717 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 12:07:47,717 - models.llm_integration - INFO - LLM background processing started
2025-06-06 12:07:47,717 - __main__ - INFO - [OK] LLM integration initialized
2025-06-06 12:07:47,718 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:07:47,718 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 12:07:47,718 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 12:07:47,718 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:07:47,877 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 12:07:47,879 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 12:07:47,879 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:07:49,917 - feeds.binance_ws_client - ERROR - ❌ Failed to connect to Binance WebSocket: server rejected WebSocket connection: HTTP 404
2025-06-06 12:07:49,917 - __main__ - ERROR - [WS_ERROR] WebSocket error: server rejected WebSocket connection: HTTP 404
2025-06-06 12:07:49,917 - __main__ - ERROR - [ERROR] Both HTX and Binance connections failed
2025-06-06 12:07:49,917 - __main__ - INFO - [MODE] Continuing without WebSocket for testing purposes
2025-06-06 12:07:49,917 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 12:07:49,918 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 12:07:49,918 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 12:07:49,918 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 12:07:49,918 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 12:07:49,919 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 12:07:49,919 - __main__ - INFO - ============================================================
2025-06-06 12:07:49,919 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 12:07:49,919 - __main__ - INFO - [DATA] Market data source: None (Testing Mode)
2025-06-06 12:07:49,919 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 12:07:49,919 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 12:07:49,919 - __main__ - INFO - ============================================================
2025-06-06 12:07:50,082 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:07:50,410 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:07:51,451 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:08:05,601 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:08:05 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:08:06,387 - ui.ai_strategy_tuner - INFO - WebSocket client connected (4 total)
2025-06-06 12:08:06,388 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:08:06 -0600] "GET /api/data HTTP/1.1" 200 706 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:08:51,482 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 12:08:51,482 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:08:51 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:09:29,354 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (3 total)
2025-06-06 12:09:29,354 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:07:50 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:09:29,917 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (2 total)
2025-06-06 12:09:29,918 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:07:50 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:09:30,428 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:09:30,429 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:07:51 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:10:12,382 - __main__ - INFO - [SIGNAL] Received signal 2
2025-06-06 12:10:13,343 - __main__ - INFO - [STOP] Stopping Epinnox V6 AI Strategy Tuner...
2025-06-06 12:10:13,344 - storage.live_store - INFO - Live Data Store shutdown complete
2025-06-06 12:10:13,344 - __main__ - INFO - [OK] Application stopped gracefully
2025-06-06 12:10:13,344 - __main__ - INFO - [COMPLETE] Epinnox V6 AI Strategy Tuner completed successfully
2025-06-06 12:10:13,344 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (0 total)
