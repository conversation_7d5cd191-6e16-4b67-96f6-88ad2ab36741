2025-06-06 11:45:31,492 - storage.live_store - INFO - Live Data Store initialized
2025-06-06 11:45:31,492 - storage.live_store - INFO - Background tasks started
2025-06-06 11:45:31,494 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 11:45:31,495 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 11:45:31,495 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 11:45:31,496 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 11:45:31,496 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 11:45:31,498 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 11:45:31,500 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 11:45:31,500 - models.llm_integration - INFO - LLM background processing started
2025-06-06 11:45:31,503 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 11:45:31,506 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 11:55:43,009 - storage.live_store - INFO - Live Data Store initialized
2025-06-06 11:55:43,010 - storage.live_store - INFO - Background tasks started
2025-06-06 11:55:43,012 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 11:55:43,014 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 11:55:43,014 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 11:55:43,014 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 11:55:43,014 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 11:55:43,014 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 11:55:43,014 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 11:55:43,014 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 11:55:43,014 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 11:55:43,016 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 11:55:43,020 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 11:55:43,020 - models.llm_integration - INFO - LLM background processing started
2025-06-06 11:55:43,022 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 11:55:43,026 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 11:55:43,219 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 11:55:45,328 - __main__ - ERROR - WebSocket error: server rejected WebSocket connection: HTTP 404
2025-06-06 11:55:45,346 - __main__ - INFO - ============================================================
2025-06-06 11:55:45,354 - __main__ - INFO - ============================================================
2025-06-06 11:56:24,432 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:56:24 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:56:24,636 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:56:24 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:56:24,637 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 11:56:24,642 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:56:24 -0600] "GET /favicon.ico HTTP/1.1" 404 172 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:57:59,254 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:57:59 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:57:59,469 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 11:57:59,469 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:57:59 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:03,679 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 11:58:03,679 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:03 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:31,826 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:31 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:31,829 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 11:58:31,830 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:56:24 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:31,904 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:31 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:31,904 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 11:58:32,483 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:32 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:32,486 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 11:58:32,487 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:31 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:32,547 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 11:58:32,547 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:32 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:00:43,056 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 12:01:25,836 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:25 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:25,845 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:01:25,845 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:32 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:25,937 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:01:25,938 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:25 -0600] "GET /api/data HTTP/1.1" 200 706 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:27,602 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:27 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:27,606 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:01:27,606 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:25 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:27,680 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:01:27,681 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:27 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:28,223 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:28 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:28,228 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:01:28,228 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:27 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:28,315 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:01:28,316 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:28 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:31,414 - ui.ai_strategy_tuner - INFO - Applied Conservative preset to BTC-USDT
2025-06-06 12:01:31,415 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:31 -0600] "POST /api/presets/apply HTTP/1.1" 200 351 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
