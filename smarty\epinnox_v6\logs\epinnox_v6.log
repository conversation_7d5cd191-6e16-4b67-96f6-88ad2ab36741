2025-06-06 11:45:31,492 - storage.live_store - INFO - Live Data Store initialized
2025-06-06 11:45:31,492 - storage.live_store - INFO - Background tasks started
2025-06-06 11:45:31,494 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 11:45:31,495 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 11:45:31,495 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 11:45:31,496 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 11:45:31,496 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 11:45:31,498 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 11:45:31,500 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 11:45:31,500 - models.llm_integration - INFO - LLM background processing started
2025-06-06 11:45:31,503 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 11:45:31,506 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 11:55:43,009 - storage.live_store - INFO - Live Data Store initialized
2025-06-06 11:55:43,010 - storage.live_store - INFO - Background tasks started
2025-06-06 11:55:43,012 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 11:55:43,014 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 11:55:43,014 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 11:55:43,014 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 11:55:43,014 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 11:55:43,014 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 11:55:43,014 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 11:55:43,014 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 11:55:43,014 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 11:55:43,016 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 11:55:43,020 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 11:55:43,020 - models.llm_integration - INFO - LLM background processing started
2025-06-06 11:55:43,022 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 11:55:43,026 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 11:55:43,219 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 11:55:45,328 - __main__ - ERROR - WebSocket error: server rejected WebSocket connection: HTTP 404
2025-06-06 11:55:45,346 - __main__ - INFO - ============================================================
2025-06-06 11:55:45,354 - __main__ - INFO - ============================================================
2025-06-06 11:56:24,432 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:56:24 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:56:24,636 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:56:24 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:56:24,637 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 11:56:24,642 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:56:24 -0600] "GET /favicon.ico HTTP/1.1" 404 172 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:57:59,254 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:57:59 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:57:59,469 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 11:57:59,469 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:57:59 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:03,679 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 11:58:03,679 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:03 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:31,826 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:31 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:31,829 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 11:58:31,830 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:56:24 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:31,904 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:31 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:31,904 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 11:58:32,483 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:32 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:32,486 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 11:58:32,487 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:31 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:32,547 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 11:58:32,547 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:32 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:00:43,056 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 12:01:25,836 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:25 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:25,845 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:01:25,845 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:32 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:25,937 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:01:25,938 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:25 -0600] "GET /api/data HTTP/1.1" 200 706 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:27,602 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:27 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:27,606 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:01:27,606 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:25 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:27,680 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:01:27,681 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:27 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:28,223 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:28 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:28,228 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:01:28,228 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:27 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:28,315 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:01:28,316 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:28 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:31,414 - ui.ai_strategy_tuner - INFO - Applied Conservative preset to BTC-USDT
2025-06-06 12:01:31,415 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:31 -0600] "POST /api/presets/apply HTTP/1.1" 200 351 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:02:23,083 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:02:23,084 - storage.live_store - INFO - Live Data Store initialized
2025-06-06 12:02:23,084 - storage.live_store - INFO - Background tasks started
2025-06-06 12:02:23,087 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:02:23,093 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:02:23,094 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:02:23,094 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:02:23,094 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:02:23,094 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:02:23,094 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:02:23,094 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:02:23,094 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:02:23,099 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:02:23,102 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 12:02:23,102 - models.llm_integration - INFO - LLM background processing started
2025-06-06 12:02:23,104 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:02:23,108 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:02:23,274 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:02:25,299 - __main__ - ERROR - WebSocket error: server rejected WebSocket connection: HTTP 404
2025-06-06 12:02:25,312 - __main__ - INFO - ============================================================
2025-06-06 12:02:25,317 - __main__ - INFO - ============================================================
2025-06-06 12:02:25,493 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:03:05,942 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:05 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:03:06,132 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:03:06,133 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:06 -0600] "GET /api/data HTTP/1.1" 200 706 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:03:45,335 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:45 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:03:46,114 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:03:46,115 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:46 -0600] "GET /api/data HTTP/1.1" 200 706 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:03:49,229 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 12:03:49,230 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:49 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:05:48,226 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:05:48 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:05:48,231 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (2 total)
2025-06-06 12:05:48,231 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:46 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:05:48,315 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:05:48,316 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:05:48 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:05:55,331 - ui.ai_strategy_tuner - INFO - Applied Aggressive preset to BTC-USDT
2025-06-06 12:05:55,332 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:05:55 -0600] "POST /api/presets/apply HTTP/1.1" 200 349 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:06:01,379 - ui.ai_strategy_tuner - INFO - Switched to symbol: ETH-USDT
2025-06-06 12:06:01,379 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:06:01 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:06:08,711 - __main__ - INFO - Received signal 2
2025-06-06 12:06:09,634 - storage.live_store - INFO - Live Data Store shutdown complete
2025-06-06 12:06:09,640 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (2 total)
2025-06-06 12:06:09,640 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:06:09,641 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (0 total)
2025-06-06 12:07:47,711 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 12:07:47,711 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 12:07:47,716 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 12:07:47,716 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 12:07:47,717 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:07:47,717 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 12:07:47,717 - storage.live_store - INFO - Background tasks started
2025-06-06 12:07:47,717 - __main__ - INFO - [OK] Data store initialized
2025-06-06 12:07:47,717 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:07:47,717 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 12:07:47,717 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:07:47,717 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:07:47,717 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:07:47,717 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:07:47,717 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:07:47,717 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:07:47,717 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:07:47,717 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:07:47,717 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance backup)
2025-06-06 12:07:47,717 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:07:47,717 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 12:07:47,717 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 12:07:47,717 - models.llm_integration - INFO - LLM background processing started
2025-06-06 12:07:47,717 - __main__ - INFO - [OK] LLM integration initialized
2025-06-06 12:07:47,718 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:07:47,718 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 12:07:47,718 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 12:07:47,718 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:07:47,877 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 12:07:47,879 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 12:07:47,879 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:07:49,917 - feeds.binance_ws_client - ERROR - ❌ Failed to connect to Binance WebSocket: server rejected WebSocket connection: HTTP 404
2025-06-06 12:07:49,917 - __main__ - ERROR - [WS_ERROR] WebSocket error: server rejected WebSocket connection: HTTP 404
2025-06-06 12:07:49,917 - __main__ - ERROR - [ERROR] Both HTX and Binance connections failed
2025-06-06 12:07:49,917 - __main__ - INFO - [MODE] Continuing without WebSocket for testing purposes
2025-06-06 12:07:49,917 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 12:07:49,918 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 12:07:49,918 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 12:07:49,918 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 12:07:49,918 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 12:07:49,919 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 12:07:49,919 - __main__ - INFO - ============================================================
2025-06-06 12:07:49,919 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 12:07:49,919 - __main__ - INFO - [DATA] Market data source: None (Testing Mode)
2025-06-06 12:07:49,919 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 12:07:49,919 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 12:07:49,919 - __main__ - INFO - ============================================================
2025-06-06 12:07:50,082 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:07:50,410 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:07:51,451 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:08:05,601 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:08:05 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:08:06,387 - ui.ai_strategy_tuner - INFO - WebSocket client connected (4 total)
2025-06-06 12:08:06,388 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:08:06 -0600] "GET /api/data HTTP/1.1" 200 706 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:08:51,482 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 12:08:51,482 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:08:51 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:09:29,354 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (3 total)
2025-06-06 12:09:29,354 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:07:50 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:09:29,917 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (2 total)
2025-06-06 12:09:29,918 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:07:50 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:09:30,428 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:09:30,429 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:07:51 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:10:12,382 - __main__ - INFO - [SIGNAL] Received signal 2
2025-06-06 12:10:13,343 - __main__ - INFO - [STOP] Stopping Epinnox V6 AI Strategy Tuner...
2025-06-06 12:10:13,344 - storage.live_store - INFO - Live Data Store shutdown complete
2025-06-06 12:10:13,344 - __main__ - INFO - [OK] Application stopped gracefully
2025-06-06 12:10:13,344 - __main__ - INFO - [COMPLETE] Epinnox V6 AI Strategy Tuner completed successfully
2025-06-06 12:10:13,344 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (0 total)
2025-06-06 12:12:10,332 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 12:12:10,334 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 12:12:10,340 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 12:12:10,340 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 12:12:10,340 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:12:10,340 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 12:12:10,340 - storage.live_store - INFO - Background tasks started
2025-06-06 12:12:10,340 - __main__ - INFO - [OK] Data store initialized
2025-06-06 12:12:10,340 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:12:10,340 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 12:12:10,340 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:12:10,340 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:12:10,340 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 12:12:10,340 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:12:10,341 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:12:10,341 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:12:10,341 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:12:10,341 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:12:10,341 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:12:10,341 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 12:12:10,341 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 12:12:10,341 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 12:12:10,341 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 12:12:10,341 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:12:10,341 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 12:12:10,341 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 12:12:10,342 - models.llm_integration - INFO - LLM background processing started
2025-06-06 12:12:10,342 - __main__ - INFO - [OK] LLM integration initialized
2025-06-06 12:12:10,342 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:12:10,342 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 12:12:10,342 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 12:12:10,342 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:12:10,618 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 12:12:10,619 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 12:12:10,619 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 12:12:10,619 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:12:12,285 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 12:12:12,285 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 12:12:12,285 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 12:12:12,286 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 12:12:12,286 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 12:12:12,287 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 12:12:12,287 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 12:12:12,287 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 12:12:12,288 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 12:12:12,288 - __main__ - INFO - ============================================================
2025-06-06 12:12:12,288 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 12:12:12,288 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 12:12:12,288 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 12:12:12,289 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 12:12:12,289 - __main__ - INFO - ============================================================
2025-06-06 12:12:13,319 - storage.live_store - ERROR - Error storing signal: 'BTC-USDT'
2025-06-06 12:12:13,320 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 100.00%)
2025-06-06 12:12:13,320 - models.llm_integration - ERROR - LLM API call error: unsupported format string passed to NoneType.__format__
2025-06-06 12:12:18,079 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:12:55,359 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:13:40,373 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:14:25,391 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:15:10,401 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:15:18,600 - storage.live_store - ERROR - Error storing signal: 'BTC-USDT'
2025-06-06 12:15:18,601 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:15:18,601 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:15:54,635 - storage.live_store - ERROR - Error storing signal: 'BTC-USDT'
2025-06-06 12:15:54,635 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:15:54,636 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:15:55,395 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:15:57,076 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:15:57 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:15:57,371 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:15:57,372 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:15:57 -0600] "GET /api/data HTTP/1.1" 200 4147 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:16:28,995 - storage.live_store - ERROR - Error storing signal: 'BTC-USDT'
2025-06-06 12:16:28,995 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:16:28,995 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:16:30,968 - ui.ai_strategy_tuner - INFO - Applied Conservative preset to BTC-USDT
2025-06-06 12:16:30,969 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:16:30 -0600] "POST /api/presets/apply HTTP/1.1" 200 351 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:16:40,403 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:17:10,377 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 12:17:25,404 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:17:33,802 - storage.live_store - ERROR - Error storing signal: 'BTC-USDT'
2025-06-06 12:17:33,802 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 73.21%)
2025-06-06 12:17:33,803 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:18:09,968 - storage.live_store - ERROR - Error storing signal: 'BTC-USDT'
2025-06-06 12:18:09,968 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:18:09,968 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:18:10,417 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:18:55,426 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:19:26,547 - storage.live_store - ERROR - Error storing signal: 'BTC-USDT'
2025-06-06 12:19:26,548 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:19:26,548 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:19:40,443 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:19:48,515 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 12:19:48,515 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 12:19:51,864 - __main__ - INFO - [SIGNAL] Received signal 2
2025-06-06 12:19:52,727 - __main__ - INFO - [STOP] Stopping Epinnox V6 AI Strategy Tuner...
2025-06-06 12:19:52,728 - storage.live_store - INFO - Live Data Store shutdown complete
2025-06-06 12:19:52,728 - __main__ - INFO - [OK] Application stopped gracefully
2025-06-06 12:19:52,728 - __main__ - INFO - [COMPLETE] Epinnox V6 AI Strategy Tuner completed successfully
2025-06-06 12:19:52,728 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:19:52,728 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (0 total)
2025-06-06 12:24:16,593 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 12:24:16,593 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 12:24:16,598 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 12:24:16,598 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 12:24:16,599 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:24:16,599 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 12:24:16,599 - storage.live_store - INFO - Background tasks started
2025-06-06 12:24:16,599 - __main__ - INFO - [OK] Data store initialized
2025-06-06 12:24:16,599 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:24:16,599 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 12:24:16,600 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:24:16,600 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:24:16,600 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 12:24:16,600 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:24:16,600 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:24:16,600 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:24:16,600 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:24:16,600 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:24:16,600 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:24:16,600 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 12:24:16,600 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 12:24:16,600 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 12:24:16,600 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 12:24:16,602 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:24:16,602 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 12:24:16,602 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 12:24:16,602 - models.llm_integration - INFO - LLM background processing started
2025-06-06 12:24:16,602 - __main__ - INFO - [OK] LLM integration initialized
2025-06-06 12:24:16,603 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:24:16,603 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 12:24:16,603 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 12:24:16,603 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:24:16,930 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 12:24:16,930 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 12:24:16,930 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 12:24:16,930 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:24:18,465 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 12:24:18,465 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 12:24:18,465 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 12:24:18,466 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 12:24:18,466 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 12:24:18,466 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 12:24:18,467 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 12:24:18,467 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 12:24:18,467 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 12:24:18,467 - __main__ - INFO - ============================================================
2025-06-06 12:24:18,467 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 12:24:18,467 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 12:24:18,467 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 12:24:18,467 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 12:24:18,467 - __main__ - INFO - ============================================================
2025-06-06 12:24:18,633 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:24:20,622 - storage.live_store - INFO - 📈 Signal stored: BTC-USDT - LONG
2025-06-06 12:24:20,622 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 100.00%)
2025-06-06 12:24:21,375 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:24:22,788 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:25:01,637 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:25:01 -0600] "GET / HTTP/1.1" 200 49239 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:25:01,958 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:25:01,959 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:25:01 -0600] "GET /api/data HTTP/1.1" 200 2566 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:25:03,695 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:25:04,472 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:25:04 -0600] "GET /api/settings/get HTTP/1.1" 200 333 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:25:04,961 - storage.live_store - INFO - 📈 Signal stored: BTC-USDT - LONG
2025-06-06 12:25:04,962 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 78.33%)
2025-06-06 12:25:07,003 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:25:10,539 - ui.ai_strategy_tuner - INFO - Applied Conservative preset to BTC-USDT
2025-06-06 12:25:10,541 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:25:10 -0600] "POST /api/presets/apply HTTP/1.1" 200 351 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:25:50,754 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:26:37,804 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:27:24,841 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:27:43,392 - ui.ai_strategy_tuner - INFO - Switched to symbol: ETH-USDT
2025-06-06 12:27:43,392 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:27:43 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:27:44,037 - ui.ai_strategy_tuner - INFO - Switched to symbol: DOGE-USDT
2025-06-06 12:27:44,037 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:27:44 -0600] "POST /api/symbol/switch HTTP/1.1" 200 348 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:27:44,366 - ui.ai_strategy_tuner - INFO - Switched to symbol: SOL-USDT
2025-06-06 12:27:44,367 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:27:44 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:27:44,622 - ui.ai_strategy_tuner - INFO - Switched to symbol: ADA-USDT
2025-06-06 12:27:44,624 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:27:44 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:28:01,363 - ui.ai_strategy_tuner - INFO - Switched to symbol: BTC-USDT
2025-06-06 12:28:01,364 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:28:01 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:28:11,905 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:28:19,771 - ui.ai_strategy_tuner - INFO - Applied Aggressive preset to BTC-USDT
2025-06-06 12:28:19,772 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:28:19 -0600] "POST /api/presets/apply HTTP/1.1" 200 349 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:28:24,975 - ui.ai_strategy_tuner - INFO - Switched to symbol: DOGE-USDT
2025-06-06 12:28:24,976 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:28:24 -0600] "POST /api/symbol/switch HTTP/1.1" 200 348 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:28:29,411 - storage.live_store - INFO - 📈 Signal stored: BTC-USDT - LONG
2025-06-06 12:28:29,411 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:28:30,773 - ui.ai_strategy_tuner - INFO - Switched to symbol: ETH-USDT
2025-06-06 12:28:30,773 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:28:30 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:28:31,454 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:28:36,634 - ui.ai_strategy_tuner - INFO - Switched to symbol: BTC-USDT
2025-06-06 12:28:36,635 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:28:36 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:28:39,732 - ui.ai_strategy_tuner - INFO - Switched to symbol: ETH-USDT
2025-06-06 12:28:39,733 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:28:39 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:28:47,317 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to ETH-USDT
2025-06-06 12:28:47,317 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:28:47 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:28:58,951 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:29:16,641 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 12:29:21,848 - storage.live_store - INFO - 📈 Signal stored: BTC-USDT - LONG
2025-06-06 12:29:21,849 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:29:23,829 - ui.ai_strategy_tuner - INFO - Applied Aggressive preset to ETH-USDT
2025-06-06 12:29:23,829 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:29:23 -0600] "POST /api/presets/apply HTTP/1.1" 200 349 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:29:23,889 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:29:25,901 - ui.ai_strategy_tuner - INFO - Switched to symbol: BTC-USDT
2025-06-06 12:29:25,903 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:29:25 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:29:46,021 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:30:02,814 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 12:30:02,815 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 12:30:02,820 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 12:30:02,820 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 12:30:02,820 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:30:02,820 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 12:30:02,820 - storage.live_store - INFO - Background tasks started
2025-06-06 12:30:02,821 - __main__ - INFO - [OK] Data store initialized
2025-06-06 12:30:02,821 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:30:02,821 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 12:30:02,821 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:30:02,821 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:30:02,821 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 12:30:02,821 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:30:02,821 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:30:02,821 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:30:02,821 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:30:02,821 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:30:02,821 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:30:02,821 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 12:30:02,821 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 12:30:02,821 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 12:30:02,821 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 12:30:02,822 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:30:02,822 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 12:30:02,822 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 12:30:02,822 - models.llm_integration - INFO - LLM background processing started
2025-06-06 12:30:02,822 - __main__ - INFO - [OK] LLM integration initialized
2025-06-06 12:30:02,822 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:30:02,822 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 12:30:02,822 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 12:30:02,822 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:30:03,090 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 12:30:03,090 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 12:30:03,090 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 12:30:03,090 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:30:04,528 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 12:30:04,528 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 12:30:04,528 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 12:30:04,529 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 12:30:04,529 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 12:30:04,530 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 12:30:04,530 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 12:30:04,530 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 12:30:04,530 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 12:30:04,530 - __main__ - INFO - ============================================================
2025-06-06 12:30:04,530 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 12:30:04,530 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 12:30:04,531 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 12:30:04,531 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 12:30:04,531 - __main__ - INFO - ============================================================
2025-06-06 12:30:04,648 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:30:05,099 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:30:05,100 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 100.00%)
2025-06-06 12:30:05,438 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:30:07,148 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:30:07,477 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:30:49,897 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:31:36,925 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:32:23,977 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:33:00,518 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:33:00,518 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 78.70%)
2025-06-06 12:33:02,560 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:33:10,989 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:33:58,049 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:34:37,657 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 12:34:37,657 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 12:34:37,663 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 12:34:37,663 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 12:34:37,663 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:34:37,663 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 12:34:37,663 - storage.live_store - INFO - Background tasks started
2025-06-06 12:34:37,663 - __main__ - INFO - [OK] Data store initialized
2025-06-06 12:34:37,663 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:34:37,663 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 12:34:37,663 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:34:37,663 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:34:37,663 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 12:34:37,663 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:34:37,664 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:34:37,664 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:34:37,664 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:34:37,664 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:34:37,664 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:34:37,664 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 12:34:37,664 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 12:34:37,664 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 12:34:37,664 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 12:34:37,664 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:34:37,664 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 12:34:37,664 - models.llm_integration - INFO - LLM Integration disabled in configuration
2025-06-06 12:34:37,664 - __main__ - INFO - [OK] LLM integration initialized
2025-06-06 12:34:37,664 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:34:37,664 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 12:34:37,664 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 12:34:37,665 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:34:37,925 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 12:34:37,925 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 12:34:37,925 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 12:34:37,925 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:34:39,454 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 12:34:39,454 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 12:34:39,454 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 12:34:39,455 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 12:34:39,455 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 12:34:39,456 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 12:34:39,456 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 12:34:39,456 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 12:34:39,456 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 12:34:39,456 - __main__ - INFO - ============================================================
2025-06-06 12:34:39,457 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 12:34:39,457 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 12:34:39,457 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 12:34:39,457 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 12:34:39,457 - __main__ - INFO - ============================================================
2025-06-06 12:34:39,626 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:34:40,049 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:34:40,103 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - SHORT
2025-06-06 12:34:40,103 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - SHORT (confidence: 100.00%)
2025-06-06 12:34:42,104 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:35:29,015 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:35:29,015 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:36:03,852 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:36:03,852 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:39:37,672 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 12:42:11,328 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:42:11,329 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:43:04,073 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 12:43:04,073 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:43:04 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:43:06,046 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:43:06 -0600] "GET / HTTP/1.1" 200 55375 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:43:06,051 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (2 total)
2025-06-06 12:43:06,051 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:34:40 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:43:06,191 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:43:59,002 - ui.ai_strategy_tuner - INFO - Switched to symbol: ETH-USDT
2025-06-06 12:43:59,003 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:43:59 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:44:07,570 - ui.ai_strategy_tuner - INFO - Switched to symbol: SOL-USDT
2025-06-06 12:44:07,571 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:44:07 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:44:09,101 - ui.ai_strategy_tuner - INFO - Switched to symbol: BTC-USDT
2025-06-06 12:44:09,103 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:44:09 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:44:37,676 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 12:44:46,737 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:44:46,738 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:45:39,402 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 12:45:39,402 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 12:45:39,407 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 12:45:39,407 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 12:45:39,408 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:45:39,408 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 12:45:39,408 - storage.live_store - INFO - Background tasks started
2025-06-06 12:45:39,408 - __main__ - INFO - [OK] Data store initialized
2025-06-06 12:45:39,408 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:45:39,408 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 12:45:39,408 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:45:39,408 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:45:39,408 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 12:45:39,408 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:45:39,408 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:45:39,408 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:45:39,408 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:45:39,408 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:45:39,408 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:45:39,410 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 12:45:39,410 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 12:45:39,410 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 12:45:39,410 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 12:45:39,410 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:45:39,410 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 12:45:39,410 - models.llm_integration - INFO - LLM Integration disabled in configuration
2025-06-06 12:45:39,410 - __main__ - INFO - [OK] LLM integration initialized (disabled)
2025-06-06 12:45:39,410 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:45:39,410 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 12:45:39,411 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 12:45:39,411 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:45:39,682 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 12:45:39,682 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 12:45:39,682 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 12:45:39,682 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:45:41,176 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 12:45:41,177 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 12:45:41,177 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 12:45:41,177 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 12:45:41,177 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 12:45:41,178 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 12:45:41,178 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 12:45:41,178 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 12:45:41,178 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 12:45:41,178 - __main__ - INFO - ============================================================
2025-06-06 12:45:41,178 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 12:45:41,178 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 12:45:41,178 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 12:45:41,178 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 12:45:41,178 - __main__ - INFO - ============================================================
2025-06-06 12:45:41,257 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - SHORT
2025-06-06 12:45:41,257 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - SHORT (confidence: 100.00%)
2025-06-06 12:45:41,419 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:45:41,433 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:45:43,387 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:46:04,281 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 12:46:04,283 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:46:04 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:47:02,799 - ui.ai_strategy_tuner - INFO - Applied Aggressive preset to BTC-USDT
2025-06-06 12:47:02,800 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:47:02 -0600] "POST /api/presets/apply HTTP/1.1" 200 349 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:47:03,386 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:47:03 -0600] "GET /api/settings/get HTTP/1.1" 200 333 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:47:56,262 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 12:47:56,262 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 12:47:56,267 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 12:47:56,268 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 12:47:56,268 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:47:56,268 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 12:47:56,268 - storage.live_store - INFO - Background tasks started
2025-06-06 12:47:56,268 - __main__ - INFO - [OK] Data store initialized
2025-06-06 12:47:56,268 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:47:56,268 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 12:47:56,268 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:47:56,268 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:47:56,268 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 12:47:56,268 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:47:56,268 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:47:56,268 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:47:56,268 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:47:56,268 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:47:56,268 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:47:56,268 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 12:47:56,268 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 12:47:56,268 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 12:47:56,268 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 12:47:56,269 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:47:56,269 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 12:47:56,269 - models.llm_integration - INFO - LLM Integration initialized (enabled: True, auto-detect: True)
2025-06-06 12:47:56,269 - models.llm_integration - INFO - LLM background processing started
2025-06-06 12:47:56,269 - __main__ - INFO - [OK] LLM integration initialized and started
2025-06-06 12:47:56,269 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:47:56,269 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 12:47:56,269 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 12:47:56,270 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:47:56,540 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 12:47:56,541 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 12:47:56,541 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 12:47:56,541 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:47:57,996 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 12:47:57,996 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 12:47:57,996 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 12:47:57,996 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 12:47:57,997 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 12:47:57,997 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 12:47:57,997 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 12:47:57,998 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 12:47:57,998 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 12:47:57,998 - __main__ - INFO - ============================================================
2025-06-06 12:47:57,998 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 12:47:57,998 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 12:47:57,998 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 12:47:57,998 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 12:47:57,998 - __main__ - INFO - ============================================================
2025-06-06 12:47:58,286 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:47:58,443 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:47:58,905 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - SHORT
2025-06-06 12:47:58,905 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - SHORT (confidence: 100.00%)
2025-06-06 12:47:59,742 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:48:00,956 - models.llm_integration - WARNING - [LLM] Server health check: UNAVAILABLE (status: 404)
2025-06-06 12:49:22,830 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 12:49:22,830 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:49:22 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:49:36,325 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:49:36 -0600] "GET /api/settings/get HTTP/1.1" 200 333 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:49:42,343 - ui.ai_strategy_tuner - INFO - Updated AI model settings for BTC-USDT: weights={'rsi': 0.25, 'vwap': 0.25, 'orderflow': 0.25, 'volatility': 0.25}, confidence=0.7, risk=1
2025-06-06 12:49:42,344 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:49:42 -0600] "POST /api/settings/update HTTP/1.1" 200 496 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:50:02,469 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:50:02,469 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 78.99%)
2025-06-06 12:51:57,691 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:51:57 -0600] "GET /api/settings/get HTTP/1.1" 200 333 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:51:58,616 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:51:58 -0600] "GET / HTTP/1.1" 200 55375 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:51:58,831 - ui.ai_strategy_tuner - INFO - WebSocket client connected (4 total)
2025-06-06 12:52:56,304 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 12:53:13,335 - models.llm_integration - WARNING - [LLM] Server health check: UNAVAILABLE (status: 404)
2025-06-06 12:55:17,501 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 12:55:17,502 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 12:55:17,507 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 12:55:17,507 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 12:55:17,509 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:55:17,510 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 12:55:17,510 - storage.live_store - INFO - Background tasks started
2025-06-06 12:55:17,510 - __main__ - INFO - [OK] Data store initialized
2025-06-06 12:55:17,510 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:55:17,510 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 12:55:17,510 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:55:17,510 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:55:17,510 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 12:55:17,510 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:55:17,510 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:55:17,510 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:55:17,510 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:55:17,510 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:55:17,511 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:55:17,511 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 12:55:17,511 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 12:55:17,511 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 12:55:17,511 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 12:55:17,511 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:55:17,511 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 12:55:17,511 - models.llm_integration - INFO - LLM Integration initialized (enabled: True, auto-detect: True)
2025-06-06 12:55:17,512 - models.llm_integration - INFO - LLM background processing started
2025-06-06 12:55:17,512 - __main__ - INFO - [OK] LLM integration initialized and started
2025-06-06 12:55:17,512 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:55:17,512 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 12:55:17,513 - utils.system_monitor - INFO - System Monitor initialized
2025-06-06 12:55:17,514 - __main__ - INFO - [OK] System monitor initialized
2025-06-06 12:55:17,514 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 12:55:17,514 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:55:17,544 - utils.system_monitor - INFO - Starting system monitoring (interval: 60s)
2025-06-06 12:55:18,032 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 12:55:18,033 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 12:55:18,033 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 12:55:18,033 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:55:19,473 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 12:55:19,473 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 12:55:19,473 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 12:55:19,474 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 12:55:19,474 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 12:55:19,475 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 12:55:19,475 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 12:55:19,475 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 12:55:19,475 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 12:55:19,475 - __main__ - INFO - ============================================================
2025-06-06 12:55:19,475 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 12:55:19,475 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 12:55:19,475 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 12:55:19,475 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 12:55:19,476 - __main__ - INFO - ============================================================
2025-06-06 12:55:19,514 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:55:19,530 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:55:19,545 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:55:19,779 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - SHORT
2025-06-06 12:55:19,779 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - SHORT (confidence: 100.00%)
2025-06-06 12:55:20,975 - ui.ai_strategy_tuner - INFO - WebSocket client connected (4 total)
2025-06-06 12:55:22,229 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 12:55:25,708 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 12:55:25,709 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 100.00%)
2025-06-06 12:56:17,654 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 83.5%
2025-06-06 12:56:17,665 - utils.system_monitor - INFO - Garbage collection freed 116 objects
2025-06-06 12:56:17,791 - utils.system_monitor - WARNING - Memory usage high: 83.5%
2025-06-06 12:56:21,120 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:56:21,120 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:56:24,615 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 12:56:24,615 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8250.00%)
2025-06-06 12:57:17,913 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.8%
2025-06-06 12:57:17,918 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 12:57:18,033 - utils.system_monitor - WARNING - Memory usage high: 81.9%
2025-06-06 12:57:36,234 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 12:57:36,234 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 85.00%)
2025-06-06 12:57:53,334 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:57:53,335 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 78.72%)
2025-06-06 12:58:18,177 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 87.6%
2025-06-06 12:58:18,186 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 12:58:18,297 - utils.system_monitor - WARNING - Memory usage high: 87.6%
2025-06-06 12:58:24,674 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 12:58:24,674 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8250.00%)
2025-06-06 12:58:27,014 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:58:27,014 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:59:13,359 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 12:59:13,359 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 85.00%)
2025-06-06 12:59:18,430 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 84.1%
2025-06-06 12:59:18,437 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 12:59:18,554 - utils.system_monitor - WARNING - Memory usage high: 84.1%
2025-06-06 13:00:02,095 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:00:02,095 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:00:06,284 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:00:06,284 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:00:17,536 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 13:00:18,683 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 82.2%
2025-06-06 13:00:18,689 - utils.system_monitor - INFO - Garbage collection freed 159 objects
2025-06-06 13:00:18,809 - utils.system_monitor - WARNING - Memory usage high: 82.3%
2025-06-06 13:00:49,509 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 13:00:53,237 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:00:53,238 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:01:05,965 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:01:05,966 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 73.61%)
2025-06-06 13:01:18,918 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.1%
2025-06-06 13:01:18,924 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 13:01:19,043 - utils.system_monitor - WARNING - Memory usage high: 81.1%
2025-06-06 13:01:41,879 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:01:41,880 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 7360.00%)
2025-06-06 13:02:19,154 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 83.2%
2025-06-06 13:02:19,159 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:02:19,278 - utils.system_monitor - WARNING - Memory usage high: 82.2%
2025-06-06 13:02:30,651 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:02:30,651 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:02:56,518 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:02:56,518 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:03:19,395 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 83.9%
2025-06-06 13:03:19,401 - utils.system_monitor - INFO - Garbage collection freed 176 objects
2025-06-06 13:03:19,518 - utils.system_monitor - WARNING - Memory usage high: 83.9%
2025-06-06 13:03:19,519 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:03:19,519 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 85.00%)
2025-06-06 13:03:28,559 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:03:28,559 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:04:08,183 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:04:08,183 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:04:19,631 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 85.3%
2025-06-06 13:04:19,637 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:04:19,755 - utils.system_monitor - WARNING - Memory usage high: 85.3%
2025-06-06 13:04:56,730 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:04:56,731 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8250.00%)
2025-06-06 13:05:17,542 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 13:05:19,885 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 10.8%, Memory: 80.0%, Signals/min: 0.8
2025-06-06 13:05:19,891 - utils.system_monitor - INFO - Garbage collection freed 159 objects
2025-06-06 13:05:20,010 - utils.system_monitor - WARNING - Memory usage high: 79.9%
2025-06-06 13:05:45,287 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:05:45,287 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8250.00%)
2025-06-06 13:05:55,679 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:05:55,680 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:05:58,021 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 13:06:20,129 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 82.6%
2025-06-06 13:06:20,135 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 13:06:20,251 - utils.system_monitor - WARNING - Memory usage high: 82.5%
2025-06-06 13:06:34,289 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:06:34,289 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:06:42,551 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 13:06:42,551 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:06:42 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:07:08,500 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:07:08,500 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:07:20,387 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.6%
2025-06-06 13:07:20,394 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:07:20,508 - utils.system_monitor - WARNING - Memory usage high: 81.6%
2025-06-06 13:07:22,785 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:07:22,786 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 85.00%)
2025-06-06 13:07:39,433 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:07:39,433 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 77.00%)
2025-06-06 13:08:11,319 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:08:11,319 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8250.00%)
2025-06-06 13:08:20,625 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.3%
2025-06-06 13:08:20,632 - utils.system_monitor - INFO - Garbage collection freed 252 objects
2025-06-06 13:08:20,750 - utils.system_monitor - WARNING - Memory usage high: 81.2%
2025-06-06 13:08:56,119 - __main__ - INFO - [SIGNAL] Received signal 2
2025-06-06 13:08:56,330 - __main__ - INFO - [STOP] Stopping Epinnox V6 AI Strategy Tuner...
2025-06-06 13:08:56,331 - feeds.binance_ws_client - INFO - 🔌 Disconnecting from Binance WebSocket
2025-06-06 13:08:59,878 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:08:59,878 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8250.00%)
2025-06-06 13:09:05,685 - storage.live_store - INFO - Live Data Store shutdown complete
2025-06-06 13:09:05,685 - __main__ - INFO - [OK] Application stopped gracefully
2025-06-06 13:09:05,685 - __main__ - INFO - [COMPLETE] Epinnox V6 AI Strategy Tuner completed successfully
2025-06-06 13:09:05,686 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (3 total)
2025-06-06 13:09:05,686 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (2 total)
2025-06-06 13:09:05,686 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 13:09:05,686 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (0 total)
2025-06-06 13:12:20,402 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 13:12:20,403 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 13:12:20,409 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 13:12:20,410 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 13:12:20,410 - storage.live_store - INFO - State loaded from cache file
2025-06-06 13:12:20,410 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 13:12:20,410 - storage.live_store - INFO - Background tasks started
2025-06-06 13:12:20,410 - __main__ - INFO - [OK] Data store initialized
2025-06-06 13:12:20,410 - analytics.signal_tracker - INFO - Signal Tracker initialized (TP: 2.0%, SL: 1.0%)
2025-06-06 13:12:20,411 - analytics.performance_analyzer - INFO - Performance Analyzer initialized
2025-06-06 13:12:20,411 - __main__ - INFO - [OK] Signal tracking and performance analytics initialized
2025-06-06 13:12:20,411 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 13:12:20,411 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 13:12:20,411 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 13:12:20,411 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 13:12:20,411 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 13:12:20,411 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 13:12:20,411 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 13:12:20,411 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 13:12:20,411 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 13:12:20,411 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 13:12:20,411 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 13:12:20,411 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 13:12:20,411 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 13:12:20,411 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 13:12:20,411 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 13:12:20,412 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 13:12:20,412 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 13:12:20,412 - models.llm_integration - INFO - LLM Integration initialized (enabled: True, auto-detect: True)
2025-06-06 13:12:20,412 - models.llm_integration - INFO - LLM background processing started
2025-06-06 13:12:20,412 - __main__ - INFO - [OK] LLM integration initialized and started
2025-06-06 13:12:20,412 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 13:12:20,412 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 13:12:20,412 - utils.system_monitor - INFO - System Monitor initialized
2025-06-06 13:12:20,412 - __main__ - INFO - [OK] System monitor initialized
2025-06-06 13:12:20,412 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 13:12:20,412 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 13:12:20,435 - utils.system_monitor - INFO - Starting system monitoring (interval: 60s)
2025-06-06 13:12:20,686 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 13:12:20,687 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 13:12:20,687 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 13:12:20,687 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 13:12:22,135 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 13:12:22,135 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 13:12:22,135 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 13:12:22,136 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 13:12:22,136 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 13:12:22,137 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 13:12:22,137 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 13:12:22,137 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 13:12:22,137 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 13:12:22,137 - __main__ - INFO - ============================================================
2025-06-06 13:12:22,137 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 13:12:22,137 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 13:12:22,137 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 13:12:22,137 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 13:12:22,137 - __main__ - INFO - ============================================================
2025-06-06 13:12:22,308 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 13:12:22,339 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 13:12:22,420 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:23,785 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:24,127 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 13:12:24,901 - ui.ai_strategy_tuner - INFO - WebSocket client connected (4 total)
2025-06-06 13:12:25,055 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:26,154 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:27,664 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:29,882 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:31,002 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:32,442 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:33,494 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:34,616 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:36,003 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:37,501 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:38,625 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:39,961 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:41,624 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:42,909 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:43,945 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:45,117 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:46,162 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:47,847 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:49,010 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:49,040 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:12:49 -0600] "GET / HTTP/1.1" 200 59882 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:12:50,617 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:50,872 - ui.ai_strategy_tuner - INFO - WebSocket client connected (5 total)
2025-06-06 13:12:50,928 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:12:50 -0600] "GET /api/data HTTP/1.1" 200 1308 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:12:51,889 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:53,307 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:54,500 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:56,676 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:58,328 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:59,416 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:00,502 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:01,624 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:02,682 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:03,692 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:05,176 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:06,841 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:07,881 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 13:13:08,802 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:11,952 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:13,260 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:14,617 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:16,095 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:17,444 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:18,491 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:19,803 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:20,573 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.7%, High error rate: 100.0%
2025-06-06 13:13:20,582 - utils.system_monitor - INFO - Garbage collection freed 131 objects
2025-06-06 13:13:20,697 - utils.system_monitor - WARNING - Memory usage high: 81.7%
2025-06-06 13:13:21,461 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:22,587 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:23,452 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:13:23 -0600] "GET / HTTP/1.1" 200 59882 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:13:23,459 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (4 total)
2025-06-06 13:13:23,460 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:12:50 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:13:23,526 - ui.ai_strategy_tuner - INFO - WebSocket client connected (5 total)
2025-06-06 13:13:23,533 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:13:23 -0600] "GET /api/data HTTP/1.1" 200 1298 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:13:23,948 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:24,909 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:13:24 -0600] "GET / HTTP/1.1" 200 59882 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:13:24,913 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (4 total)
2025-06-06 13:13:24,914 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:13:23 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:13:24,992 - ui.ai_strategy_tuner - INFO - WebSocket client connected (5 total)
2025-06-06 13:13:24,993 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:13:24 -0600] "GET /api/data HTTP/1.1" 200 1315 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:13:25,721 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:27,402 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:28,411 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:28,774 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 13:13:28,774 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:13:28 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:13:30,055 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:31,527 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:34,435 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:35,522 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:36,561 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:37,669 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:38,686 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:39,955 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:40,961 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:42,022 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:43,207 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:45,767 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:46,789 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:48,657 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:50,440 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:51,646 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:52,928 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:54,610 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:55,893 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:57,473 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:59,033 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:00,621 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:01,714 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:02,795 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:05,700 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:06,747 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:07,809 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:09,604 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:10,763 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:12,873 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:13,909 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:15,446 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:16,801 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:18,335 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:19,769 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:20,820 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.1%, High error rate: 100.0%
2025-06-06 13:14:20,942 - utils.system_monitor - WARNING - Memory usage high: 82.2%
2025-06-06 13:14:20,943 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:22,020 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:23,264 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:24,313 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:25,396 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:26,408 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:27,578 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:28,627 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:30,950 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:32,132 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:33,570 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:35,014 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
