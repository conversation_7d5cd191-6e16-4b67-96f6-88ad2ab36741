2025-06-06 11:45:31,492 - storage.live_store - INFO - Live Data Store initialized
2025-06-06 11:45:31,492 - storage.live_store - INFO - Background tasks started
2025-06-06 11:45:31,494 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 11:45:31,495 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 11:45:31,495 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 11:45:31,496 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 11:45:31,496 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 11:45:31,498 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 11:45:31,500 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 11:45:31,500 - models.llm_integration - INFO - LLM background processing started
2025-06-06 11:45:31,503 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 11:45:31,506 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 11:55:43,009 - storage.live_store - INFO - Live Data Store initialized
2025-06-06 11:55:43,010 - storage.live_store - INFO - Background tasks started
2025-06-06 11:55:43,012 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 11:55:43,014 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 11:55:43,014 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 11:55:43,014 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 11:55:43,014 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 11:55:43,014 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 11:55:43,014 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 11:55:43,014 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 11:55:43,014 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 11:55:43,016 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 11:55:43,020 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 11:55:43,020 - models.llm_integration - INFO - LLM background processing started
2025-06-06 11:55:43,022 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 11:55:43,026 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 11:55:43,219 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 11:55:45,328 - __main__ - ERROR - WebSocket error: server rejected WebSocket connection: HTTP 404
2025-06-06 11:55:45,346 - __main__ - INFO - ============================================================
2025-06-06 11:55:45,354 - __main__ - INFO - ============================================================
2025-06-06 11:56:24,432 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:56:24 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:56:24,636 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:56:24 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:56:24,637 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 11:56:24,642 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:56:24 -0600] "GET /favicon.ico HTTP/1.1" 404 172 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:57:59,254 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:57:59 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:57:59,469 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 11:57:59,469 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:57:59 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:03,679 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 11:58:03,679 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:03 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:31,826 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:31 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:31,829 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 11:58:31,830 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:56:24 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:31,904 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:31 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:31,904 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 11:58:32,483 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:32 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:32,486 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 11:58:32,487 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:31 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 11:58:32,547 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 11:58:32,547 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:32 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:00:43,056 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 12:01:25,836 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:25 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:25,845 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:01:25,845 - aiohttp.access - INFO - ::1 [06/Jun/2025:10:58:32 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:25,937 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:01:25,938 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:25 -0600] "GET /api/data HTTP/1.1" 200 706 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:27,602 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:27 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:27,606 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:01:27,606 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:25 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:27,680 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:01:27,681 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:27 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:28,223 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:28 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:28,228 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:01:28,228 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:27 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:28,315 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:01:28,316 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:28 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:01:31,414 - ui.ai_strategy_tuner - INFO - Applied Conservative preset to BTC-USDT
2025-06-06 12:01:31,415 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:01:31 -0600] "POST /api/presets/apply HTTP/1.1" 200 351 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:02:23,083 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:02:23,084 - storage.live_store - INFO - Live Data Store initialized
2025-06-06 12:02:23,084 - storage.live_store - INFO - Background tasks started
2025-06-06 12:02:23,087 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:02:23,093 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:02:23,094 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:02:23,094 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:02:23,094 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:02:23,094 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:02:23,094 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:02:23,094 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:02:23,094 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:02:23,099 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:02:23,102 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 12:02:23,102 - models.llm_integration - INFO - LLM background processing started
2025-06-06 12:02:23,104 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:02:23,108 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:02:23,274 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:02:25,299 - __main__ - ERROR - WebSocket error: server rejected WebSocket connection: HTTP 404
2025-06-06 12:02:25,312 - __main__ - INFO - ============================================================
2025-06-06 12:02:25,317 - __main__ - INFO - ============================================================
2025-06-06 12:02:25,493 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:03:05,942 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:05 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:03:06,132 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:03:06,133 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:06 -0600] "GET /api/data HTTP/1.1" 200 706 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:03:45,335 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:45 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:03:46,114 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:03:46,115 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:46 -0600] "GET /api/data HTTP/1.1" 200 706 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:03:49,229 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 12:03:49,230 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:49 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:05:48,226 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:05:48 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:05:48,231 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (2 total)
2025-06-06 12:05:48,231 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:03:46 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:05:48,315 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:05:48,316 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:05:48 -0600] "GET /api/data HTTP/1.1" 200 707 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:05:55,331 - ui.ai_strategy_tuner - INFO - Applied Aggressive preset to BTC-USDT
2025-06-06 12:05:55,332 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:05:55 -0600] "POST /api/presets/apply HTTP/1.1" 200 349 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:06:01,379 - ui.ai_strategy_tuner - INFO - Switched to symbol: ETH-USDT
2025-06-06 12:06:01,379 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:06:01 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:06:08,711 - __main__ - INFO - Received signal 2
2025-06-06 12:06:09,634 - storage.live_store - INFO - Live Data Store shutdown complete
2025-06-06 12:06:09,640 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (2 total)
2025-06-06 12:06:09,640 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:06:09,641 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (0 total)
2025-06-06 12:07:47,711 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 12:07:47,711 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 12:07:47,716 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 12:07:47,716 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 12:07:47,717 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:07:47,717 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 12:07:47,717 - storage.live_store - INFO - Background tasks started
2025-06-06 12:07:47,717 - __main__ - INFO - [OK] Data store initialized
2025-06-06 12:07:47,717 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:07:47,717 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 12:07:47,717 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:07:47,717 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:07:47,717 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:07:47,717 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:07:47,717 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:07:47,717 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:07:47,717 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:07:47,717 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:07:47,717 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance backup)
2025-06-06 12:07:47,717 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:07:47,717 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 12:07:47,717 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 12:07:47,717 - models.llm_integration - INFO - LLM background processing started
2025-06-06 12:07:47,717 - __main__ - INFO - [OK] LLM integration initialized
2025-06-06 12:07:47,718 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:07:47,718 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 12:07:47,718 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 12:07:47,718 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:07:47,877 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 12:07:47,879 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 12:07:47,879 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:07:49,917 - feeds.binance_ws_client - ERROR - ❌ Failed to connect to Binance WebSocket: server rejected WebSocket connection: HTTP 404
2025-06-06 12:07:49,917 - __main__ - ERROR - [WS_ERROR] WebSocket error: server rejected WebSocket connection: HTTP 404
2025-06-06 12:07:49,917 - __main__ - ERROR - [ERROR] Both HTX and Binance connections failed
2025-06-06 12:07:49,917 - __main__ - INFO - [MODE] Continuing without WebSocket for testing purposes
2025-06-06 12:07:49,917 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 12:07:49,918 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 12:07:49,918 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 12:07:49,918 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 12:07:49,918 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 12:07:49,919 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 12:07:49,919 - __main__ - INFO - ============================================================
2025-06-06 12:07:49,919 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 12:07:49,919 - __main__ - INFO - [DATA] Market data source: None (Testing Mode)
2025-06-06 12:07:49,919 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 12:07:49,919 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 12:07:49,919 - __main__ - INFO - ============================================================
2025-06-06 12:07:50,082 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:07:50,410 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:07:51,451 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:08:05,601 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:08:05 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:08:06,387 - ui.ai_strategy_tuner - INFO - WebSocket client connected (4 total)
2025-06-06 12:08:06,388 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:08:06 -0600] "GET /api/data HTTP/1.1" 200 706 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:08:51,482 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 12:08:51,482 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:08:51 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:09:29,354 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (3 total)
2025-06-06 12:09:29,354 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:07:50 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:09:29,917 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (2 total)
2025-06-06 12:09:29,918 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:07:50 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:09:30,428 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:09:30,429 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:07:51 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:10:12,382 - __main__ - INFO - [SIGNAL] Received signal 2
2025-06-06 12:10:13,343 - __main__ - INFO - [STOP] Stopping Epinnox V6 AI Strategy Tuner...
2025-06-06 12:10:13,344 - storage.live_store - INFO - Live Data Store shutdown complete
2025-06-06 12:10:13,344 - __main__ - INFO - [OK] Application stopped gracefully
2025-06-06 12:10:13,344 - __main__ - INFO - [COMPLETE] Epinnox V6 AI Strategy Tuner completed successfully
2025-06-06 12:10:13,344 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (0 total)
2025-06-06 12:12:10,332 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 12:12:10,334 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 12:12:10,340 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 12:12:10,340 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 12:12:10,340 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:12:10,340 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 12:12:10,340 - storage.live_store - INFO - Background tasks started
2025-06-06 12:12:10,340 - __main__ - INFO - [OK] Data store initialized
2025-06-06 12:12:10,340 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:12:10,340 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 12:12:10,340 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:12:10,340 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:12:10,340 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 12:12:10,340 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:12:10,341 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:12:10,341 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:12:10,341 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:12:10,341 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:12:10,341 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:12:10,341 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 12:12:10,341 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 12:12:10,341 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 12:12:10,341 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 12:12:10,341 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:12:10,341 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 12:12:10,341 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 12:12:10,342 - models.llm_integration - INFO - LLM background processing started
2025-06-06 12:12:10,342 - __main__ - INFO - [OK] LLM integration initialized
2025-06-06 12:12:10,342 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:12:10,342 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 12:12:10,342 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 12:12:10,342 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:12:10,618 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 12:12:10,619 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 12:12:10,619 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 12:12:10,619 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:12:12,285 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 12:12:12,285 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 12:12:12,285 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 12:12:12,286 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 12:12:12,286 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 12:12:12,287 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 12:12:12,287 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 12:12:12,287 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 12:12:12,288 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 12:12:12,288 - __main__ - INFO - ============================================================
2025-06-06 12:12:12,288 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 12:12:12,288 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 12:12:12,288 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 12:12:12,289 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 12:12:12,289 - __main__ - INFO - ============================================================
2025-06-06 12:12:13,319 - storage.live_store - ERROR - Error storing signal: 'BTC-USDT'
2025-06-06 12:12:13,320 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 100.00%)
2025-06-06 12:12:13,320 - models.llm_integration - ERROR - LLM API call error: unsupported format string passed to NoneType.__format__
2025-06-06 12:12:18,079 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:12:55,359 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:13:40,373 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:14:25,391 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:15:10,401 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:15:18,600 - storage.live_store - ERROR - Error storing signal: 'BTC-USDT'
2025-06-06 12:15:18,601 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:15:18,601 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:15:54,635 - storage.live_store - ERROR - Error storing signal: 'BTC-USDT'
2025-06-06 12:15:54,635 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:15:54,636 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:15:55,395 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:15:57,076 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:15:57 -0600] "GET / HTTP/1.1" 200 29418 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:15:57,371 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:15:57,372 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:15:57 -0600] "GET /api/data HTTP/1.1" 200 4147 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:16:28,995 - storage.live_store - ERROR - Error storing signal: 'BTC-USDT'
2025-06-06 12:16:28,995 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:16:28,995 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:16:30,968 - ui.ai_strategy_tuner - INFO - Applied Conservative preset to BTC-USDT
2025-06-06 12:16:30,969 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:16:30 -0600] "POST /api/presets/apply HTTP/1.1" 200 351 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:16:40,403 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:17:10,377 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 12:17:25,404 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:17:33,802 - storage.live_store - ERROR - Error storing signal: 'BTC-USDT'
2025-06-06 12:17:33,802 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 73.21%)
2025-06-06 12:17:33,803 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:18:09,968 - storage.live_store - ERROR - Error storing signal: 'BTC-USDT'
2025-06-06 12:18:09,968 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:18:09,968 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:18:10,417 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:18:55,426 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:19:26,547 - storage.live_store - ERROR - Error storing signal: 'BTC-USDT'
2025-06-06 12:19:26,548 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:19:26,548 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:19:40,443 - models.llm_integration - ERROR - LLM API call error: Invalid format specifier
2025-06-06 12:19:48,515 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 12:19:48,515 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 12:19:51,864 - __main__ - INFO - [SIGNAL] Received signal 2
2025-06-06 12:19:52,727 - __main__ - INFO - [STOP] Stopping Epinnox V6 AI Strategy Tuner...
2025-06-06 12:19:52,728 - storage.live_store - INFO - Live Data Store shutdown complete
2025-06-06 12:19:52,728 - __main__ - INFO - [OK] Application stopped gracefully
2025-06-06 12:19:52,728 - __main__ - INFO - [COMPLETE] Epinnox V6 AI Strategy Tuner completed successfully
2025-06-06 12:19:52,728 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 12:19:52,728 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (0 total)
2025-06-06 12:24:16,593 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 12:24:16,593 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 12:24:16,598 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 12:24:16,598 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 12:24:16,599 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:24:16,599 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 12:24:16,599 - storage.live_store - INFO - Background tasks started
2025-06-06 12:24:16,599 - __main__ - INFO - [OK] Data store initialized
2025-06-06 12:24:16,599 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:24:16,599 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 12:24:16,600 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:24:16,600 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:24:16,600 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 12:24:16,600 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:24:16,600 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:24:16,600 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:24:16,600 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:24:16,600 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:24:16,600 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:24:16,600 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 12:24:16,600 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 12:24:16,600 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 12:24:16,600 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 12:24:16,602 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:24:16,602 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 12:24:16,602 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 12:24:16,602 - models.llm_integration - INFO - LLM background processing started
2025-06-06 12:24:16,602 - __main__ - INFO - [OK] LLM integration initialized
2025-06-06 12:24:16,603 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:24:16,603 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 12:24:16,603 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 12:24:16,603 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:24:16,930 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 12:24:16,930 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 12:24:16,930 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 12:24:16,930 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:24:18,465 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 12:24:18,465 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 12:24:18,465 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 12:24:18,466 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 12:24:18,466 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 12:24:18,466 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 12:24:18,467 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 12:24:18,467 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 12:24:18,467 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 12:24:18,467 - __main__ - INFO - ============================================================
2025-06-06 12:24:18,467 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 12:24:18,467 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 12:24:18,467 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 12:24:18,467 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 12:24:18,467 - __main__ - INFO - ============================================================
2025-06-06 12:24:18,633 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:24:20,622 - storage.live_store - INFO - 📈 Signal stored: BTC-USDT - LONG
2025-06-06 12:24:20,622 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 100.00%)
2025-06-06 12:24:21,375 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:24:22,788 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:25:01,637 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:25:01 -0600] "GET / HTTP/1.1" 200 49239 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:25:01,958 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:25:01,959 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:25:01 -0600] "GET /api/data HTTP/1.1" 200 2566 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:25:03,695 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:25:04,472 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:25:04 -0600] "GET /api/settings/get HTTP/1.1" 200 333 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:25:04,961 - storage.live_store - INFO - 📈 Signal stored: BTC-USDT - LONG
2025-06-06 12:25:04,962 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 78.33%)
2025-06-06 12:25:07,003 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:25:10,539 - ui.ai_strategy_tuner - INFO - Applied Conservative preset to BTC-USDT
2025-06-06 12:25:10,541 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:25:10 -0600] "POST /api/presets/apply HTTP/1.1" 200 351 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:25:50,754 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:26:37,804 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:27:24,841 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:27:43,392 - ui.ai_strategy_tuner - INFO - Switched to symbol: ETH-USDT
2025-06-06 12:27:43,392 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:27:43 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:27:44,037 - ui.ai_strategy_tuner - INFO - Switched to symbol: DOGE-USDT
2025-06-06 12:27:44,037 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:27:44 -0600] "POST /api/symbol/switch HTTP/1.1" 200 348 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:27:44,366 - ui.ai_strategy_tuner - INFO - Switched to symbol: SOL-USDT
2025-06-06 12:27:44,367 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:27:44 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:27:44,622 - ui.ai_strategy_tuner - INFO - Switched to symbol: ADA-USDT
2025-06-06 12:27:44,624 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:27:44 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:28:01,363 - ui.ai_strategy_tuner - INFO - Switched to symbol: BTC-USDT
2025-06-06 12:28:01,364 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:28:01 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:28:11,905 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:28:19,771 - ui.ai_strategy_tuner - INFO - Applied Aggressive preset to BTC-USDT
2025-06-06 12:28:19,772 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:28:19 -0600] "POST /api/presets/apply HTTP/1.1" 200 349 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:28:24,975 - ui.ai_strategy_tuner - INFO - Switched to symbol: DOGE-USDT
2025-06-06 12:28:24,976 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:28:24 -0600] "POST /api/symbol/switch HTTP/1.1" 200 348 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:28:29,411 - storage.live_store - INFO - 📈 Signal stored: BTC-USDT - LONG
2025-06-06 12:28:29,411 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:28:30,773 - ui.ai_strategy_tuner - INFO - Switched to symbol: ETH-USDT
2025-06-06 12:28:30,773 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:28:30 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:28:31,454 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:28:36,634 - ui.ai_strategy_tuner - INFO - Switched to symbol: BTC-USDT
2025-06-06 12:28:36,635 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:28:36 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:28:39,732 - ui.ai_strategy_tuner - INFO - Switched to symbol: ETH-USDT
2025-06-06 12:28:39,733 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:28:39 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:28:47,317 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to ETH-USDT
2025-06-06 12:28:47,317 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:28:47 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:28:58,951 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:29:16,641 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 12:29:21,848 - storage.live_store - INFO - 📈 Signal stored: BTC-USDT - LONG
2025-06-06 12:29:21,849 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:29:23,829 - ui.ai_strategy_tuner - INFO - Applied Aggressive preset to ETH-USDT
2025-06-06 12:29:23,829 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:29:23 -0600] "POST /api/presets/apply HTTP/1.1" 200 349 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:29:23,889 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:29:25,901 - ui.ai_strategy_tuner - INFO - Switched to symbol: BTC-USDT
2025-06-06 12:29:25,903 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:29:25 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:29:46,021 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:30:02,814 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 12:30:02,815 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 12:30:02,820 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 12:30:02,820 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 12:30:02,820 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:30:02,820 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 12:30:02,820 - storage.live_store - INFO - Background tasks started
2025-06-06 12:30:02,821 - __main__ - INFO - [OK] Data store initialized
2025-06-06 12:30:02,821 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:30:02,821 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 12:30:02,821 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:30:02,821 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:30:02,821 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 12:30:02,821 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:30:02,821 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:30:02,821 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:30:02,821 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:30:02,821 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:30:02,821 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:30:02,821 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 12:30:02,821 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 12:30:02,821 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 12:30:02,821 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 12:30:02,822 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:30:02,822 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 12:30:02,822 - models.llm_integration - INFO - LLM Integration initialized (enabled: True)
2025-06-06 12:30:02,822 - models.llm_integration - INFO - LLM background processing started
2025-06-06 12:30:02,822 - __main__ - INFO - [OK] LLM integration initialized
2025-06-06 12:30:02,822 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:30:02,822 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 12:30:02,822 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 12:30:02,822 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:30:03,090 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 12:30:03,090 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 12:30:03,090 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 12:30:03,090 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:30:04,528 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 12:30:04,528 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 12:30:04,528 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 12:30:04,529 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 12:30:04,529 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 12:30:04,530 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 12:30:04,530 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 12:30:04,530 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 12:30:04,530 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 12:30:04,530 - __main__ - INFO - ============================================================
2025-06-06 12:30:04,530 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 12:30:04,530 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 12:30:04,531 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 12:30:04,531 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 12:30:04,531 - __main__ - INFO - ============================================================
2025-06-06 12:30:04,648 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:30:05,099 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:30:05,100 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 100.00%)
2025-06-06 12:30:05,438 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:30:07,148 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:30:07,477 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:30:49,897 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:31:36,925 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:32:23,977 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:33:00,518 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:33:00,518 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 78.70%)
2025-06-06 12:33:02,560 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:33:10,989 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:33:58,049 - models.llm_integration - ERROR - LLM API error: 404
2025-06-06 12:34:37,657 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 12:34:37,657 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 12:34:37,663 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 12:34:37,663 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 12:34:37,663 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:34:37,663 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 12:34:37,663 - storage.live_store - INFO - Background tasks started
2025-06-06 12:34:37,663 - __main__ - INFO - [OK] Data store initialized
2025-06-06 12:34:37,663 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:34:37,663 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 12:34:37,663 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:34:37,663 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:34:37,663 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 12:34:37,663 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:34:37,664 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:34:37,664 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:34:37,664 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:34:37,664 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:34:37,664 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:34:37,664 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 12:34:37,664 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 12:34:37,664 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 12:34:37,664 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 12:34:37,664 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:34:37,664 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 12:34:37,664 - models.llm_integration - INFO - LLM Integration disabled in configuration
2025-06-06 12:34:37,664 - __main__ - INFO - [OK] LLM integration initialized
2025-06-06 12:34:37,664 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:34:37,664 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 12:34:37,664 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 12:34:37,665 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:34:37,925 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 12:34:37,925 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 12:34:37,925 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 12:34:37,925 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:34:39,454 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 12:34:39,454 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 12:34:39,454 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 12:34:39,455 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 12:34:39,455 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 12:34:39,456 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 12:34:39,456 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 12:34:39,456 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 12:34:39,456 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 12:34:39,456 - __main__ - INFO - ============================================================
2025-06-06 12:34:39,457 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 12:34:39,457 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 12:34:39,457 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 12:34:39,457 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 12:34:39,457 - __main__ - INFO - ============================================================
2025-06-06 12:34:39,626 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:34:40,049 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:34:40,103 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - SHORT
2025-06-06 12:34:40,103 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - SHORT (confidence: 100.00%)
2025-06-06 12:34:42,104 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:35:29,015 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:35:29,015 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:36:03,852 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:36:03,852 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:39:37,672 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 12:42:11,328 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:42:11,329 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:43:04,073 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 12:43:04,073 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:43:04 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:43:06,046 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:43:06 -0600] "GET / HTTP/1.1" 200 55375 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:43:06,051 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (2 total)
2025-06-06 12:43:06,051 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:34:40 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:43:06,191 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:43:59,002 - ui.ai_strategy_tuner - INFO - Switched to symbol: ETH-USDT
2025-06-06 12:43:59,003 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:43:59 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:44:07,570 - ui.ai_strategy_tuner - INFO - Switched to symbol: SOL-USDT
2025-06-06 12:44:07,571 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:44:07 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:44:09,101 - ui.ai_strategy_tuner - INFO - Switched to symbol: BTC-USDT
2025-06-06 12:44:09,103 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:44:09 -0600] "POST /api/symbol/switch HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:44:37,676 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 12:44:46,737 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:44:46,738 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:45:39,402 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 12:45:39,402 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 12:45:39,407 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 12:45:39,407 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 12:45:39,408 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:45:39,408 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 12:45:39,408 - storage.live_store - INFO - Background tasks started
2025-06-06 12:45:39,408 - __main__ - INFO - [OK] Data store initialized
2025-06-06 12:45:39,408 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:45:39,408 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 12:45:39,408 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:45:39,408 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:45:39,408 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 12:45:39,408 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:45:39,408 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:45:39,408 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:45:39,408 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:45:39,408 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:45:39,408 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:45:39,410 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 12:45:39,410 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 12:45:39,410 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 12:45:39,410 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 12:45:39,410 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:45:39,410 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 12:45:39,410 - models.llm_integration - INFO - LLM Integration disabled in configuration
2025-06-06 12:45:39,410 - __main__ - INFO - [OK] LLM integration initialized (disabled)
2025-06-06 12:45:39,410 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:45:39,410 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 12:45:39,411 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 12:45:39,411 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:45:39,682 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 12:45:39,682 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 12:45:39,682 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 12:45:39,682 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:45:41,176 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 12:45:41,177 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 12:45:41,177 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 12:45:41,177 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 12:45:41,177 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 12:45:41,178 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 12:45:41,178 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 12:45:41,178 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 12:45:41,178 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 12:45:41,178 - __main__ - INFO - ============================================================
2025-06-06 12:45:41,178 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 12:45:41,178 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 12:45:41,178 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 12:45:41,178 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 12:45:41,178 - __main__ - INFO - ============================================================
2025-06-06 12:45:41,257 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - SHORT
2025-06-06 12:45:41,257 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - SHORT (confidence: 100.00%)
2025-06-06 12:45:41,419 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:45:41,433 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:45:43,387 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:46:04,281 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 12:46:04,283 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:46:04 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:47:02,799 - ui.ai_strategy_tuner - INFO - Applied Aggressive preset to BTC-USDT
2025-06-06 12:47:02,800 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:47:02 -0600] "POST /api/presets/apply HTTP/1.1" 200 349 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:47:03,386 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:47:03 -0600] "GET /api/settings/get HTTP/1.1" 200 333 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:47:56,262 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 12:47:56,262 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 12:47:56,267 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 12:47:56,268 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 12:47:56,268 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:47:56,268 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 12:47:56,268 - storage.live_store - INFO - Background tasks started
2025-06-06 12:47:56,268 - __main__ - INFO - [OK] Data store initialized
2025-06-06 12:47:56,268 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:47:56,268 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 12:47:56,268 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:47:56,268 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:47:56,268 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 12:47:56,268 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:47:56,268 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:47:56,268 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:47:56,268 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:47:56,268 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:47:56,268 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:47:56,268 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 12:47:56,268 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 12:47:56,268 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 12:47:56,268 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 12:47:56,269 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:47:56,269 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 12:47:56,269 - models.llm_integration - INFO - LLM Integration initialized (enabled: True, auto-detect: True)
2025-06-06 12:47:56,269 - models.llm_integration - INFO - LLM background processing started
2025-06-06 12:47:56,269 - __main__ - INFO - [OK] LLM integration initialized and started
2025-06-06 12:47:56,269 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:47:56,269 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 12:47:56,269 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 12:47:56,270 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:47:56,540 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 12:47:56,541 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 12:47:56,541 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 12:47:56,541 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:47:57,996 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 12:47:57,996 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 12:47:57,996 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 12:47:57,996 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 12:47:57,997 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 12:47:57,997 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 12:47:57,997 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 12:47:57,998 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 12:47:57,998 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 12:47:57,998 - __main__ - INFO - ============================================================
2025-06-06 12:47:57,998 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 12:47:57,998 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 12:47:57,998 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 12:47:57,998 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 12:47:57,998 - __main__ - INFO - ============================================================
2025-06-06 12:47:58,286 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:47:58,443 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:47:58,905 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - SHORT
2025-06-06 12:47:58,905 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - SHORT (confidence: 100.00%)
2025-06-06 12:47:59,742 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:48:00,956 - models.llm_integration - WARNING - [LLM] Server health check: UNAVAILABLE (status: 404)
2025-06-06 12:49:22,830 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 12:49:22,830 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:49:22 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:49:36,325 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:49:36 -0600] "GET /api/settings/get HTTP/1.1" 200 333 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:49:42,343 - ui.ai_strategy_tuner - INFO - Updated AI model settings for BTC-USDT: weights={'rsi': 0.25, 'vwap': 0.25, 'orderflow': 0.25, 'volatility': 0.25}, confidence=0.7, risk=1
2025-06-06 12:49:42,344 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:49:42 -0600] "POST /api/settings/update HTTP/1.1" 200 496 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:50:02,469 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:50:02,469 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 78.99%)
2025-06-06 12:51:57,691 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:51:57 -0600] "GET /api/settings/get HTTP/1.1" 200 333 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:51:58,616 - aiohttp.access - INFO - ::1 [06/Jun/2025:11:51:58 -0600] "GET / HTTP/1.1" 200 55375 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 12:51:58,831 - ui.ai_strategy_tuner - INFO - WebSocket client connected (4 total)
2025-06-06 12:52:56,304 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 12:53:13,335 - models.llm_integration - WARNING - [LLM] Server health check: UNAVAILABLE (status: 404)
2025-06-06 12:55:17,501 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 12:55:17,502 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 12:55:17,507 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 12:55:17,507 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 12:55:17,509 - storage.live_store - INFO - State loaded from cache file
2025-06-06 12:55:17,510 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 12:55:17,510 - storage.live_store - INFO - Background tasks started
2025-06-06 12:55:17,510 - __main__ - INFO - [OK] Data store initialized
2025-06-06 12:55:17,510 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 12:55:17,510 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 12:55:17,510 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 12:55:17,510 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 12:55:17,510 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 12:55:17,510 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 12:55:17,510 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 12:55:17,510 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 12:55:17,510 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 12:55:17,510 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 12:55:17,511 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 12:55:17,511 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 12:55:17,511 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 12:55:17,511 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 12:55:17,511 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 12:55:17,511 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 12:55:17,511 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 12:55:17,511 - models.llm_integration - INFO - LLM Integration initialized (enabled: True, auto-detect: True)
2025-06-06 12:55:17,512 - models.llm_integration - INFO - LLM background processing started
2025-06-06 12:55:17,512 - __main__ - INFO - [OK] LLM integration initialized and started
2025-06-06 12:55:17,512 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 12:55:17,512 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 12:55:17,513 - utils.system_monitor - INFO - System Monitor initialized
2025-06-06 12:55:17,514 - __main__ - INFO - [OK] System monitor initialized
2025-06-06 12:55:17,514 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 12:55:17,514 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 12:55:17,544 - utils.system_monitor - INFO - Starting system monitoring (interval: 60s)
2025-06-06 12:55:18,032 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 12:55:18,033 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 12:55:18,033 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 12:55:18,033 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 12:55:19,473 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 12:55:19,473 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 12:55:19,473 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 12:55:19,474 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 12:55:19,474 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 12:55:19,475 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 12:55:19,475 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 12:55:19,475 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 12:55:19,475 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 12:55:19,475 - __main__ - INFO - ============================================================
2025-06-06 12:55:19,475 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 12:55:19,475 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 12:55:19,475 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 12:55:19,475 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 12:55:19,476 - __main__ - INFO - ============================================================
2025-06-06 12:55:19,514 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 12:55:19,530 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 12:55:19,545 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 12:55:19,779 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - SHORT
2025-06-06 12:55:19,779 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - SHORT (confidence: 100.00%)
2025-06-06 12:55:20,975 - ui.ai_strategy_tuner - INFO - WebSocket client connected (4 total)
2025-06-06 12:55:22,229 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 12:55:25,708 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 12:55:25,709 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 100.00%)
2025-06-06 12:56:17,654 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 83.5%
2025-06-06 12:56:17,665 - utils.system_monitor - INFO - Garbage collection freed 116 objects
2025-06-06 12:56:17,791 - utils.system_monitor - WARNING - Memory usage high: 83.5%
2025-06-06 12:56:21,120 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:56:21,120 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:56:24,615 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 12:56:24,615 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8250.00%)
2025-06-06 12:57:17,913 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.8%
2025-06-06 12:57:17,918 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 12:57:18,033 - utils.system_monitor - WARNING - Memory usage high: 81.9%
2025-06-06 12:57:36,234 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 12:57:36,234 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 85.00%)
2025-06-06 12:57:53,334 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:57:53,335 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 78.72%)
2025-06-06 12:58:18,177 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 87.6%
2025-06-06 12:58:18,186 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 12:58:18,297 - utils.system_monitor - WARNING - Memory usage high: 87.6%
2025-06-06 12:58:24,674 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 12:58:24,674 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8250.00%)
2025-06-06 12:58:27,014 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 12:58:27,014 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 12:59:13,359 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 12:59:13,359 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 85.00%)
2025-06-06 12:59:18,430 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 84.1%
2025-06-06 12:59:18,437 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 12:59:18,554 - utils.system_monitor - WARNING - Memory usage high: 84.1%
2025-06-06 13:00:02,095 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:00:02,095 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:00:06,284 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:00:06,284 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:00:17,536 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 13:00:18,683 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 82.2%
2025-06-06 13:00:18,689 - utils.system_monitor - INFO - Garbage collection freed 159 objects
2025-06-06 13:00:18,809 - utils.system_monitor - WARNING - Memory usage high: 82.3%
2025-06-06 13:00:49,509 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 13:00:53,237 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:00:53,238 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:01:05,965 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:01:05,966 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 73.61%)
2025-06-06 13:01:18,918 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.1%
2025-06-06 13:01:18,924 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 13:01:19,043 - utils.system_monitor - WARNING - Memory usage high: 81.1%
2025-06-06 13:01:41,879 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:01:41,880 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 7360.00%)
2025-06-06 13:02:19,154 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 83.2%
2025-06-06 13:02:19,159 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:02:19,278 - utils.system_monitor - WARNING - Memory usage high: 82.2%
2025-06-06 13:02:30,651 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:02:30,651 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:02:56,518 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:02:56,518 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:03:19,395 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 83.9%
2025-06-06 13:03:19,401 - utils.system_monitor - INFO - Garbage collection freed 176 objects
2025-06-06 13:03:19,518 - utils.system_monitor - WARNING - Memory usage high: 83.9%
2025-06-06 13:03:19,519 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:03:19,519 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 85.00%)
2025-06-06 13:03:28,559 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:03:28,559 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:04:08,183 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:04:08,183 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:04:19,631 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 85.3%
2025-06-06 13:04:19,637 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:04:19,755 - utils.system_monitor - WARNING - Memory usage high: 85.3%
2025-06-06 13:04:56,730 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:04:56,731 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8250.00%)
2025-06-06 13:05:17,542 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 13:05:19,885 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 10.8%, Memory: 80.0%, Signals/min: 0.8
2025-06-06 13:05:19,891 - utils.system_monitor - INFO - Garbage collection freed 159 objects
2025-06-06 13:05:20,010 - utils.system_monitor - WARNING - Memory usage high: 79.9%
2025-06-06 13:05:45,287 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:05:45,287 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8250.00%)
2025-06-06 13:05:55,679 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:05:55,680 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:05:58,021 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 13:06:20,129 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 82.6%
2025-06-06 13:06:20,135 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 13:06:20,251 - utils.system_monitor - WARNING - Memory usage high: 82.5%
2025-06-06 13:06:34,289 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:06:34,289 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:06:42,551 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 13:06:42,551 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:06:42 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:07:08,500 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:07:08,500 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:07:20,387 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.6%
2025-06-06 13:07:20,394 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:07:20,508 - utils.system_monitor - WARNING - Memory usage high: 81.6%
2025-06-06 13:07:22,785 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:07:22,786 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 85.00%)
2025-06-06 13:07:39,433 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:07:39,433 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 77.00%)
2025-06-06 13:08:11,319 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:08:11,319 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8250.00%)
2025-06-06 13:08:20,625 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.3%
2025-06-06 13:08:20,632 - utils.system_monitor - INFO - Garbage collection freed 252 objects
2025-06-06 13:08:20,750 - utils.system_monitor - WARNING - Memory usage high: 81.2%
2025-06-06 13:08:56,119 - __main__ - INFO - [SIGNAL] Received signal 2
2025-06-06 13:08:56,330 - __main__ - INFO - [STOP] Stopping Epinnox V6 AI Strategy Tuner...
2025-06-06 13:08:56,331 - feeds.binance_ws_client - INFO - 🔌 Disconnecting from Binance WebSocket
2025-06-06 13:08:59,878 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:08:59,878 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8250.00%)
2025-06-06 13:09:05,685 - storage.live_store - INFO - Live Data Store shutdown complete
2025-06-06 13:09:05,685 - __main__ - INFO - [OK] Application stopped gracefully
2025-06-06 13:09:05,685 - __main__ - INFO - [COMPLETE] Epinnox V6 AI Strategy Tuner completed successfully
2025-06-06 13:09:05,686 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (3 total)
2025-06-06 13:09:05,686 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (2 total)
2025-06-06 13:09:05,686 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 13:09:05,686 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (0 total)
2025-06-06 13:12:20,402 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 13:12:20,403 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 13:12:20,409 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 13:12:20,410 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 13:12:20,410 - storage.live_store - INFO - State loaded from cache file
2025-06-06 13:12:20,410 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 13:12:20,410 - storage.live_store - INFO - Background tasks started
2025-06-06 13:12:20,410 - __main__ - INFO - [OK] Data store initialized
2025-06-06 13:12:20,410 - analytics.signal_tracker - INFO - Signal Tracker initialized (TP: 2.0%, SL: 1.0%)
2025-06-06 13:12:20,411 - analytics.performance_analyzer - INFO - Performance Analyzer initialized
2025-06-06 13:12:20,411 - __main__ - INFO - [OK] Signal tracking and performance analytics initialized
2025-06-06 13:12:20,411 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 13:12:20,411 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 13:12:20,411 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 13:12:20,411 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 13:12:20,411 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 13:12:20,411 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 13:12:20,411 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 13:12:20,411 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 13:12:20,411 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 13:12:20,411 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 13:12:20,411 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 13:12:20,411 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 13:12:20,411 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 13:12:20,411 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 13:12:20,411 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 13:12:20,412 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 13:12:20,412 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 13:12:20,412 - models.llm_integration - INFO - LLM Integration initialized (enabled: True, auto-detect: True)
2025-06-06 13:12:20,412 - models.llm_integration - INFO - LLM background processing started
2025-06-06 13:12:20,412 - __main__ - INFO - [OK] LLM integration initialized and started
2025-06-06 13:12:20,412 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 13:12:20,412 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 13:12:20,412 - utils.system_monitor - INFO - System Monitor initialized
2025-06-06 13:12:20,412 - __main__ - INFO - [OK] System monitor initialized
2025-06-06 13:12:20,412 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 13:12:20,412 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 13:12:20,435 - utils.system_monitor - INFO - Starting system monitoring (interval: 60s)
2025-06-06 13:12:20,686 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 13:12:20,687 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 13:12:20,687 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 13:12:20,687 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 13:12:22,135 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 13:12:22,135 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 13:12:22,135 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 13:12:22,136 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 13:12:22,136 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 13:12:22,137 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 13:12:22,137 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 13:12:22,137 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 13:12:22,137 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 13:12:22,137 - __main__ - INFO - ============================================================
2025-06-06 13:12:22,137 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 13:12:22,137 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 13:12:22,137 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 13:12:22,137 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 13:12:22,137 - __main__ - INFO - ============================================================
2025-06-06 13:12:22,308 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 13:12:22,339 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 13:12:22,420 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:23,785 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:24,127 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 13:12:24,901 - ui.ai_strategy_tuner - INFO - WebSocket client connected (4 total)
2025-06-06 13:12:25,055 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:26,154 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:27,664 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:29,882 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:31,002 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:32,442 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:33,494 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:34,616 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:36,003 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:37,501 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:38,625 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:39,961 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:41,624 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:42,909 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:43,945 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:45,117 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:46,162 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:47,847 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:49,010 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:49,040 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:12:49 -0600] "GET / HTTP/1.1" 200 59882 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:12:50,617 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:50,872 - ui.ai_strategy_tuner - INFO - WebSocket client connected (5 total)
2025-06-06 13:12:50,928 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:12:50 -0600] "GET /api/data HTTP/1.1" 200 1308 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:12:51,889 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:53,307 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:54,500 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:56,676 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:58,328 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:12:59,416 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:00,502 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:01,624 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:02,682 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:03,692 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:05,176 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:06,841 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:07,881 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 13:13:08,802 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:11,952 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:13,260 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:14,617 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:16,095 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:17,444 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:18,491 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:19,803 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:20,573 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.7%, High error rate: 100.0%
2025-06-06 13:13:20,582 - utils.system_monitor - INFO - Garbage collection freed 131 objects
2025-06-06 13:13:20,697 - utils.system_monitor - WARNING - Memory usage high: 81.7%
2025-06-06 13:13:21,461 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:22,587 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:23,452 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:13:23 -0600] "GET / HTTP/1.1" 200 59882 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:13:23,459 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (4 total)
2025-06-06 13:13:23,460 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:12:50 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:13:23,526 - ui.ai_strategy_tuner - INFO - WebSocket client connected (5 total)
2025-06-06 13:13:23,533 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:13:23 -0600] "GET /api/data HTTP/1.1" 200 1298 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:13:23,948 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:24,909 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:13:24 -0600] "GET / HTTP/1.1" 200 59882 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:13:24,913 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (4 total)
2025-06-06 13:13:24,914 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:13:23 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:13:24,992 - ui.ai_strategy_tuner - INFO - WebSocket client connected (5 total)
2025-06-06 13:13:24,993 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:13:24 -0600] "GET /api/data HTTP/1.1" 200 1315 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:13:25,721 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:27,402 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:28,411 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:28,774 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 13:13:28,774 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:13:28 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:13:30,055 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:31,527 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:34,435 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:35,522 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:36,561 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:37,669 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:38,686 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:39,955 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:40,961 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:42,022 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:43,207 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:45,767 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:46,789 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:48,657 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:50,440 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:51,646 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:52,928 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:54,610 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:55,893 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:57,473 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:13:59,033 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:00,621 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:01,714 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:02,795 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:05,700 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:06,747 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:07,809 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:09,604 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:10,763 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:12,873 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:13,909 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:15,446 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:16,801 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:18,335 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:19,769 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:20,820 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.1%, High error rate: 100.0%
2025-06-06 13:14:20,942 - utils.system_monitor - WARNING - Memory usage high: 82.2%
2025-06-06 13:14:20,943 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:22,020 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:23,264 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:24,313 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:25,396 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:26,408 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:27,578 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:28,627 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:30,950 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:32,132 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:33,570 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:35,014 - __main__ - ERROR - [ERROR] Error handling trade data: argument of type 'MarketFeatures' is not iterable
2025-06-06 13:14:45,445 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 13:14:45,445 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 13:14:45,451 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 13:14:45,451 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 13:14:45,451 - storage.live_store - INFO - State loaded from cache file
2025-06-06 13:14:45,451 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 13:14:45,451 - storage.live_store - INFO - Background tasks started
2025-06-06 13:14:45,451 - __main__ - INFO - [OK] Data store initialized
2025-06-06 13:14:45,452 - analytics.signal_tracker - INFO - Signal Tracker initialized (TP: 2.0%, SL: 1.0%)
2025-06-06 13:14:45,452 - analytics.performance_analyzer - INFO - Performance Analyzer initialized
2025-06-06 13:14:45,452 - __main__ - INFO - [OK] Signal tracking and performance analytics initialized
2025-06-06 13:14:45,452 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 13:14:45,452 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 13:14:45,452 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 13:14:45,452 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 13:14:45,452 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 13:14:45,452 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 13:14:45,452 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 13:14:45,452 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 13:14:45,452 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 13:14:45,452 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 13:14:45,452 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 13:14:45,452 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 13:14:45,452 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 13:14:45,452 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 13:14:45,453 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 13:14:45,453 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 13:14:45,453 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 13:14:45,453 - models.llm_integration - INFO - LLM Integration initialized (enabled: True, auto-detect: True)
2025-06-06 13:14:45,453 - models.llm_integration - INFO - LLM background processing started
2025-06-06 13:14:45,453 - __main__ - INFO - [OK] LLM integration initialized and started
2025-06-06 13:14:45,453 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 13:14:45,453 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 13:14:45,453 - utils.system_monitor - INFO - System Monitor initialized
2025-06-06 13:14:45,453 - __main__ - INFO - [OK] System monitor initialized
2025-06-06 13:14:45,453 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 13:14:45,453 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 13:14:45,475 - utils.system_monitor - INFO - Starting system monitoring (interval: 60s)
2025-06-06 13:14:45,717 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 13:14:45,718 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 13:14:45,718 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 13:14:45,718 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 13:14:47,162 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 13:14:47,163 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 13:14:47,163 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 13:14:47,163 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 13:14:47,164 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 13:14:47,164 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 13:14:47,166 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 13:14:47,166 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 13:14:47,166 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 13:14:47,166 - __main__ - INFO - ============================================================
2025-06-06 13:14:47,166 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 13:14:47,166 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 13:14:47,166 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 13:14:47,166 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 13:14:47,166 - __main__ - INFO - ============================================================
2025-06-06 13:14:47,355 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 13:14:47,370 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 13:14:47,402 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 13:14:47,961 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - SHORT
2025-06-06 13:14:47,961 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - SHORT (confidence: 100.00%)
2025-06-06 13:14:48,446 - ui.ai_strategy_tuner - INFO - WebSocket client connected (4 total)
2025-06-06 13:14:50,258 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 13:14:50,438 - ui.ai_strategy_tuner - INFO - WebSocket client connected (5 total)
2025-06-06 13:14:53,776 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:14:53,776 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 100.00%)
2025-06-06 13:15:15,948 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (4 total)
2025-06-06 13:15:15,949 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:14:47 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:15:16,626 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (3 total)
2025-06-06 13:15:16,627 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:14:48 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:15:19,293 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:15:19 -0600] "GET / HTTP/1.1" 200 59882 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:15:19,457 - ui.ai_strategy_tuner - INFO - WebSocket client connected (4 total)
2025-06-06 13:15:19,510 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:15:19 -0600] "GET /api/data HTTP/1.1" 200 2997 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:15:45,603 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 10.2%, Memory: 78.8%, Signals/min: 1.0
2025-06-06 13:15:45,610 - utils.system_monitor - INFO - Garbage collection freed 114 objects
2025-06-06 13:15:45,728 - utils.system_monitor - WARNING - Memory usage high: 78.8%
2025-06-06 13:16:19,120 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:16:19,121 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 95.00%)
2025-06-06 13:16:45,859 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 10.7%, Memory: 79.7%, Signals/min: 0.5
2025-06-06 13:16:45,866 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:16:45,974 - utils.system_monitor - WARNING - Memory usage high: 80.0%
2025-06-06 13:17:05,832 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (3 total)
2025-06-06 13:17:05,832 - aiohttp.access - INFO - 127.0.0.1 [06/Jun/2025:12:14:47 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:17:05,838 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (2 total)
2025-06-06 13:17:05,839 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:14:50 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:17:07,566 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:17:07,567 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 95.00%)
2025-06-06 13:17:21,217 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 13:17:21,218 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 13:17:21,224 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 13:17:21,224 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 13:17:21,224 - storage.live_store - INFO - State loaded from cache file
2025-06-06 13:17:21,224 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 13:17:21,225 - storage.live_store - INFO - Background tasks started
2025-06-06 13:17:21,225 - __main__ - INFO - [OK] Data store initialized
2025-06-06 13:17:21,225 - analytics.signal_tracker - INFO - Signal Tracker initialized (TP: 2.0%, SL: 1.0%)
2025-06-06 13:17:21,225 - analytics.performance_analyzer - INFO - Performance Analyzer initialized
2025-06-06 13:17:21,225 - __main__ - INFO - [OK] Signal tracking and performance analytics initialized
2025-06-06 13:17:21,225 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 13:17:21,225 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 13:17:21,225 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 13:17:21,225 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 13:17:21,226 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 13:17:21,226 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 13:17:21,226 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 13:17:21,226 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 13:17:21,226 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 13:17:21,226 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 13:17:21,226 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 13:17:21,227 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 13:17:21,227 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 13:17:21,227 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 13:17:21,227 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 13:17:21,227 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 13:17:21,227 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 13:17:21,227 - models.llm_integration - INFO - LLM Integration initialized (enabled: True, auto-detect: True)
2025-06-06 13:17:21,227 - models.llm_integration - INFO - LLM background processing started
2025-06-06 13:17:21,228 - __main__ - INFO - [OK] LLM integration initialized and started
2025-06-06 13:17:21,228 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 13:17:21,228 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 13:17:21,228 - utils.system_monitor - INFO - System Monitor initialized
2025-06-06 13:17:21,228 - __main__ - INFO - [OK] System monitor initialized
2025-06-06 13:17:21,228 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 13:17:21,228 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 13:17:21,255 - utils.system_monitor - INFO - Starting system monitoring (interval: 60s)
2025-06-06 13:17:21,506 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 13:17:21,507 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 13:17:21,507 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 13:17:21,507 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 13:17:23,079 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 13:17:23,080 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 13:17:23,080 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 13:17:23,080 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 13:17:23,080 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 13:17:23,081 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 13:17:23,081 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 13:17:23,081 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 13:17:23,081 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 13:17:23,081 - __main__ - INFO - ============================================================
2025-06-06 13:17:23,081 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 13:17:23,081 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 13:17:23,081 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 13:17:23,081 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 13:17:23,081 - __main__ - INFO - ============================================================
2025-06-06 13:17:23,189 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - SHORT
2025-06-06 13:17:23,189 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - SHORT (confidence: 100.00%)
2025-06-06 13:17:23,255 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 13:17:24,436 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 13:17:25,599 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 13:17:29,019 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:17:29,020 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 100.00%)
2025-06-06 13:17:49,089 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:17:49 -0600] "GET / HTTP/1.1" 200 59988 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:17:49,396 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 13:17:49,418 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:17:49 -0600] "GET /api/data HTTP/1.1" 200 2988 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:18:21,392 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 82.7%
2025-06-06 13:18:21,399 - utils.system_monitor - INFO - Garbage collection freed 114 objects
2025-06-06 13:18:21,520 - utils.system_monitor - WARNING - Memory usage high: 82.7%
2025-06-06 13:18:39,777 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:18:39 -0600] "GET / HTTP/1.1" 200 59988 "-" "python-requests/2.32.3"
2025-06-06 13:18:39,782 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:18:39 -0600] "GET /api/data HTTP/1.1" 200 4812 "-" "python-requests/2.32.3"
2025-06-06 13:18:39,815 - ui.ai_strategy_tuner - INFO - WebSocket client connected (4 total)
2025-06-06 13:18:45,583 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (3 total)
2025-06-06 13:18:45,583 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:18:39 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Python/3.9 websockets/12.0"
2025-06-06 13:18:54,834 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:18:54,834 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 95.00%)
2025-06-06 13:19:02,494 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (2 total)
2025-06-06 13:19:02,494 - aiohttp.access - INFO - 127.0.0.1 [06/Jun/2025:12:17:23 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:19:03,181 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 13:19:03,182 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:17:24 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:19:21,658 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 83.2%
2025-06-06 13:19:21,664 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:19:21,775 - utils.system_monitor - WARNING - Memory usage high: 83.3%
2025-06-06 13:19:29,295 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:19:29,295 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 78.97%)
2025-06-06 13:19:43,488 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:19:43,489 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 85.00%)
2025-06-06 13:20:32,860 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 13:20:32,861 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 13:20:32,867 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 13:20:32,867 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 13:20:32,867 - storage.live_store - INFO - State loaded from cache file
2025-06-06 13:20:32,867 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 13:20:32,868 - storage.live_store - INFO - Background tasks started
2025-06-06 13:20:32,868 - __main__ - INFO - [OK] Data store initialized
2025-06-06 13:20:32,868 - analytics.signal_tracker - INFO - Signal Tracker initialized (TP: 2.0%, SL: 1.0%)
2025-06-06 13:20:32,868 - analytics.performance_analyzer - INFO - Performance Analyzer initialized
2025-06-06 13:20:32,868 - __main__ - INFO - [OK] Signal tracking and performance analytics initialized
2025-06-06 13:20:32,868 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 13:20:32,868 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 13:20:32,868 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 13:20:32,868 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 13:20:32,868 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 13:20:32,868 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 13:20:32,868 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 13:20:32,868 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 13:20:32,868 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 13:20:32,868 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 13:20:32,869 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 13:20:32,869 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 13:20:32,869 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 13:20:32,869 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 13:20:32,869 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 13:20:32,869 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 13:20:32,869 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 13:20:32,869 - models.llm_integration - INFO - LLM Integration initialized (enabled: True, auto-detect: True)
2025-06-06 13:20:32,869 - models.llm_integration - INFO - LLM background processing started
2025-06-06 13:20:32,869 - __main__ - INFO - [OK] LLM integration initialized and started
2025-06-06 13:20:32,869 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 13:20:32,870 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 13:20:32,870 - utils.system_monitor - INFO - System Monitor initialized
2025-06-06 13:20:32,870 - __main__ - INFO - [OK] System monitor initialized
2025-06-06 13:20:32,870 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 13:20:32,870 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 13:20:32,896 - utils.system_monitor - INFO - Starting system monitoring (interval: 60s)
2025-06-06 13:20:33,140 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 13:20:33,140 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 13:20:33,140 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 13:20:33,140 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 13:20:34,689 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 13:20:34,690 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 13:20:34,690 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 13:20:34,690 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 13:20:34,690 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 13:20:34,691 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 13:20:34,691 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 13:20:34,691 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 13:20:34,691 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 13:20:34,691 - __main__ - INFO - ============================================================
2025-06-06 13:20:34,691 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 13:20:34,691 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 13:20:34,691 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 13:20:34,692 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 13:20:34,692 - __main__ - INFO - ============================================================
2025-06-06 13:20:34,764 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 13:20:35,534 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:20:35,534 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 100.00%)
2025-06-06 13:20:37,962 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 13:20:41,557 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:20:41,557 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 100.00%)
2025-06-06 13:21:02,176 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:21:02 -0600] "GET / HTTP/1.1" 200 61296 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:21:02,392 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:21:02 -0600] "GET /api/data HTTP/1.1" 200 3172 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:21:02,394 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 13:21:10,536 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:21:10 -0600] "GET / HTTP/1.1" 200 61296 "-" "python-requests/2.32.3"
2025-06-06 13:21:10,539 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:21:10 -0600] "GET /api/data HTTP/1.1" 200 3887 "-" "python-requests/2.32.3"
2025-06-06 13:21:10,567 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 13:21:14,820 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (2 total)
2025-06-06 13:21:14,820 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:21:10 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Python/3.9 websockets/12.0"
2025-06-06 13:21:33,000 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.7%
2025-06-06 13:21:33,007 - utils.system_monitor - INFO - Garbage collection freed 113 objects
2025-06-06 13:21:33,124 - utils.system_monitor - WARNING - Memory usage high: 81.7%
2025-06-06 13:22:06,748 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:22:06,748 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:22:26,536 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:22:26 -0600] "GET /api/settings/get HTTP/1.1" 200 333 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:22:27,718 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:22:27 -0600] "GET /api/settings/get HTTP/1.1" 200 333 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:22:33,255 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 5.0%, Memory: 79.8%, Signals/min: 0.5
2025-06-06 13:22:33,379 - utils.system_monitor - WARNING - Memory usage high: 79.8%
2025-06-06 13:22:36,330 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:22:36 -0600] "GET /api/settings/get HTTP/1.1" 200 333 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:22:42,185 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:22:42,185 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:22:55,144 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:22:55,144 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8250.00%)
2025-06-06 13:22:59,411 - ui.ai_strategy_tuner - INFO - Updated AI model settings for BTC-USDT: weights={'rsi': 0.25, 'vwap': 0.25, 'orderflow': 0.25, 'volatility': 0.25}, confidence=0.7, risk=1
2025-06-06 13:22:59,413 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:22:59 -0600] "POST /api/settings/update HTTP/1.1" 200 496 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 13:23:12,724 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:23:12,725 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 77.96%)
2025-06-06 13:23:33,506 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 7.3%, Memory: 79.9%, Signals/min: 1.0
2025-06-06 13:23:33,512 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:23:33,628 - utils.system_monitor - WARNING - Memory usage high: 80.0%
2025-06-06 13:23:43,801 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:23:43,802 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8500.00%)
2025-06-06 13:24:32,434 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:24:32,434 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 85.00%)
2025-06-06 13:24:33,757 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 10.0%, Memory: 75.5%, Signals/min: 0.7
2025-06-06 13:24:33,764 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 13:24:33,886 - utils.system_monitor - WARNING - Memory usage high: 75.5%
2025-06-06 13:25:21,145 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:25:21,145 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 95.00%)
2025-06-06 13:25:32,906 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 13:25:34,003 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 11.3%, Memory: 74.1%, Signals/min: 0.6
2025-06-06 13:25:34,010 - utils.system_monitor - INFO - Garbage collection freed 159 objects
2025-06-06 13:25:34,128 - utils.system_monitor - WARNING - Memory usage high: 74.1%
2025-06-06 13:26:07,257 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:26:07,257 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:26:08,451 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 13:26:10,453 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:26:10,453 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8560.00%)
2025-06-06 13:26:11,458 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:26:11,458 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8560.00%)
2025-06-06 13:26:34,259 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 8.7%, Memory: 74.9%, Signals/min: 0.7
2025-06-06 13:26:34,387 - utils.system_monitor - WARNING - Memory usage high: 74.9%
2025-06-06 13:27:00,154 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:27:00,154 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:27:34,515 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.7%
2025-06-06 13:27:34,521 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:27:34,638 - utils.system_monitor - WARNING - Memory usage high: 81.7%
2025-06-06 13:27:49,131 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:27:49,131 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:28:34,778 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.6%
2025-06-06 13:28:34,784 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:28:34,899 - utils.system_monitor - WARNING - Memory usage high: 81.6%
2025-06-06 13:28:37,886 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:28:37,886 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 85.00%)
2025-06-06 13:28:41,444 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:28:41,444 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:29:26,654 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:29:26,654 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8540.00%)
2025-06-06 13:29:35,028 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 18.8%, Memory: 79.8%, Signals/min: 0.6
2025-06-06 13:29:35,034 - utils.system_monitor - INFO - Garbage collection freed 252 objects
2025-06-06 13:29:35,153 - utils.system_monitor - WARNING - Memory usage high: 79.9%
2025-06-06 13:30:15,298 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 13:30:15,298 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 65.00%)
2025-06-06 13:30:32,922 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 13:30:35,273 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 7.2%, Memory: 77.3%, Signals/min: 0.5
2025-06-06 13:30:35,279 - utils.system_monitor - INFO - Garbage collection freed 159 objects
2025-06-06 13:30:35,396 - utils.system_monitor - WARNING - Memory usage high: 77.3%
2025-06-06 13:30:39,523 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:30:39,523 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 77.85%)
2025-06-06 13:31:04,079 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:31:04,079 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 85.00%)
2025-06-06 13:31:35,527 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 4.0%, Memory: 79.6%, Signals/min: 0.5
2025-06-06 13:31:35,532 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:31:35,651 - utils.system_monitor - WARNING - Memory usage high: 79.6%
2025-06-06 13:31:51,541 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 13:31:55,302 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 13:31:55,302 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 65.00%)
2025-06-06 13:32:35,736 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 9.3%, Memory: 77.7%, Signals/min: 0.5
2025-06-06 13:32:35,742 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 13:32:35,860 - utils.system_monitor - WARNING - Memory usage high: 77.7%
2025-06-06 13:32:44,124 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:32:44,124 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 78.00%)
2025-06-06 13:33:01,913 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:33:01,913 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 78.63%)
2025-06-06 13:33:32,854 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:33:32,854 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 85.00%)
2025-06-06 13:33:34,505 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 13:33:34,506 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 13:33:36,004 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.7%
2025-06-06 13:33:36,011 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 13:33:36,123 - utils.system_monitor - WARNING - Memory usage high: 81.7%
2025-06-06 13:33:39,520 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 13:33:42,619 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 13:33:42,620 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 13:34:21,722 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 13:34:21,722 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 65.00%)
2025-06-06 13:34:36,255 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.2%
2025-06-06 13:34:36,260 - utils.system_monitor - INFO - Garbage collection freed 103 objects
2025-06-06 13:34:36,380 - utils.system_monitor - WARNING - Memory usage high: 80.2%
2025-06-06 13:34:40,128 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:34:40,129 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:35:10,131 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:35:10,131 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8540.00%)
2025-06-06 13:35:13,290 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:35:13,290 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 65.62%)
2025-06-06 13:35:32,942 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 13:35:36,515 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.8%
2025-06-06 13:35:36,521 - utils.system_monitor - INFO - Garbage collection freed 159 objects
2025-06-06 13:35:36,640 - utils.system_monitor - WARNING - Memory usage high: 80.8%
2025-06-06 13:35:58,789 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 13:35:58,790 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 75.00%)
2025-06-06 13:36:36,779 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.9%
2025-06-06 13:36:36,786 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:36:36,904 - utils.system_monitor - WARNING - Memory usage high: 80.8%
2025-06-06 13:36:47,511 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:36:47,511 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:37:34,477 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:37:34,477 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:37:34,836 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 13:37:37,044 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 38.2%, Memory: 78.8%, Signals/min: 0.6
2025-06-06 13:37:37,050 - utils.system_monitor - INFO - Garbage collection freed 95 objects
2025-06-06 13:37:37,166 - utils.system_monitor - WARNING - Memory usage high: 78.8%
2025-06-06 13:37:37,945 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:37:37,945 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 85.00%)
2025-06-06 13:37:39,230 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:37:39,230 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 85.00%)
2025-06-06 13:38:27,877 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:38:27,877 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:38:37,296 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 3.8%, Memory: 78.6%, Signals/min: 0.6
2025-06-06 13:38:37,302 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:38:37,420 - utils.system_monitor - WARNING - Memory usage high: 78.6%
2025-06-06 13:38:43,594 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:38:43,594 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:39:16,282 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:39:16,282 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8250.00%)
2025-06-06 13:39:37,541 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 3.0%, Memory: 78.9%, Signals/min: 0.6
2025-06-06 13:39:37,546 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:39:37,665 - utils.system_monitor - WARNING - Memory usage high: 78.9%
2025-06-06 13:40:04,834 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:40:04,834 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:40:22,539 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:40:22,539 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:40:32,946 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 13:40:37,778 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 8.6%, Memory: 78.0%, Signals/min: 0.6
2025-06-06 13:40:37,786 - utils.system_monitor - INFO - Garbage collection freed 159 objects
2025-06-06 13:40:37,897 - utils.system_monitor - WARNING - Memory usage high: 78.1%
2025-06-06 13:40:53,281 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:40:53,281 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8540.00%)
2025-06-06 13:41:07,662 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:41:07,662 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:41:38,013 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 12.3%, Memory: 80.0%, Signals/min: 0.6
2025-06-06 13:41:38,019 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:41:38,136 - utils.system_monitor - WARNING - Memory usage high: 80.9%
2025-06-06 13:41:41,865 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:41:41,865 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:42:30,343 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:42:30,344 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8250.00%)
2025-06-06 13:42:38,269 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 2.2%, Memory: 77.7%, Signals/min: 0.6
2025-06-06 13:42:38,276 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 13:42:38,394 - utils.system_monitor - WARNING - Memory usage high: 77.7%
2025-06-06 13:43:17,727 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 13:43:21,422 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:43:21,423 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:43:38,518 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 6.4%, Memory: 79.4%, Signals/min: 0.6
2025-06-06 13:43:38,524 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 13:43:38,647 - utils.system_monitor - WARNING - Memory usage high: 79.1%
2025-06-06 13:44:10,075 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:44:10,075 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:44:38,749 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.4%
2025-06-06 13:44:38,756 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:44:38,879 - utils.system_monitor - WARNING - Memory usage high: 80.3%
2025-06-06 13:44:58,914 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:44:58,914 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85.00%)
2025-06-06 13:45:32,949 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 13:45:39,004 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 7.2%, Memory: 76.9%, Signals/min: 0.5
2025-06-06 13:45:39,012 - utils.system_monitor - INFO - Garbage collection freed 159 objects
2025-06-06 13:45:39,133 - utils.system_monitor - WARNING - Memory usage high: 77.0%
2025-06-06 13:45:47,460 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:45:47,461 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 85.00%)
2025-06-06 13:46:35,960 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:46:35,960 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 8540.00%)
2025-06-06 13:46:39,248 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.4%
2025-06-06 13:46:39,254 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 13:46:39,372 - utils.system_monitor - WARNING - Memory usage high: 78.4%
2025-06-06 13:47:09,958 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:47:09,959 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:47:24,588 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 13:47:24,589 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 85.00%)
2025-06-06 13:47:39,506 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 82.1%
2025-06-06 13:47:39,514 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:47:39,633 - utils.system_monitor - WARNING - Memory usage high: 81.9%
2025-06-06 13:48:10,389 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 13:48:10,389 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 13:48:10,394 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 13:48:10,394 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 13:48:10,394 - storage.live_store - INFO - State loaded from cache file
2025-06-06 13:48:10,394 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 13:48:10,394 - storage.live_store - INFO - Background tasks started
2025-06-06 13:48:10,394 - __main__ - INFO - [OK] Data store initialized
2025-06-06 13:48:10,394 - analytics.signal_tracker - INFO - Signal Tracker initialized (TP: 2.0%, SL: 1.0%)
2025-06-06 13:48:10,394 - analytics.performance_analyzer - INFO - Performance Analyzer initialized
2025-06-06 13:48:10,394 - __main__ - INFO - [OK] Signal tracking and performance analytics initialized
2025-06-06 13:48:10,396 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 13:48:10,396 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 13:48:10,396 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 13:48:10,396 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 13:48:10,396 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 13:48:10,396 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 13:48:10,396 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 13:48:10,397 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 13:48:10,397 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 13:48:10,397 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 13:48:10,397 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 13:48:10,397 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 13:48:10,397 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 13:48:10,397 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 13:48:10,397 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 13:48:10,397 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 13:48:10,397 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 13:48:10,402 - models.llm_integration - INFO - ✅ Loaded dynamic prompt templates from YAML
2025-06-06 13:48:10,403 - models.llm_integration - INFO - LLM Integration initialized (enabled: True, auto-detect: True)
2025-06-06 13:48:10,403 - models.llm_integration - INFO - Loaded 12 prompt templates
2025-06-06 13:48:10,403 - models.llm_integration - INFO - LLM background processing started
2025-06-06 13:48:10,403 - __main__ - INFO - [OK] LLM integration initialized and started
2025-06-06 13:48:10,403 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 13:48:10,403 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 13:48:10,404 - utils.system_monitor - INFO - System Monitor initialized
2025-06-06 13:48:10,404 - __main__ - INFO - [OK] System monitor initialized
2025-06-06 13:48:10,404 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 13:48:10,404 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 13:48:10,426 - utils.system_monitor - INFO - Starting system monitoring (interval: 60s)
2025-06-06 13:48:10,585 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 13:48:10,585 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 13:48:10,585 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 13:48:10,586 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 13:48:12,216 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 13:48:12,217 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 13:48:12,217 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 13:48:12,217 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 13:48:12,218 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 13:48:12,219 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 13:48:12,219 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 13:48:12,219 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 13:48:12,219 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 13:48:12,219 - __main__ - INFO - ============================================================
2025-06-06 13:48:12,219 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 13:48:12,219 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 13:48:12,220 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 13:48:12,220 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 13:48:12,220 - __main__ - INFO - ============================================================
2025-06-06 13:48:12,323 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 13:48:13,068 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - SHORT
2025-06-06 13:48:13,068 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - SHORT (confidence: 100.00%)
2025-06-06 13:48:15,427 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 13:48:15,427 - models.llm_integration - WARNING - Error analyzing market regime: '>' not supported between instances of 'NoneType' and 'float'
2025-06-06 13:48:15,446 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 13:48:19,593 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:48:19,593 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 95%)
2025-06-06 13:49:10,548 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 5.2%, Memory: 80.0%, Signals/min: 1.0
2025-06-06 13:49:10,672 - utils.system_monitor - WARNING - Memory usage high: 80.0%
2025-06-06 13:49:34,403 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:49:34 -0600] "GET /api/status HTTP/1.1" 200 292 "-" "python-requests/2.32.3"
2025-06-06 13:49:34,407 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:49:34 -0600] "GET /api/data HTTP/1.1" 200 5159 "-" "python-requests/2.32.3"
2025-06-06 13:49:34,425 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:49:34 -0600] "GET /api/data HTTP/1.1" 200 5158 "-" "python-requests/2.32.3"
2025-06-06 13:49:34,428 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:49:34 -0600] "GET /api/data HTTP/1.1" 200 5158 "-" "python-requests/2.32.3"
2025-06-06 13:49:34,455 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:49:34 -0600] "GET /api/data HTTP/1.1" 200 5159 "-" "python-requests/2.32.3"
2025-06-06 13:49:34,470 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:49:34 -0600] "GET /api/data HTTP/1.1" 200 5159 "-" "python-requests/2.32.3"
2025-06-06 13:49:34,486 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:49:34 -0600] "GET /api/data HTTP/1.1" 200 5159 "-" "python-requests/2.32.3"
2025-06-06 13:49:34,511 - ui.ai_strategy_tuner - INFO - WebSocket client connected (3 total)
2025-06-06 13:49:44,654 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:49:44,654 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 95%)
2025-06-06 13:49:44,770 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (2 total)
2025-06-06 13:49:44,770 - aiohttp.access - INFO - ::1 [06/Jun/2025:12:49:34 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Python/3.9 websockets/12.0"
2025-06-06 13:50:10,800 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.4%
2025-06-06 13:50:10,923 - utils.system_monitor - WARNING - Memory usage high: 81.5%
2025-06-06 13:50:33,882 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 13:50:33,883 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 95%)
2025-06-06 13:51:11,061 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 4.3%, Memory: 78.8%, Signals/min: 0.3
2025-06-06 13:51:11,067 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:51:11,184 - utils.system_monitor - WARNING - Memory usage high: 78.8%
2025-06-06 13:51:23,149 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:51:23,149 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 95%)
2025-06-06 13:52:11,331 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 17.4%, Memory: 75.7%, Signals/min: 0.2
2025-06-06 13:52:11,339 - utils.system_monitor - INFO - Garbage collection freed 176 objects
2025-06-06 13:52:11,454 - utils.system_monitor - WARNING - Memory usage high: 75.7%
2025-06-06 13:52:12,536 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:52:12,536 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 92%)
2025-06-06 13:53:01,527 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 13:53:01,528 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 75%)
2025-06-06 13:53:10,424 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 13:53:11,579 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 3.1%, Memory: 74.0%, Signals/min: 0.2
2025-06-06 13:53:11,584 - utils.system_monitor - INFO - Garbage collection freed 159 objects
2025-06-06 13:53:11,703 - utils.system_monitor - WARNING - Memory usage high: 74.0%
2025-06-06 13:53:48,918 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 13:53:53,255 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:53:53,255 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 95%)
2025-06-06 13:54:11,821 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 4.9%, Memory: 72.1%, Signals/min: 0.2
2025-06-06 13:54:11,827 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 13:54:11,946 - utils.system_monitor - WARNING - Memory usage high: 72.0%
2025-06-06 13:54:42,435 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 13:54:42,436 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 92%)
2025-06-06 13:55:12,056 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 10.7%, Memory: 74.7%, Signals/min: 0.1
2025-06-06 13:55:12,062 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:55:12,181 - utils.system_monitor - WARNING - Memory usage high: 74.7%
2025-06-06 13:55:31,561 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 13:55:31,561 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 13:56:12,298 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.4%
2025-06-06 13:56:12,305 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 13:56:12,420 - utils.system_monitor - WARNING - Memory usage high: 80.4%
2025-06-06 13:56:20,736 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 13:56:20,736 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 95%)
2025-06-06 13:56:41,360 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 13:56:41,361 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 13:56:59,389 - __main__ - INFO - [SIGNAL] Received signal 2
2025-06-06 13:56:59,493 - __main__ - INFO - [STOP] Stopping Epinnox V6 AI Strategy Tuner...
2025-06-06 13:56:59,493 - feeds.binance_ws_client - INFO - 🔌 Disconnecting from Binance WebSocket
2025-06-06 13:57:03,407 - __main__ - INFO - [SIGNAL] Received signal 2
2025-06-06 13:57:04,308 - __main__ - INFO - [SIGNAL] Received signal 2
2025-06-06 13:57:05,088 - __main__ - INFO - [SIGNAL] Received signal 2
2025-06-06 13:57:07,429 - storage.live_store - INFO - Live Data Store shutdown complete
2025-06-06 13:57:07,429 - __main__ - INFO - [OK] Application stopped gracefully
2025-06-06 13:57:07,429 - __main__ - INFO - [COMPLETE] Epinnox V6 AI Strategy Tuner completed successfully
2025-06-06 13:57:07,430 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 13:57:07,430 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (0 total)
2025-06-06 14:09:45,556 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 14:09:45,556 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 14:09:45,556 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 14:09:45,556 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 14:09:45,566 - storage.live_store - INFO - State loaded from cache file
2025-06-06 14:09:45,566 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 14:09:45,566 - storage.live_store - INFO - Background tasks started
2025-06-06 14:09:45,566 - __main__ - INFO - [OK] Data store initialized
2025-06-06 14:09:45,566 - analytics.signal_tracker - INFO - Signal Tracker initialized (TP: 2.0%, SL: 1.0%)
2025-06-06 14:09:45,566 - analytics.performance_analyzer - INFO - Performance Analyzer initialized
2025-06-06 14:09:45,566 - __main__ - INFO - [OK] Signal tracking and performance analytics initialized
2025-06-06 14:09:45,566 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 14:09:45,567 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 14:09:45,567 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 14:09:45,567 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 14:09:45,567 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 14:09:45,567 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 14:09:45,567 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 14:09:45,567 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 14:09:45,567 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 14:09:45,568 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 14:09:45,568 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 14:09:45,568 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 14:09:45,568 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 14:09:45,568 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 14:09:45,568 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 14:09:45,568 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 14:09:45,568 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 14:09:45,575 - models.llm_integration - INFO - ✅ Loaded dynamic prompt templates from YAML
2025-06-06 14:09:45,575 - models.llm_integration - INFO - LLM Integration initialized (enabled: True, auto-detect: True)
2025-06-06 14:09:45,575 - models.llm_integration - INFO - Loaded 14 prompt templates
2025-06-06 14:09:45,575 - models.llm_integration - INFO - LLM background processing started
2025-06-06 14:09:45,575 - __main__ - INFO - [OK] LLM integration initialized and started
2025-06-06 14:09:45,575 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 14:09:45,575 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 14:09:45,575 - utils.system_monitor - INFO - System Monitor initialized
2025-06-06 14:09:45,575 - __main__ - INFO - [OK] System monitor initialized
2025-06-06 14:09:45,575 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 14:09:45,575 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 14:09:45,597 - utils.system_monitor - INFO - Starting system monitoring (interval: 60s)
2025-06-06 14:09:45,740 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 14:09:45,740 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 14:09:45,742 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 14:09:45,742 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 14:09:51,634 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 14:09:51,634 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 14:09:51,634 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 14:09:51,636 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 14:09:51,636 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 14:09:51,636 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 14:09:51,636 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 14:09:51,636 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 14:09:51,636 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 14:09:51,636 - __main__ - INFO - ============================================================
2025-06-06 14:09:51,638 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 14:09:51,638 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 14:09:51,638 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 14:09:51,638 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 14:09:51,638 - __main__ - INFO - ============================================================
2025-06-06 14:09:52,263 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 14:09:52,263 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 100.00%)
2025-06-06 14:09:54,626 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 14:09:54,629 - models.llm_integration - WARNING - Error analyzing market regime: '>' not supported between instances of 'NoneType' and 'float'
2025-06-06 14:09:54,629 - models.llm_integration - ERROR - Error building dynamic prompt: 'market_regime_context'
2025-06-06 14:09:58,571 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 14:09:58,571 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 95%)
2025-06-06 14:10:24,644 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:10:24 -0600] "GET / HTTP/1.1" 200 61296 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 14:10:24,893 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 14:10:24,894 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:10:24 -0600] "GET /api/data HTTP/1.1" 200 2936 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 14:10:38,760 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:10:38 -0600] "GET /api/status HTTP/1.1" 200 292 "-" "python-requests/2.32.3"
2025-06-06 14:10:38,786 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:10:38 -0600] "GET /api/data HTTP/1.1" 200 2935 "-" "python-requests/2.32.3"
2025-06-06 14:10:38,801 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:10:38 -0600] "GET /api/data HTTP/1.1" 200 2936 "-" "python-requests/2.32.3"
2025-06-06 14:10:38,818 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:10:38 -0600] "GET /api/data HTTP/1.1" 200 2935 "-" "python-requests/2.32.3"
2025-06-06 14:10:38,833 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:10:38 -0600] "GET /api/data HTTP/1.1" 200 2935 "-" "python-requests/2.32.3"
2025-06-06 14:10:38,848 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:10:38 -0600] "GET /api/data HTTP/1.1" 200 2936 "-" "python-requests/2.32.3"
2025-06-06 14:10:38,863 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:10:38 -0600] "GET /api/data HTTP/1.1" 200 2936 "-" "python-requests/2.32.3"
2025-06-06 14:10:38,879 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:10:38 -0600] "GET /api/data HTTP/1.1" 200 2936 "-" "python-requests/2.32.3"
2025-06-06 14:10:38,895 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:10:38 -0600] "GET /api/data HTTP/1.1" 200 2935 "-" "python-requests/2.32.3"
2025-06-06 14:10:38,909 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:10:38 -0600] "GET /api/data HTTP/1.1" 200 2936 "-" "python-requests/2.32.3"
2025-06-06 14:10:38,934 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 14:10:41,639 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 14:10:41,639 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 14:10:45,730 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.6%
2025-06-06 14:10:45,853 - utils.system_monitor - WARNING - Memory usage high: 80.6%
2025-06-06 14:10:46,652 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 14:10:49,900 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 14:10:49,900 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:10:38 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Python/3.9 websockets/12.0"
2025-06-06 14:10:52,625 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 14:10:52,625 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 14:10:54,719 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:10:54 -0600] "GET /api/settings/get HTTP/1.1" 200 333 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 14:11:08,068 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:11:08 -0600] "GET /api/data HTTP/1.1" 200 3845 "-" "curl/8.12.1"
2025-06-06 14:11:15,624 - models.llm_integration - ERROR - Error building dynamic prompt: 'market_regime_context'
2025-06-06 14:11:19,849 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:11:19,849 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 14:11:45,972 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 6.2%, Memory: 79.6%, Signals/min: 0.5
2025-06-06 14:11:45,979 - utils.system_monitor - INFO - Garbage collection freed 87 objects
2025-06-06 14:11:46,095 - utils.system_monitor - WARNING - Memory usage high: 79.6%
2025-06-06 14:12:04,855 - models.llm_integration - ERROR - Error building dynamic prompt: 'market_regime_context'
2025-06-06 14:12:08,629 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:12:08,630 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 100%)
2025-06-06 14:12:40,114 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 14:12:40,114 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 14:12:45,111 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 14:12:46,223 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 2.4%, Memory: 77.2%, Signals/min: 0.3
2025-06-06 14:12:46,229 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 14:12:46,349 - utils.system_monitor - WARNING - Memory usage high: 77.2%
2025-06-06 14:12:48,716 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 14:12:48,716 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 14:12:53,646 - models.llm_integration - ERROR - Error building dynamic prompt: 'market_regime_context'
2025-06-06 14:12:57,693 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:12:57,693 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 90%)
2025-06-06 14:13:38,757 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 14:13:38,757 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 14:13:42,692 - models.llm_integration - ERROR - Error building dynamic prompt: 'market_regime_context'
2025-06-06 14:13:43,759 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 14:13:46,486 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 85.0%
2025-06-06 14:13:46,493 - utils.system_monitor - INFO - Garbage collection freed 131 objects
2025-06-06 14:13:46,608 - utils.system_monitor - WARNING - Memory usage high: 83.4%
2025-06-06 14:13:46,643 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:13:46,644 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 14:13:47,444 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 14:13:47,444 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 14:14:31,666 - models.llm_integration - ERROR - Error building dynamic prompt: 'market_regime_context'
2025-06-06 14:14:36,062 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:14:36,062 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 14:14:45,612 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 14:14:46,739 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.9%
2025-06-06 14:14:46,746 - utils.system_monitor - INFO - Garbage collection freed 246 objects
2025-06-06 14:14:46,863 - utils.system_monitor - WARNING - Memory usage high: 80.8%
2025-06-06 14:14:57,922 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 14:14:57,922 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 14:15:02,945 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 14:15:06,404 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 14:15:06,406 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 14:15:06,630 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 14:15:06,630 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 75.59%)
2025-06-06 14:15:08,939 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 14:15:21,078 - models.llm_integration - ERROR - Error building dynamic prompt: 'market_regime_context'
2025-06-06 14:15:25,409 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:15:25,410 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 95%)
2025-06-06 14:15:46,989 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.8%
2025-06-06 14:15:46,996 - utils.system_monitor - INFO - Garbage collection freed 96 objects
2025-06-06 14:15:47,105 - utils.system_monitor - WARNING - Memory usage high: 80.9%
2025-06-06 14:15:47,386 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 14:15:47,386 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 78.66%)
2025-06-06 14:16:11,008 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 14:16:11,008 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 14:16:11,013 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 14:16:11,013 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 14:16:11,014 - storage.live_store - INFO - State loaded from cache file
2025-06-06 14:16:11,014 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 14:16:11,014 - storage.live_store - INFO - Background tasks started
2025-06-06 14:16:11,014 - __main__ - INFO - [OK] Data store initialized
2025-06-06 14:16:11,014 - analytics.signal_tracker - INFO - Signal Tracker initialized (TP: 2.0%, SL: 1.0%)
2025-06-06 14:16:11,014 - analytics.performance_analyzer - INFO - Performance Analyzer initialized
2025-06-06 14:16:11,014 - __main__ - INFO - [OK] Signal tracking and performance analytics initialized
2025-06-06 14:16:11,014 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 14:16:11,015 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 14:16:11,015 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 14:16:11,015 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 14:16:11,015 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 14:16:11,015 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 14:16:11,015 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 14:16:11,015 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 14:16:11,015 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 14:16:11,015 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 14:16:11,015 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 14:16:11,015 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 14:16:11,015 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 14:16:11,015 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 14:16:11,015 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 14:16:11,015 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 14:16:11,017 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 14:16:11,023 - models.llm_integration - INFO - ✅ Loaded dynamic prompt templates from YAML
2025-06-06 14:16:11,023 - models.llm_integration - INFO - LLM Integration initialized (enabled: True, auto-detect: True)
2025-06-06 14:16:11,023 - models.llm_integration - INFO - Loaded 14 prompt templates
2025-06-06 14:16:11,023 - models.llm_integration - INFO - LLM background processing started
2025-06-06 14:16:11,024 - __main__ - INFO - [OK] LLM integration initialized and started
2025-06-06 14:16:11,024 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 14:16:11,024 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 14:16:11,024 - utils.system_monitor - INFO - System Monitor initialized
2025-06-06 14:16:11,024 - __main__ - INFO - [OK] System monitor initialized
2025-06-06 14:16:11,024 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 14:16:11,024 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 14:16:11,046 - utils.system_monitor - INFO - Starting system monitoring (interval: 60s)
2025-06-06 14:16:11,184 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 14:16:11,184 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 14:16:11,184 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 14:16:11,184 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 14:16:12,823 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 14:16:12,825 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 14:16:12,825 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 14:16:12,825 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 14:16:12,826 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 14:16:12,826 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 14:16:12,826 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 14:16:12,826 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 14:16:12,826 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 14:16:12,827 - __main__ - INFO - ============================================================
2025-06-06 14:16:12,827 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 14:16:12,827 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 14:16:12,827 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 14:16:12,827 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 14:16:12,827 - __main__ - INFO - ============================================================
2025-06-06 14:16:12,968 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 14:16:13,363 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 14:16:13,364 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 100.00%)
2025-06-06 14:16:15,713 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 14:16:19,972 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:16:19,972 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 14:16:49,157 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:16:49 -0600] "GET /api/data HTTP/1.1" 200 4258 "-" "curl/8.12.1"
2025-06-06 14:16:58,933 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 14:16:58,933 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:16:58 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 14:16:59,662 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:16:59 -0600] "GET /api/status HTTP/1.1" 200 292 "-" "python-requests/2.32.3"
2025-06-06 14:16:59,677 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:16:59 -0600] "GET /api/data HTTP/1.1" 200 4613 "-" "python-requests/2.32.3"
2025-06-06 14:16:59,694 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:16:59 -0600] "GET /api/data HTTP/1.1" 200 4612 "-" "python-requests/2.32.3"
2025-06-06 14:16:59,708 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:16:59 -0600] "GET /api/data HTTP/1.1" 200 4613 "-" "python-requests/2.32.3"
2025-06-06 14:16:59,723 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:16:59 -0600] "GET /api/data HTTP/1.1" 200 4613 "-" "python-requests/2.32.3"
2025-06-06 14:16:59,740 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:16:59 -0600] "GET /api/data HTTP/1.1" 200 4613 "-" "python-requests/2.32.3"
2025-06-06 14:16:59,754 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:16:59 -0600] "GET /api/data HTTP/1.1" 200 4611 "-" "python-requests/2.32.3"
2025-06-06 14:16:59,769 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:16:59 -0600] "GET /api/data HTTP/1.1" 200 4613 "-" "python-requests/2.32.3"
2025-06-06 14:16:59,786 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:16:59 -0600] "GET /api/data HTTP/1.1" 200 4613 "-" "python-requests/2.32.3"
2025-06-06 14:16:59,802 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:16:59 -0600] "GET /api/data HTTP/1.1" 200 4613 "-" "python-requests/2.32.3"
2025-06-06 14:16:59,828 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 14:16:59,964 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:16:59 -0600] "GET / HTTP/1.1" 200 61296 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 14:16:59,969 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 14:16:59,970 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:16:12 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 14:17:00,128 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 14:17:00,129 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:17:00 -0600] "GET /api/data HTTP/1.1" 200 4613 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 14:17:11,176 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 8.2%, Memory: 79.9%, Signals/min: 1.0
2025-06-06 14:17:11,182 - utils.system_monitor - INFO - Garbage collection freed 3 objects
2025-06-06 14:17:11,300 - utils.system_monitor - WARNING - Memory usage high: 79.9%
2025-06-06 14:17:11,301 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 14:17:11,302 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:16:59 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Python/3.9 websockets/12.0"
2025-06-06 14:17:45,769 - models.llm_integration - ERROR - No valid JSON found in LLM response
2025-06-06 14:17:45,769 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:17:45,769 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 0%)
2025-06-06 14:18:11,426 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.3%
2025-06-06 14:18:11,432 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 14:18:11,554 - utils.system_monitor - WARNING - Memory usage high: 79.6%
2025-06-06 14:18:35,349 - models.llm_integration - ERROR - No valid JSON found in LLM response
2025-06-06 14:18:35,350 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:18:35,350 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 0%)
2025-06-06 14:19:11,670 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 82.4%
2025-06-06 14:19:11,677 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 14:19:11,795 - utils.system_monitor - WARNING - Memory usage high: 82.4%
2025-06-06 14:19:24,913 - models.llm_integration - ERROR - No valid JSON found in LLM response
2025-06-06 14:19:24,914 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:19:24,914 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 0%)
2025-06-06 14:20:11,913 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.6%
2025-06-06 14:20:11,919 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 14:20:12,036 - utils.system_monitor - WARNING - Memory usage high: 81.6%
2025-06-06 14:20:14,690 - models.llm_integration - ERROR - No valid JSON found in LLM response
2025-06-06 14:20:14,690 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:20:14,690 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 0%)
2025-06-06 14:21:04,264 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:21:04,264 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 75%)
2025-06-06 14:21:11,070 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 14:21:12,162 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.8%
2025-06-06 14:21:12,167 - utils.system_monitor - INFO - Garbage collection freed 284 objects
2025-06-06 14:21:12,287 - utils.system_monitor - WARNING - Memory usage high: 81.8%
2025-06-06 14:21:51,617 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 14:21:55,624 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:21:55,624 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 14:22:12,417 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 3.4%, Memory: 77.8%, Signals/min: 0.2
2025-06-06 14:22:12,423 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 14:22:12,538 - utils.system_monitor - WARNING - Memory usage high: 77.8%
2025-06-06 14:22:44,892 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 14:22:44,892 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 14:22:45,145 - models.llm_integration - ERROR - No valid JSON found in LLM response
2025-06-06 14:22:45,145 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:22:45,145 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 0%)
2025-06-06 14:22:49,119 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 14:22:49,120 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 95%)
2025-06-06 14:23:12,666 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 8.2%, Memory: 76.9%, Signals/min: 0.3
2025-06-06 14:23:12,672 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 14:23:12,794 - utils.system_monitor - WARNING - Memory usage high: 76.9%
2025-06-06 14:23:25,217 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 14:23:25,218 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 14:23:34,721 - models.llm_integration - ERROR - No valid JSON found in LLM response
2025-06-06 14:23:34,722 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:23:34,722 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 0%)
2025-06-06 14:24:12,924 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 3.3%, Memory: 74.9%, Signals/min: 0.4
2025-06-06 14:24:12,931 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 14:24:13,047 - utils.system_monitor - WARNING - Memory usage high: 74.9%
2025-06-06 14:24:24,315 - models.llm_integration - ERROR - No valid JSON found in LLM response
2025-06-06 14:24:24,315 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:24:24,315 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 0%)
2025-06-06 14:25:13,183 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 20.0%, Memory: 73.6%, Signals/min: 0.3
2025-06-06 14:25:13,190 - utils.system_monitor - INFO - Garbage collection freed 176 objects
2025-06-06 14:25:13,309 - utils.system_monitor - WARNING - Memory usage high: 73.6%
2025-06-06 14:25:14,040 - models.llm_integration - ERROR - No valid JSON found in LLM response
2025-06-06 14:25:14,040 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:25:14,040 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 0%)
2025-06-06 14:26:03,484 - models.llm_integration - ERROR - No valid JSON found in LLM response
2025-06-06 14:26:03,484 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:26:03,484 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 0%)
2025-06-06 14:26:11,077 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 14:26:13,419 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 5.3%, Memory: 73.9%, Signals/min: 0.3
2025-06-06 14:26:13,426 - utils.system_monitor - INFO - Garbage collection freed 159 objects
2025-06-06 14:26:13,545 - utils.system_monitor - WARNING - Memory usage high: 73.9%
2025-06-06 14:26:53,213 - models.llm_integration - ERROR - No valid JSON found in LLM response
2025-06-06 14:26:53,213 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:26:53,213 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 0%)
2025-06-06 14:27:13,686 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 1.9%, Memory: 74.2%, Signals/min: 0.3
2025-06-06 14:27:13,691 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 14:27:13,816 - utils.system_monitor - WARNING - Memory usage high: 74.2%
2025-06-06 14:27:40,742 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 14:27:45,462 - models.llm_integration - ERROR - No valid JSON found in LLM response
2025-06-06 14:27:45,462 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:27:45,462 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 0%)
2025-06-06 14:28:13,943 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 4.0%, Memory: 74.1%, Signals/min: 0.2
2025-06-06 14:28:13,948 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 14:28:14,069 - utils.system_monitor - WARNING - Memory usage high: 74.1%
2025-06-06 14:28:35,178 - models.llm_integration - ERROR - No valid JSON found in LLM response
2025-06-06 14:28:35,178 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:28:35,178 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 0%)
2025-06-06 14:29:07,263 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 14:29:07,263 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 14:29:14,190 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 8.2%, Memory: 74.2%, Signals/min: 0.3
2025-06-06 14:29:14,195 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 14:29:14,316 - utils.system_monitor - WARNING - Memory usage high: 74.2%
2025-06-06 14:29:24,692 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 14:29:24,692 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 95%)
2025-06-06 14:30:14,245 - models.llm_integration - ERROR - No valid JSON found in LLM response
2025-06-06 14:30:14,245 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:30:14,245 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 0%)
2025-06-06 14:30:14,440 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.4%
2025-06-06 14:30:14,447 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 14:30:14,563 - utils.system_monitor - WARNING - Memory usage high: 81.5%
2025-06-06 14:31:03,820 - models.llm_integration - ERROR - No valid JSON found in LLM response
2025-06-06 14:31:03,821 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:31:03,821 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 0%)
2025-06-06 14:31:11,081 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 14:31:14,692 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.5%
2025-06-06 14:31:14,698 - utils.system_monitor - INFO - Garbage collection freed 159 objects
2025-06-06 14:31:14,813 - utils.system_monitor - WARNING - Memory usage high: 81.5%
2025-06-06 14:31:36,062 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 14:31:36,062 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 14:31:36,069 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 14:31:36,069 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 14:31:36,069 - storage.live_store - INFO - State loaded from cache file
2025-06-06 14:31:36,069 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 14:31:36,069 - storage.live_store - INFO - Background tasks started
2025-06-06 14:31:36,069 - __main__ - INFO - [OK] Data store initialized
2025-06-06 14:31:36,069 - analytics.signal_tracker - INFO - Signal Tracker initialized (TP: 2.0%, SL: 1.0%)
2025-06-06 14:31:36,069 - analytics.performance_analyzer - INFO - Performance Analyzer initialized
2025-06-06 14:31:36,069 - __main__ - INFO - [OK] Signal tracking and performance analytics initialized
2025-06-06 14:31:36,069 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 14:31:36,069 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 14:31:36,069 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 14:31:36,070 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 14:31:36,070 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 14:31:36,070 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 14:31:36,070 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 14:31:36,070 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 14:31:36,070 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 14:31:36,070 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 14:31:36,070 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 14:31:36,070 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 14:31:36,070 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 14:31:36,070 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 14:31:36,070 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 14:31:36,070 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 14:31:36,070 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 14:31:36,078 - models.llm_integration - INFO - ✅ Loaded dynamic prompt templates from YAML
2025-06-06 14:31:36,078 - models.llm_integration - INFO - LLM Integration initialized (enabled: True, auto-detect: True)
2025-06-06 14:31:36,078 - models.llm_integration - INFO - Loaded 14 prompt templates
2025-06-06 14:31:36,078 - models.llm_integration - INFO - LLM background processing started
2025-06-06 14:31:36,079 - __main__ - INFO - [OK] LLM integration initialized and started
2025-06-06 14:31:36,079 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 14:31:36,079 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 14:31:36,079 - utils.system_monitor - INFO - System Monitor initialized
2025-06-06 14:31:36,079 - __main__ - INFO - [OK] System monitor initialized
2025-06-06 14:31:36,079 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 14:31:36,079 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 14:31:36,103 - utils.system_monitor - INFO - Starting system monitoring (interval: 60s)
2025-06-06 14:31:36,254 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 14:31:36,254 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 14:31:36,255 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 14:31:36,255 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 14:31:37,895 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 14:31:37,896 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 14:31:37,896 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 14:31:37,896 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 14:31:37,897 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 14:31:37,898 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 14:31:37,898 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 14:31:37,898 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 14:31:37,898 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 14:31:37,899 - __main__ - INFO - ============================================================
2025-06-06 14:31:37,899 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 14:31:37,899 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 14:31:37,899 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 14:31:37,899 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 14:31:37,899 - __main__ - INFO - ============================================================
2025-06-06 14:31:38,234 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - SHORT
2025-06-06 14:31:38,234 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - SHORT (confidence: 100.00%)
2025-06-06 14:31:40,339 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 14:31:40,621 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 14:31:44,777 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 14:31:44,777 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 95%)
2025-06-06 14:32:36,228 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.9%
2025-06-06 14:32:36,234 - utils.system_monitor - INFO - Garbage collection freed 3 objects
2025-06-06 14:32:36,352 - utils.system_monitor - WARNING - Memory usage high: 80.9%
2025-06-06 14:32:49,488 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 14:32:49,489 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 14:32:49,494 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 14:32:49,495 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 14:32:49,495 - storage.live_store - INFO - State loaded from cache file
2025-06-06 14:32:49,495 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 14:32:49,495 - storage.live_store - INFO - Background tasks started
2025-06-06 14:32:49,495 - __main__ - INFO - [OK] Data store initialized
2025-06-06 14:32:49,495 - analytics.signal_tracker - INFO - Signal Tracker initialized (TP: 2.0%, SL: 1.0%)
2025-06-06 14:32:49,495 - analytics.performance_analyzer - INFO - Performance Analyzer initialized
2025-06-06 14:32:49,495 - __main__ - INFO - [OK] Signal tracking and performance analytics initialized
2025-06-06 14:32:49,496 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 14:32:49,496 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 14:32:49,496 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 14:32:49,496 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 14:32:49,496 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 14:32:49,496 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 14:32:49,496 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 14:32:49,496 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 14:32:49,496 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 14:32:49,496 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 14:32:49,496 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 14:32:49,496 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 14:32:49,496 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 14:32:49,496 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 14:32:49,496 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 14:32:49,497 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 14:32:49,497 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 14:32:49,503 - models.llm_integration - INFO - ✅ Loaded dynamic prompt templates from YAML
2025-06-06 14:32:49,503 - models.llm_integration - INFO - LLM Integration initialized (enabled: True, auto-detect: True)
2025-06-06 14:32:49,503 - models.llm_integration - INFO - Loaded 14 prompt templates
2025-06-06 14:32:49,504 - models.llm_integration - INFO - LLM background processing started
2025-06-06 14:32:49,504 - __main__ - INFO - [OK] LLM integration initialized and started
2025-06-06 14:32:49,504 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 14:32:49,504 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 14:32:49,504 - utils.system_monitor - INFO - System Monitor initialized
2025-06-06 14:32:49,504 - __main__ - INFO - [OK] System monitor initialized
2025-06-06 14:32:49,505 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 14:32:49,505 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 14:32:49,525 - utils.system_monitor - INFO - Starting system monitoring (interval: 60s)
2025-06-06 14:32:49,669 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 14:32:49,669 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 14:32:49,669 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 14:32:49,670 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 14:32:51,392 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 14:32:51,392 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 14:32:51,392 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 14:32:51,393 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 14:32:51,393 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 14:32:51,394 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 14:32:51,394 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 14:32:51,395 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 14:32:51,395 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 14:32:51,395 - __main__ - INFO - ============================================================
2025-06-06 14:32:51,395 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 14:32:51,395 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 14:32:51,395 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 14:32:51,395 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 14:32:51,395 - __main__ - INFO - ============================================================
2025-06-06 14:32:52,000 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 14:32:52,000 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 100.00%)
2025-06-06 14:32:52,876 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 14:32:54,392 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 14:32:58,733 - models.llm_integration - INFO - Raw LLM response content: '```json\n{\n  "final_decision": "WAIT",\n  "confidence": 85,\n  "reasoning": "The ORDERFLOW model\'s strong conviction (100%) for a BUY signal is compelling; however, the market context indicates that we are currently in a sideways trend with an adaptive tone suggesting less reliability of trend signals. The absence of recent performance data and high volatility uncertainty necessitates caution.",\n  "risk_assessment": "MEDIUM",\n  "model_consensus": "STRONG"\n}\n```'
2025-06-06 14:32:58,733 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:32:58,733 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 14:33:49,640 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.9%
2025-06-06 14:33:49,645 - utils.system_monitor - INFO - Garbage collection freed 3 objects
2025-06-06 14:33:49,763 - utils.system_monitor - WARNING - Memory usage high: 80.8%
2025-06-06 14:34:24,097 - models.llm_integration - INFO - Raw LLM response content: '```json\n{\n  "final_decision": "WAIT",\n  "confidence": 95,\n  "reasoning": "Despite the OrderFlow model\'s strong buy signal with a value of 0.412 and VWAP near support at $104316.40 suggesting potential entry points in future price movements within this range, recent market context indicates that we are currently experiencing sideways trends which reduce reliability on these signals as per the Adaptive Tone\'s 60% signal reliability during ranging periods.",\n  "risk_assessment": "MEDIUM",\n  "model_'
2025-06-06 14:34:24,097 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '```json\n{\n  "final_decision": "WAIT",\n  "confidence": 95,\n  "reasoning": "Despite the OrderFlow model\'s strong buy signal with a value of 0.412 and VWAP near support at $104316.40 suggesting potential entry points in future price movements within this range, recent market context indicates that we are currently experiencing sideways trends which reduce reliability on these signals as per the Adaptive Tone\'s 60% signal reliability during ranging periods.",\n  "risk_assessment": "MEDIUM",\n  "model_consensus'
2025-06-06 14:34:24,098 - models.llm_integration - INFO - Manual extraction successful: {"final_decision": "WAIT", "confidence": 95.0, "reasoning": "Despite the OrderFlow model's strong buy signal with a value of 0.412 and VWAP near support at $104316.40 suggesting potential entry points in future price movements within this range, recent market context indicates that we are currently experiencing sideways trends which reduce reliability on these signals as per the Adaptive Tone's 60% signal reliability during ranging periods.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:34:24,098 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 95.0, "reasoning": "Despite the OrderFlow model's strong buy signal with a value of 0.412 and VWAP near support at $104316.40 suggesting potential entry points in future price movements within this range, recent market context indicates that we are currently experiencing sideways trends which reduce reliability on these signals as per the Adaptive Tone's 60% signal reliability during ranging periods.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:34:24,098 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:34:24,098 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 95%)
2025-06-06 14:34:46,489 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:34:46 -0600] "GET /api/data HTTP/1.1" 200 6105 "-" "curl/8.12.1"
2025-06-06 14:34:49,892 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.1%
2025-06-06 14:34:50,022 - utils.system_monitor - WARNING - Memory usage high: 81.2%
2025-06-06 14:35:06,825 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:35:06 -0600] "GET /api/status HTTP/1.1" 200 292 "-" "python-requests/2.32.3"
2025-06-06 14:35:06,849 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:35:06 -0600] "GET /api/data HTTP/1.1" 200 6089 "-" "python-requests/2.32.3"
2025-06-06 14:35:06,864 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:35:06 -0600] "GET /api/data HTTP/1.1" 200 6088 "-" "python-requests/2.32.3"
2025-06-06 14:35:06,879 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:35:06 -0600] "GET /api/data HTTP/1.1" 200 6089 "-" "python-requests/2.32.3"
2025-06-06 14:35:06,895 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:35:06 -0600] "GET /api/data HTTP/1.1" 200 6089 "-" "python-requests/2.32.3"
2025-06-06 14:35:06,911 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:35:06 -0600] "GET /api/data HTTP/1.1" 200 6089 "-" "python-requests/2.32.3"
2025-06-06 14:35:06,926 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:35:06 -0600] "GET /api/data HTTP/1.1" 200 6089 "-" "python-requests/2.32.3"
2025-06-06 14:35:06,943 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:35:06 -0600] "GET /api/data HTTP/1.1" 200 6089 "-" "python-requests/2.32.3"
2025-06-06 14:35:06,958 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:35:06 -0600] "GET /api/data HTTP/1.1" 200 6089 "-" "python-requests/2.32.3"
2025-06-06 14:35:06,973 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:35:06 -0600] "GET /api/data HTTP/1.1" 200 6088 "-" "python-requests/2.32.3"
2025-06-06 14:35:06,999 - ui.ai_strategy_tuner - INFO - WebSocket client connected (2 total)
2025-06-06 14:35:13,642 - models.llm_integration - INFO - Raw LLM response content: '```json\n{\n  "final_decision": "WAIT",\n  "confidence": 95,\n  "reasoning": "Given the current sideways market regime indicated by \'Regime: RANGING\' with a bias towards reversal signals at support/resistance levels and recent performance patterns showing less reliable trend signals (60% reliability), I am inclined to wait. The ORDERFLOW model suggests buying, but without reasoning provided for the high confidence level which is concerning in terms of decision strength.",\n  "risk_assessment": "MEDIU'
2025-06-06 14:35:13,643 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:35:13,643 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 95%)
2025-06-06 14:35:16,536 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 14:35:16,536 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 14:35:18,152 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (1 total)
2025-06-06 14:35:18,153 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:35:07 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Python/3.9 websockets/12.0"
2025-06-06 14:35:48,766 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 14:35:48,766 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 14:35:50,146 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.1%
2025-06-06 14:35:50,152 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 14:35:50,275 - utils.system_monitor - WARNING - Memory usage high: 81.1%
2025-06-06 14:36:03,096 - models.llm_integration - INFO - Raw LLM response content: '```json\n{\n  "final_decision": "SHORT",\n  "confidence": 95.0,\n  "reasoning": "Despite the ORDERFLOW model\'s strong buy signal (100% confidence) and VWAP suggesting a wait position with high conviction at near support levels (-1.546e-5), recent performance feedback indicates mixed results in trend signals reliability, which is consistent with our current market regime of ranging/sideways movement where such signals are less reliable (current session - 60% reliability). The VWAP model\'s near signal'
2025-06-06 14:36:03,097 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '```json\n{\n  "final_decision": "SHORT",\n  "confidence": 95.0,\n  "reasoning": "Despite the ORDERFLOW model\'s strong buy signal (100% confidence) and VWAP suggesting a wait position with high conviction at near support levels (-1.546e-5), recent performance feedback indicates mixed results in trend signals reliability, which is consistent with our current market regime of ranging/sideways movement where such signals are less reliable (current session - 60% reliability). The VWAP model\'s near signal and the low volatility indicated by the Volatility model suggest'
2025-06-06 14:36:03,097 - models.llm_integration - INFO - Manual extraction successful: {"final_decision": "SHORT", "confidence": 95.0, "reasoning": "Extracted from malformed response", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:36:03,097 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "SHORT", "confidence": 95.0, "reasoning": "Extracted from malformed response", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:36:03,097 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 14:36:03,097 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 95%)
2025-06-06 14:36:50,387 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.5%
2025-06-06 14:36:50,393 - utils.system_monitor - INFO - Garbage collection freed 176 objects
2025-06-06 14:36:50,509 - utils.system_monitor - WARNING - Memory usage high: 80.5%
2025-06-06 14:36:52,816 - models.llm_integration - INFO - Raw LLM response content: '{\n  "final_decision": "WAIT",\n  "confidence": 85.0,\n  "reasoning": "Given the strong disagreement between ORDERFLOW\'s SELL signal (100% confidence) and VOLATILITY\'s BUY signal (100% confidence), I am inclined to prioritize caution in this volatile market. The current sideways trend with low volume profile suggests a lack of clear direction, which aligns more closely with the WAIT decision from both RSI and VWAP models despite their perfect confidence scores.",\n  "risk_assessment": "MEDIUM",\n'
2025-06-06 14:36:52,816 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 85.0,\n  "reasoning": "Given the strong disagreement between ORDERFLOW\'s SELL signal (100% confidence) and VOLATILITY\'s BUY signal (100% confidence), I am inclined to prioritize caution in this volatile market. The current sideways trend with low volume profile suggests a lack of clear direction, which aligns more closely with the WAIT decision from both RSI and VWAP models despite their perfect confidence scores.",\n  "risk_assessment": "MEDIUM",\n'
2025-06-06 14:36:52,817 - models.llm_integration - INFO - Manual extraction successful: {"final_decision": "WAIT", "confidence": 85.0, "reasoning": "Given the strong disagreement between ORDERFLOW's SELL signal (100% confidence) and VOLATILITY's BUY signal (100% confidence), I am inclined to prioritize caution in this volatile market. The current sideways trend with low volume profile suggests a lack of clear direction, which aligns more closely with the WAIT decision from both RSI and VWAP models despite their perfect confidence scores.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:36:52,817 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 85.0, "reasoning": "Given the strong disagreement between ORDERFLOW's SELL signal (100% confidence) and VOLATILITY's BUY signal (100% confidence), I am inclined to prioritize caution in this volatile market. The current sideways trend with low volume profile suggests a lack of clear direction, which aligns more closely with the WAIT decision from both RSI and VWAP models despite their perfect confidence scores.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:36:52,817 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:36:52,817 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 14:37:42,129 - models.llm_integration - INFO - Raw LLM response content: '```json\n{\n  "final_decision": "WAIT",\n  "confidence": 95,\n  "reasoning": "While the OrderFlow model suggests a strong buy signal due to buying pressure with no additional reasoning provided (Value: 0.51), and VWAP indicates near support at very low value (-6.32e-8) without contextual analysis, these signals are not sufficient for action in light of recent mixed performance patterns indicating variable market conditions.",\n  "risk_assessment": "MEDIUM",\n  "model_consensus": "STRONG"\n}\n```'
2025-06-06 14:37:42,130 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:37:42,130 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 95%)
2025-06-06 14:37:49,525 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 14:37:50,624 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 4.7%, Memory: 77.0%, Signals/min: 0.6
2025-06-06 14:37:50,629 - utils.system_monitor - INFO - Garbage collection freed 159 objects
2025-06-06 14:37:50,745 - utils.system_monitor - WARNING - Memory usage high: 77.0%
2025-06-06 14:38:29,493 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 14:38:34,114 - models.llm_integration - INFO - Raw LLM response content: '```json\n{\n  "final_decision": "WAIT",\n  "confidence": 95.0,\n  "reasoning": "The ORDERFLOW model suggests a strong buy signal with high confidence; however, the RSI and VWAP models advise caution by recommending to wait due to neutral signals indicating no clear trend direction in price movement. Given that we are currently experiencing low volatility within a sideways market regime where ranging is prevalent, it\'s essential to prioritize risk management over aggressive trading strategies.",\n  "r'
2025-06-06 14:38:34,114 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '```json\n{\n  "final_decision": "WAIT",\n  "confidence": 95.0,\n  "reasoning": "The ORDERFLOW model suggests a strong buy signal with high confidence; however, the RSI and VWAP models advise caution by recommending to wait due to neutral signals indicating no clear trend direction in price movement. Given that we are currently experiencing low volatility within a sideways market regime where ranging is prevalent, it\'s essential to prioritize risk management over aggressive trading strategies.",\n  "risk_assessment": "LOW",\n  "model_consensus": "STRONG'
2025-06-06 14:38:34,115 - models.llm_integration - INFO - Manual extraction successful: {"final_decision": "WAIT", "confidence": 95.0, "reasoning": "The ORDERFLOW model suggests a strong buy signal with high confidence; however, the RSI and VWAP models advise caution by recommending to wait due to neutral signals indicating no clear trend direction in price movement. Given that we are currently experiencing low volatility within a sideways market regime where ranging is prevalent, it's essential to prioritize risk management over aggressive trading strategies.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:38:34,115 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 95.0, "reasoning": "The ORDERFLOW model suggests a strong buy signal with high confidence; however, the RSI and VWAP models advise caution by recommending to wait due to neutral signals indicating no clear trend direction in price movement. Given that we are currently experiencing low volatility within a sideways market regime where ranging is prevalent, it's essential to prioritize risk management over aggressive trading strategies.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:38:34,115 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:38:34,115 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 95%)
2025-06-06 14:38:50,873 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 7.3%, Memory: 77.4%, Signals/min: 0.5
2025-06-06 14:38:50,877 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 14:38:50,999 - utils.system_monitor - WARNING - Memory usage high: 77.3%
2025-06-06 14:39:23,785 - models.llm_integration - INFO - Raw LLM response content: '{\n  "final_decision": "WAIT",\n  "confidence": 75.0,\n  "reasoning": "Given the strong disagreement between ORDERFLOW\'s SELL signal (100% confidence) and VOLATILITY\'s BUY signal (100% confidence), I am inclined to prioritize caution in this volatile market. The current sideways trend with low volume profile suggests a lack of clear direction, which aligns with the ORDERFLOW model indicating selling pressure despite its high conviction score.",\n  "risk_assessment": "MEDIUM",\n  "model_consensus'
2025-06-06 14:39:23,786 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 75.0,\n  "reasoning": "Given the strong disagreement between ORDERFLOW\'s SELL signal (100% confidence) and VOLATILITY\'s BUY signal (100% confidence), I am inclined to prioritize caution in this volatile market. The current sideways trend with low volume profile suggests a lack of clear direction, which aligns with the ORDERFLOW model indicating selling pressure despite its high conviction score.",\n  "risk_assessment": "MEDIUM",\n  "model_consensus'
2025-06-06 14:39:23,786 - models.llm_integration - INFO - Manual extraction successful: {"final_decision": "WAIT", "confidence": 75.0, "reasoning": "Given the strong disagreement between ORDERFLOW's SELL signal (100% confidence) and VOLATILITY's BUY signal (100% confidence), I am inclined to prioritize caution in this volatile market. The current sideways trend with low volume profile suggests a lack of clear direction, which aligns with the ORDERFLOW model indicating selling pressure despite its high conviction score.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:39:23,786 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 75.0, "reasoning": "Given the strong disagreement between ORDERFLOW's SELL signal (100% confidence) and VOLATILITY's BUY signal (100% confidence), I am inclined to prioritize caution in this volatile market. The current sideways trend with low volume profile suggests a lack of clear direction, which aligns with the ORDERFLOW model indicating selling pressure despite its high conviction score.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:39:23,786 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:39:23,786 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 75%)
2025-06-06 14:39:51,140 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 11.4%, Memory: 75.9%, Signals/min: 0.4
2025-06-06 14:39:51,146 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 14:39:51,265 - utils.system_monitor - WARNING - Memory usage high: 75.9%
2025-06-06 14:40:13,224 - models.llm_integration - INFO - Raw LLM response content: '```json\n{\n  "final_decision": "WAIT",\n  "confidence": 95,\n  "reasoning": "While the OrderFlow model suggests a strong buy signal due to buying pressure with no additional reasoning provided (Value: 0.892), and VWAP indicates near support levels at high confidence without specifics on price action or volume trends, these signals are not aligned when considering recent market context of sideways movement in the current ranging regime where volatility is low but buying pressure exists as per Volume'
2025-06-06 14:40:13,224 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '```json\n{\n  "final_decision": "WAIT",\n  "confidence": 95,\n  "reasoning": "While the OrderFlow model suggests a strong buy signal due to buying pressure with no additional reasoning provided (Value: 0.892), and VWAP indicates near support levels at high confidence without specifics on price action or volume trends, these signals are not aligned when considering recent market context of sideways movement in the current ranging regime where volatility is low but buying pressure exists as per Volume Profile analysis (Value: 2.1411749776445982e-07).'
2025-06-06 14:40:13,225 - models.llm_integration - INFO - Manual extraction successful: {"final_decision": "WAIT", "confidence": 95.0, "reasoning": "Extracted from malformed response", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:40:13,225 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 95.0, "reasoning": "Extracted from malformed response", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:40:13,225 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:40:13,225 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 95%)
2025-06-06 14:40:14,985 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 14:40:14,985 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 14:40:51,400 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 8.8%, Memory: 78.4%, Signals/min: 0.5
2025-06-06 14:40:51,409 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 14:40:51,522 - utils.system_monitor - WARNING - Memory usage high: 79.1%
2025-06-06 14:41:02,969 - models.llm_integration - INFO - Raw LLM response content: '{\n  "final_decision": "WAIT",\n  "confidence": 75,\n  "reasoning": "Given the strong disagreement between ORDERFLOW\'s SELL signal (100% confidence) and VOLATILITY\'s BUY signal (100% confidence), I am inclined to prioritize caution. The current market is in a ranging/sideways regime, which historically has shown less reliability for trend signals as per the adaptive tone analysis; this context further supports my decision to wait.",\n  "risk_assessment": "MEDIUM",\n  "model_consensus": "'
2025-06-06 14:41:02,970 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 75,\n  "reasoning": "Given the strong disagreement between ORDERFLOW\'s SELL signal (100% confidence) and VOLATILITY\'s BUY signal (100% confidence), I am inclined to prioritize caution. The current market is in a ranging/sideways regime, which historically has shown less reliability for trend signals as per the adaptive tone analysis; this context further supports my decision to wait.",\n  "risk_assessment": "MEDIUM",\n  "model_consensus": "'
2025-06-06 14:41:02,970 - models.llm_integration - INFO - Manual extraction successful: {"final_decision": "WAIT", "confidence": 75.0, "reasoning": "Given the strong disagreement between ORDERFLOW's SELL signal (100% confidence) and VOLATILITY's BUY signal (100% confidence), I am inclined to prioritize caution. The current market is in a ranging/sideways regime, which historically has shown less reliability for trend signals as per the adaptive tone analysis; this context further supports my decision to wait.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:41:02,970 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 75.0, "reasoning": "Given the strong disagreement between ORDERFLOW's SELL signal (100% confidence) and VOLATILITY's BUY signal (100% confidence), I am inclined to prioritize caution. The current market is in a ranging/sideways regime, which historically has shown less reliability for trend signals as per the adaptive tone analysis; this context further supports my decision to wait.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:41:02,970 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:41:02,970 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 75%)
2025-06-06 14:41:51,646 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.4%
2025-06-06 14:41:51,651 - utils.system_monitor - INFO - Garbage collection freed 176 objects
2025-06-06 14:41:51,769 - utils.system_monitor - WARNING - Memory usage high: 81.4%
2025-06-06 14:41:52,621 - models.llm_integration - INFO - Raw LLM response content: '```json\n{\n  "final_decision": "WAIT",\n  "confidence": 95,\n  "reasoning": "The ORDERFLOW model suggests a strong buy signal (100% confidence) based on BUY_PRESSURE with no provided reasoning. However, the RSI and VWAP models both recommend waiting due to neutral signals in their respective indicators; this disagreement is notable but not severe as all three have high conviction levels of 100%. The recent pattern analysis indicates mixed results from past decisions which suggest caution despite a '
2025-06-06 14:41:52,622 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '```json\n{\n  "final_decision": "WAIT",\n  "confidence": 95,\n  "reasoning": "The ORDERFLOW model suggests a strong buy signal (100% confidence) based on BUY_PRESSURE with no provided reasoning. However, the RSI and VWAP models both recommend waiting due to neutral signals in their respective indicators; this disagreement is notable but not severe as all three have high conviction levels of 100%. The recent pattern analysis indicates mixed results from past decisions which suggest caution despite a balanced volume profile. Given the current market regime\'s ranging nature, trend signals are less reliable'
2025-06-06 14:41:52,622 - models.llm_integration - INFO - Manual extraction successful: {"final_decision": "WAIT", "confidence": 95.0, "reasoning": "Extracted from malformed response", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:41:52,622 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 95.0, "reasoning": "Extracted from malformed response", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:41:52,623 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:41:52,623 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 95%)
2025-06-06 14:42:25,731 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 14:42:25,731 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 14:42:25,737 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 14:42:25,738 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 14:42:25,738 - storage.live_store - INFO - State loaded from cache file
2025-06-06 14:42:25,738 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 14:42:25,738 - storage.live_store - INFO - Background tasks started
2025-06-06 14:42:25,738 - __main__ - INFO - [OK] Data store initialized
2025-06-06 14:42:25,738 - analytics.signal_tracker - INFO - Signal Tracker initialized (TP: 2.0%, SL: 1.0%)
2025-06-06 14:42:25,738 - analytics.performance_analyzer - INFO - Performance Analyzer initialized
2025-06-06 14:42:25,738 - __main__ - INFO - [OK] Signal tracking and performance analytics initialized
2025-06-06 14:42:25,738 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 14:42:25,738 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 14:42:25,738 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 14:42:25,738 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 14:42:25,738 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 14:42:25,738 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 14:42:25,738 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 14:42:25,739 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 14:42:25,739 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 14:42:25,739 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 14:42:25,739 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 14:42:25,739 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 14:42:25,739 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 14:42:25,739 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 14:42:25,739 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 14:42:25,739 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 14:42:25,739 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 14:42:25,746 - models.llm_integration - INFO - ✅ Loaded dynamic prompt templates from YAML
2025-06-06 14:42:25,746 - models.llm_integration - INFO - LLM Integration initialized (enabled: True, auto-detect: True)
2025-06-06 14:42:25,746 - models.llm_integration - INFO - Loaded 14 prompt templates
2025-06-06 14:42:25,746 - models.llm_integration - INFO - LLM background processing started
2025-06-06 14:42:25,746 - __main__ - INFO - [OK] LLM integration initialized and started
2025-06-06 14:42:25,746 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 14:42:25,746 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 14:42:25,747 - utils.system_monitor - INFO - System Monitor initialized
2025-06-06 14:42:25,747 - __main__ - INFO - [OK] System monitor initialized
2025-06-06 14:42:25,747 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 14:42:25,747 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 14:42:25,768 - utils.system_monitor - INFO - Starting system monitoring (interval: 60s)
2025-06-06 14:42:25,909 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 14:42:25,909 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 14:42:25,909 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 14:42:25,910 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 14:42:27,625 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 14:42:27,625 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 14:42:27,625 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 14:42:27,625 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 14:42:27,626 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 14:42:27,626 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 14:42:27,626 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 14:42:27,628 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 14:42:27,628 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 14:42:27,628 - __main__ - INFO - ============================================================
2025-06-06 14:42:27,628 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 14:42:27,628 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 14:42:27,628 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 14:42:27,628 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 14:42:27,628 - __main__ - INFO - ============================================================
2025-06-06 14:42:27,893 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - SHORT
2025-06-06 14:42:27,893 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - SHORT (confidence: 100.00%)
2025-06-06 14:42:28,482 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 14:42:30,225 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 14:42:34,378 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 14:42:34,378 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 95%)
2025-06-06 14:42:59,706 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:42:59 -0600] "GET /api/data HTTP/1.1" 200 4141 "-" "curl/8.12.1"
2025-06-06 14:43:25,891 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.9%
2025-06-06 14:43:25,898 - utils.system_monitor - INFO - Garbage collection freed 3 objects
2025-06-06 14:43:26,018 - utils.system_monitor - WARNING - Memory usage high: 80.9%
2025-06-06 14:43:33,026 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:43:33 -0600] "GET /api/data HTTP/1.1" 200 5090 "-" "python-requests/2.32.3"
2025-06-06 14:43:58,669 - ui.ai_strategy_tuner - INFO - Applied Balanced preset to BTC-USDT
2025-06-06 14:43:58,670 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:43:58 -0600] "POST /api/presets/apply HTTP/1.1" 200 347 "http://localhost:8086/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-06 14:44:00,129 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:44:00,129 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 45%)
2025-06-06 14:44:26,148 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 83.9%
2025-06-06 14:44:26,156 - utils.system_monitor - INFO - Garbage collection freed 114 objects
2025-06-06 14:44:26,272 - utils.system_monitor - WARNING - Memory usage high: 83.9%
2025-06-06 14:44:46,433 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:44:46 -0600] "GET /api/data HTTP/1.1" 200 6066 "-" "python-requests/2.32.3"
2025-06-06 14:44:49,860 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '```json\n{\n  "final_decision": "WAIT",\n  "confidence": 85,\n  "reasoning": "Given the strong disagreement between ORDERFLOW (100% BUY), VOLATILITY (100% BUY), and RSI (100% SELL) signals with no recent performance data to weigh their accuracy against, I am inclined towards a cautious approach. The current market is in a ranging/sideways regime which typically reduces the reliability of trend-based models like ORDERFLOW and VOLATILITY that suggest BUY actions without clear directional momentum.",\n'
2025-06-06 14:44:49,862 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 422 chars
2025-06-06 14:44:49,862 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 85.0, "reasoning": "Given the strong disagreement between ORDERFLOW (100% BUY), VOLATILITY (100% BUY), and RSI (100% SELL) signals with no recent performance data to weigh their accuracy against, I am inclined towards a cautious approach. The current market is in a ranging/sideways regime which typically reduces the reliability of trend-based models like ORDERFLOW and VOLATILITY that suggest BUY actions without clear directional momentum.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:44:49,862 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:44:49,862 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 14:44:55,647 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 14:44:55,648 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 14:45:26,403 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.7%
2025-06-06 14:45:26,408 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 14:45:26,530 - utils.system_monitor - WARNING - Memory usage high: 81.7%
2025-06-06 14:45:39,489 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 85,\n  "reasoning": "Given the strong disagreement between ORDERFLOW\'s SELL signal (100% confidence) and both RSI and VOLATILITY models indicating BUY signals at full conviction levels, I am inclined to prioritize risk management in this sideways market. The recent performance pattern shows mixed results with variable outcomes which further complicates the decision-making process.",\n  "risk_assessment": "MEDIUM",\n  "model_consensus": "CONFLICTED",\n  "conviction_score'
2025-06-06 14:45:39,489 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 362 chars
2025-06-06 14:45:39,489 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 85.0, "reasoning": "Given the strong disagreement between ORDERFLOW's SELL signal (100% confidence) and both RSI and VOLATILITY models indicating BUY signals at full conviction levels, I am inclined to prioritize risk management in this sideways market. The recent performance pattern shows mixed results with variable outcomes which further complicates the decision-making process.", "risk_assessment": "MEDIUM", "model_consensus": "CONFLICTED"}
2025-06-06 14:45:39,490 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:45:39,490 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 14:46:26,645 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 82.9%
2025-06-06 14:46:26,650 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 14:46:26,770 - utils.system_monitor - WARNING - Memory usage high: 82.9%
2025-06-06 14:46:29,457 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 65.8,\n  "reasoning": "Given the strong disagreement between VWAP\'s NEAR signal (99.9%) and RSI\'s OVERBOUGHT warning (95.8%), with a slight edge to caution due to recent mixed performance in trading decisions, I have opted for \'WAIT\'. The current market is experiencing low volatility but remains sideways which aligns more closely with the VWAP\'s NEAR signal suggesting near-term stability. However, RSI indicates overbought conditions that could lead to a potential'
2025-06-06 14:46:29,459 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 468 chars
2025-06-06 14:46:29,459 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 65.8, "reasoning": "Given the strong disagreement between VWAP's NEAR signal (99.9%) and RSI's OVERBOUGHT warning (95.8%), with a slight edge to caution due to recent mixed performance in trading decisions, I have opted for 'WAIT'. The current market is experiencing low volatility but remains sideways which aligns more closely with the VWAP's NEAR signal suggesting near-term stability. However, RSI indicates overbought conditions that could lead to a potential... [response truncated]", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:46:29,459 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:46:29,460 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 66%)
2025-06-06 14:46:33,292 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 14:46:33,292 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 14:47:19,046 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 80,\n  "reasoning": "Given the strong disagreement between ORDERFLOW\'s SELL signal (100% confidence) and VOLATILITY\'s BUY signal (100% confidence), I have chosen to prioritize risk management in this volatile market. The current sideways trend with low volume profile suggests a lack of clear direction, which aligns more closely with the WAIT decision from both RSI and VWAP models despite their perfect confidence scores.",\n  "risk_assessment": "MEDIUM",\n  "model'
2025-06-06 14:47:19,046 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 402 chars
2025-06-06 14:47:19,046 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 80.0, "reasoning": "Given the strong disagreement between ORDERFLOW's SELL signal (100% confidence) and VOLATILITY's BUY signal (100% confidence), I have chosen to prioritize risk management in this volatile market. The current sideways trend with low volume profile suggests a lack of clear direction, which aligns more closely with the WAIT decision from both RSI and VWAP models despite their perfect confidence scores.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:47:19,046 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:47:19,046 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 80%)
2025-06-06 14:47:25,783 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 14:47:26,892 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 83.6%
2025-06-06 14:47:26,897 - utils.system_monitor - INFO - Garbage collection freed 282 objects
2025-06-06 14:47:27,015 - utils.system_monitor - WARNING - Memory usage high: 83.6%
2025-06-06 14:48:06,323 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 14:48:11,221 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 75,\n  "reasoning": "Given the strong disagreement between ORDERFLOW\'s SELL signal (100% confidence) and VOLATILITY\'s BUY signal (100% confidence), I have resolved this conflict by prioritizing recent market context. The current sideways price trend, low volatility, strong selling pressure in the volume profile, and ranging regime suggest that immediate action may not be prudent despite conflicting signals from our models.",\n  "risk_assessment": "MEDIUM",\n  "model_cons'
2025-06-06 14:48:11,222 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 405 chars
2025-06-06 14:48:11,222 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 75.0, "reasoning": "Given the strong disagreement between ORDERFLOW's SELL signal (100% confidence) and VOLATILITY's BUY signal (100% confidence), I have resolved this conflict by prioritizing recent market context. The current sideways price trend, low volatility, strong selling pressure in the volume profile, and ranging regime suggest that immediate action may not be prudent despite conflicting signals from our models.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:48:11,222 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:48:11,222 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 75%)
2025-06-06 14:48:27,145 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 82.9%
2025-06-06 14:48:27,151 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 14:48:27,268 - utils.system_monitor - WARNING - Memory usage high: 83.0%
2025-06-06 14:48:30,036 - aiohttp.access - INFO - ::1 [06/Jun/2025:13:48:30 -0600] "GET /api/data HTTP/1.1" 200 10713 "-" "curl/8.12.1"
2025-06-06 14:49:01,020 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 85,\n  "reasoning": "Given the strong disagreement between ORDERFLOW (SELL), RSI (OVERBOUGHT), and VOLATILITY (BUY) models with confidence levels of 100%, I have chosen to prioritize risk management in this volatile market. The current sideways trend, coupled with strong selling pressure indicated by the volume profile, suggests caution despite conflicting signals.",\n  "risk_assessment": "MEDIUM",\n  "model_consensus": "CONFLICTED",\n '
2025-06-06 14:49:01,020 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 346 chars
2025-06-06 14:49:01,022 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 85.0, "reasoning": "Given the strong disagreement between ORDERFLOW (SELL), RSI (OVERBOUGHT), and VOLATILITY (BUY) models with confidence levels of 100%, I have chosen to prioritize risk management in this volatile market. The current sideways trend, coupled with strong selling pressure indicated by the volume profile, suggests caution despite conflicting signals.", "risk_assessment": "MEDIUM", "model_consensus": "CONFLICTED"}
2025-06-06 14:49:01,022 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:49:01,022 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 14:49:27,389 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 86.1%
2025-06-06 14:49:27,394 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 14:49:27,513 - utils.system_monitor - WARNING - Memory usage high: 86.1%
2025-06-06 14:49:50,699 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '```json\n{\n  "final_decision": "WAIT",\n  "confidence": 65.0,\n  "reasoning": "The OrderFlow model suggests a balanced market condition with no clear directional bias; however, the VWAP and Volatility models indicate potential buying opportunities due to low volatility levels (VWAP) near support/resistance zones in conjunction with recent price trends. Given that we are currently experiencing a sideways movement within an overall ranging market regime which historically shows less reliability for trend signals, the decision leans towards waiting rather than acting on potentially unreliable directional cues.",\n  "'
2025-06-06 14:49:50,700 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 535 chars
2025-06-06 14:49:50,700 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 65.0, "reasoning": "The OrderFlow model suggests a balanced market condition with no clear directional bias; however, the VWAP and Volatility models indicate potential buying opportunities due to low volatility levels (VWAP) near support/resistance zones in conjunction with recent price trends. Given that we are currently experiencing a sideways movement within an overall ranging market regime which historically shows less reliability for trend signals, the decision leans towards waiting rather than acting on potentially unreliable directional cues.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:49:50,700 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:49:50,700 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 65%)
2025-06-06 14:50:18,359 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 14:50:18,359 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 14:50:27,649 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 84.6%
2025-06-06 14:50:27,655 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 14:50:27,775 - utils.system_monitor - WARNING - Memory usage high: 84.6%
2025-06-06 14:50:40,235 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 14:50:40,235 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 95%)
2025-06-06 14:51:27,895 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 84.5%
2025-06-06 14:51:27,900 - utils.system_monitor - INFO - Garbage collection freed 176 objects
2025-06-06 14:51:28,015 - utils.system_monitor - WARNING - Memory usage high: 84.6%
2025-06-06 14:51:29,810 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '```json\n{\n  "final_decision": "SHORT",\n  "confidence": 85,\n  "reasoning": "Despite the RSI model\'s strong buy signal indicating oversold conditions with a confidence of 100%, recent market context suggests caution due to sideways price trend and low volatility. The ORDERFLOW model aligns with this cautious approach, recommending \'WAIT\'. Given that models are in agreement on the need for risk mitigation (STRONG consensus), I am leaning towards a short position as it may capitalize on potential market reversal at support levels.",\n  "risk_assessment'
2025-06-06 14:51:29,810 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 457 chars
2025-06-06 14:51:29,810 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "SHORT", "confidence": 85.0, "reasoning": "Despite the RSI model's strong buy signal indicating oversold conditions with a confidence of 100%, recent market context suggests caution due to sideways price trend and low volatility. The ORDERFLOW model aligns with this cautious approach, recommending 'WAIT'. Given that models are in agreement on the need for risk mitigation (STRONG consensus), I am leaning towards a short position as it may capitalize on potential market reversal at support levels.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:51:29,811 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 14:51:29,811 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 85%)
2025-06-06 14:52:19,063 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:52:19,065 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 70%)
2025-06-06 14:52:25,788 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 14:52:28,160 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 82.1%
2025-06-06 14:52:28,173 - utils.system_monitor - INFO - Garbage collection freed 159 objects
2025-06-06 14:52:28,287 - utils.system_monitor - WARNING - Memory usage high: 82.2%
2025-06-06 14:52:44,818 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 14:52:44,818 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 14:52:49,834 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 14:52:51,568 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 14:52:51,569 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 14:53:06,429 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 14:53:11,235 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:53:11,235 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 75%)
2025-06-06 14:53:28,413 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.1%
2025-06-06 14:53:28,419 - utils.system_monitor - INFO - Garbage collection freed 231 objects
2025-06-06 14:53:28,536 - utils.system_monitor - WARNING - Memory usage high: 81.0%
2025-06-06 14:54:00,968 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 85,\n  "reasoning": "Given the strong disagreement between ORDERFLOW (100% BUY), VOLATILITY (100% BUY), and RSI (96% SELL) signals in a sideways market with low volatility but high buying pressure, I have resolved to wait. The recent performance pattern shows mixed results which suggest the current regime\'s trend signals are less reliable due to their 60% reliability during ranging periods as per memory data.",\n  "risk_assessment": "LOW",\n'
2025-06-06 14:54:00,969 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 391 chars
2025-06-06 14:54:00,969 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 85.0, "reasoning": "Given the strong disagreement between ORDERFLOW (100% BUY), VOLATILITY (100% BUY), and RSI (96% SELL) signals in a sideways market with low volatility but high buying pressure, I have resolved to wait. The recent performance pattern shows mixed results which suggest the current regime's trend signals are less reliable due to their 60% reliability during ranging periods as per memory data.", "risk_assessment": "LOW", "model_consensus": "UNKNOWN"}
2025-06-06 14:54:00,969 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:54:00,969 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 14:54:17,914 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 14:54:17,914 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 14:54:28,661 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 82.0%
2025-06-06 14:54:28,668 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 14:54:28,788 - utils.system_monitor - WARNING - Memory usage high: 81.9%
2025-06-06 14:54:50,476 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - SHORT
2025-06-06 14:54:50,477 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - SHORT (confidence: 95%)
2025-06-06 14:55:27,134 - __main__ - INFO - [INIT] Epinnox V6 AI Strategy Tuner initializing...
2025-06-06 14:55:27,134 - __main__ - INFO - [START] Starting Epinnox V6 AI Strategy Tuner...
2025-06-06 14:55:27,140 - __main__ - INFO - [OK] Configuration loaded from config/strategy.yaml
2025-06-06 14:55:27,140 - __main__ - INFO - [SETUP] Initializing components...
2025-06-06 14:55:27,141 - storage.live_store - INFO - State loaded from cache file
2025-06-06 14:55:27,141 - storage.live_store - INFO - [STORE] Live Data Store initialized
2025-06-06 14:55:27,141 - storage.live_store - INFO - Background tasks started
2025-06-06 14:55:27,141 - __main__ - INFO - [OK] Data store initialized
2025-06-06 14:55:27,141 - analytics.signal_tracker - INFO - Signal Tracker initialized (TP: 2.0%, SL: 1.0%)
2025-06-06 14:55:27,141 - analytics.performance_analyzer - INFO - Performance Analyzer initialized
2025-06-06 14:55:27,141 - __main__ - INFO - [OK] Signal tracking and performance analytics initialized
2025-06-06 14:55:27,141 - feeds.trade_parser - INFO - Trade Parser initialized with window size: 500
2025-06-06 14:55:27,141 - __main__ - INFO - [OK] Trade parser initialized
2025-06-06 14:55:27,141 - feeds.htx_ws_client - INFO - HTX WebSocket Client initialized
2025-06-06 14:55:27,141 - feeds.binance_ws_client - INFO - Binance WebSocket Client initialized
2025-06-06 14:55:27,142 - feeds.kraken_ws_client - INFO - Kraken WebSocket Client initialized
2025-06-06 14:55:27,142 - feeds.htx_ws_client - INFO - Trade handler registered
2025-06-06 14:55:27,142 - feeds.htx_ws_client - INFO - Depth handler registered
2025-06-06 14:55:27,142 - feeds.htx_ws_client - INFO - Error handler registered
2025-06-06 14:55:27,142 - feeds.binance_ws_client - INFO - Trade handler registered
2025-06-06 14:55:27,142 - feeds.binance_ws_client - INFO - Depth handler registered
2025-06-06 14:55:27,142 - feeds.binance_ws_client - INFO - Error handler registered
2025-06-06 14:55:27,142 - feeds.kraken_ws_client - INFO - Trade handler registered
2025-06-06 14:55:27,142 - feeds.kraken_ws_client - INFO - Depth handler registered
2025-06-06 14:55:27,142 - feeds.kraken_ws_client - INFO - Error handler registered
2025-06-06 14:55:27,142 - __main__ - INFO - [OK] WebSocket clients initialized (HTX + Binance + Kraken backups)
2025-06-06 14:55:27,142 - models.smart_strategy - INFO - Smart Strategy Engine initialized
2025-06-06 14:55:27,142 - __main__ - INFO - [OK] Strategy engine initialized
2025-06-06 14:55:27,149 - models.llm_integration - INFO - ✅ Loaded dynamic prompt templates from YAML
2025-06-06 14:55:27,150 - models.llm_integration - INFO - LLM Integration initialized (enabled: True, auto-detect: True)
2025-06-06 14:55:27,150 - models.llm_integration - INFO - Loaded 14 prompt templates
2025-06-06 14:55:27,150 - models.llm_integration - INFO - LLM background processing started
2025-06-06 14:55:27,150 - __main__ - INFO - [OK] LLM integration initialized and started
2025-06-06 14:55:27,150 - ui.ai_strategy_tuner - INFO - AI Strategy Tuner Dashboard initialized
2025-06-06 14:55:27,150 - __main__ - INFO - [OK] Dashboard initialized
2025-06-06 14:55:27,150 - utils.system_monitor - INFO - System Monitor initialized
2025-06-06 14:55:27,150 - __main__ - INFO - [OK] System monitor initialized
2025-06-06 14:55:27,150 - __main__ - INFO - [CONNECT] Connecting to HTX WebSocket...
2025-06-06 14:55:27,150 - feeds.htx_ws_client - INFO - Connecting to HTX WebSocket: wss://api-usdt.linear.contract.huobi.pro/ws
2025-06-06 14:55:27,174 - utils.system_monitor - INFO - Starting system monitoring (interval: 60s)
2025-06-06 14:55:27,317 - feeds.htx_ws_client - ERROR - ❌ Failed to connect to HTX WebSocket: [Errno 11001] getaddrinfo failed
2025-06-06 14:55:27,317 - __main__ - ERROR - [WS_ERROR] WebSocket error: [Errno 11001] getaddrinfo failed
2025-06-06 14:55:27,318 - __main__ - WARNING - [WARN] HTX connection failed, trying Binance backup...
2025-06-06 14:55:27,318 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 14:55:29,941 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 14:55:29,942 - __main__ - INFO - [OK] Binance WebSocket connected successfully
2025-06-06 14:55:29,942 - __main__ - INFO - [WEB] Starting dashboard on http://localhost:8086
2025-06-06 14:55:29,942 - ui.ai_strategy_tuner - INFO - 🌐 AI Strategy Tuner Dashboard starting on http://localhost:8086
2025-06-06 14:55:29,943 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 14:55:29,944 - ui.ai_strategy_tuner - INFO - ✅ Dashboard running at http://localhost:8086
2025-06-06 14:55:29,944 - ui.ai_strategy_tuner - INFO - 🎯 Real-time market data and AI analysis
2025-06-06 14:55:29,944 - __main__ - INFO - [OK] Dashboard started successfully
2025-06-06 14:55:29,945 - __main__ - INFO - [SUCCESS] Epinnox V6 AI Strategy Tuner started successfully!
2025-06-06 14:55:29,945 - __main__ - INFO - ============================================================
2025-06-06 14:55:29,945 - __main__ - INFO - [WEB] Dashboard: http://localhost:8086
2025-06-06 14:55:29,945 - __main__ - INFO - [DATA] Market data source: Binance
2025-06-06 14:55:29,945 - __main__ - INFO - [AI] AI model analysis and LLM integration
2025-06-06 14:55:29,945 - __main__ - INFO - [SIGNALS] Live trading signal generation
2025-06-06 14:55:29,945 - __main__ - INFO - ============================================================
2025-06-06 14:55:30,225 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 14:55:30,225 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 100.00%)
2025-06-06 14:55:31,552 - ui.ai_strategy_tuner - INFO - WebSocket client connected (1 total)
2025-06-06 14:55:32,572 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 14:55:37,166 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '```json\n{\n  "final_decision": "WAIT",\n  "confidence": 85,\n  "reasoning": "Given the current market regime of ranging/sideways with strong buying pressure as indicated by ORDERFLOW\'s BUY signal and recent price trend analysis suggesting a bias towards reversal signals at support levels. However, without performance tracking data for these models to inform reliability in this specific context, I am cautious not to act on the high conviction of 100%. The absence of historical decision feedback limits my confidence assessment.",\n  "risk_assessment": "MEDIUM",\n  "model_cons'
2025-06-06 14:55:37,168 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 454 chars
2025-06-06 14:55:37,168 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 85.0, "reasoning": "Given the current market regime of ranging/sideways with strong buying pressure as indicated by ORDERFLOW's BUY signal and recent price trend analysis suggesting a bias towards reversal signals at support levels. However, without performance tracking data for these models to inform reliability in this specific context, I am cautious not to act on the high conviction of 100%. The absence of historical decision feedback limits my confidence assessment.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:55:37,168 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:55:37,168 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 14:56:27,306 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 9.3%, Memory: 78.2%, Signals/min: 1.0
2025-06-06 14:56:27,433 - utils.system_monitor - WARNING - Memory usage high: 78.2%
2025-06-06 14:56:30,829 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 14:56:30,829 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 14:56:35,838 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 14:56:37,502 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 14:56:37,502 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 14:57:01,880 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 75,\n  "reasoning": "Given the strong disagreement between ORDERFLOW\'s SELL signal (100% confidence) and VOLATILITY\'s BUY signal (100% confidence), I am inclined to prioritize caution. The current market is in a sideways trend with low volatility, which typically suggests less reliability for strong signals like those from ORDERFLOW during ranging periods as indicated by the Adaptive Tone memory of 60%. Additionally, recent performance patterns are not available to gauge model accuracy; hence I cannot'
2025-06-06 14:57:01,881 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 509 chars
2025-06-06 14:57:01,882 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 75.0, "reasoning": "Given the strong disagreement between ORDERFLOW's SELL signal (100% confidence) and VOLATILITY's BUY signal (100% confidence), I am inclined to prioritize caution. The current market is in a sideways trend with low volatility, which typically suggests less reliability for strong signals like those from ORDERFLOW during ranging periods as indicated by the Adaptive Tone memory of 60%. Additionally, recent performance patterns are not available to gauge model accuracy; hence I cannot... [response truncated]", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:57:01,882 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:57:01,882 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 75%)
2025-06-06 14:57:27,558 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 9.0%, Memory: 77.3%, Signals/min: 0.5
2025-06-06 14:57:27,565 - utils.system_monitor - INFO - Garbage collection freed 105 objects
2025-06-06 14:57:27,676 - utils.system_monitor - WARNING - Memory usage high: 77.3%
2025-06-06 14:57:51,393 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:57:51,393 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 95%)
2025-06-06 14:58:08,348 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 14:58:08,348 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 14:58:13,353 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 14:58:23,354 - feeds.binance_ws_client - ERROR - ❌ Failed to connect to Binance WebSocket: 
2025-06-06 14:58:23,354 - __main__ - ERROR - [WS_ERROR] WebSocket error: 
2025-06-06 14:58:23,354 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 2/10
2025-06-06 14:58:27,804 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 13.5%, Memory: 77.9%, Signals/min: 0.3
2025-06-06 14:58:27,810 - utils.system_monitor - INFO - Garbage collection freed 144 objects
2025-06-06 14:58:27,927 - utils.system_monitor - WARNING - Memory usage high: 77.9%
2025-06-06 14:58:28,352 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 14:58:32,241 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 14:58:32,241 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 14:58:41,085 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 85,\n  "reasoning": "Given the strong model disagreement between ORDERFLOW (100% BUY), VOLATILITY (100% BUY), and RSI (99% SELL) with a focus on risk management in current sideways market conditions, I have resolved to wait. The recent performance pattern indicates variable results which suggest caution is warranted despite the strong buying pressure indicated by ORDERFLOW and VOLATILITY models.",\n  "risk_assessment": "MEDIUM",\n  "model_cons'
2025-06-06 14:58:41,086 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 377 chars
2025-06-06 14:58:41,086 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 85.0, "reasoning": "Given the strong model disagreement between ORDERFLOW (100% BUY), VOLATILITY (100% BUY), and RSI (99% SELL) with a focus on risk management in current sideways market conditions, I have resolved to wait. The recent performance pattern indicates variable results which suggest caution is warranted despite the strong buying pressure indicated by ORDERFLOW and VOLATILITY models.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:58:41,086 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:58:41,086 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 14:59:22,296 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 14:59:22,297 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 14:59:27,321 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 14:59:28,040 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.2%
2025-06-06 14:59:28,047 - utils.system_monitor - INFO - Garbage collection freed 84 objects
2025-06-06 14:59:28,167 - utils.system_monitor - WARNING - Memory usage high: 81.2%
2025-06-06 14:59:29,930 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 14:59:29,931 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 14:59:30,881 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 75,\n  "reasoning": "Given the strong disagreement between ORDERFLOW (BUY) at 100% confidence, VOLATILITY (BUY) also at 100%, and RSI (SELL) suggesting a sell position with equal certainty, I must prioritize risk management. The current market is in a ranging/sideways regime which historically has shown less reliability for trend signals as per the adaptive tone analysis.",\n  "risk_assessment": "MEDIUM",\n  "model_consensus": "'
2025-06-06 14:59:30,881 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 353 chars
2025-06-06 14:59:30,882 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 75.0, "reasoning": "Given the strong disagreement between ORDERFLOW (BUY) at 100% confidence, VOLATILITY (BUY) also at 100%, and RSI (SELL) suggesting a sell position with equal certainty, I must prioritize risk management. The current market is in a ranging/sideways regime which historically has shown less reliability for trend signals as per the adaptive tone analysis.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 14:59:30,882 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 14:59:30,882 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 75%)
2025-06-06 14:59:35,386 - storage.live_store - INFO - [SIGNAL] Signal stored: BTC-USDT - LONG
2025-06-06 14:59:35,387 - models.smart_strategy - INFO - 🎯 Signal generated: BTC-USDT - LONG (confidence: 79.17%)
2025-06-06 15:00:10,681 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 15:00:10,681 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 15:00:15,693 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 15:00:17,985 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 15:00:17,986 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 15:00:20,352 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 15:00:20,352 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 95%)
2025-06-06 15:00:27,184 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 15:00:28,290 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 82.1%
2025-06-06 15:00:28,296 - utils.system_monitor - INFO - Garbage collection freed 351 objects
2025-06-06 15:00:28,415 - utils.system_monitor - WARNING - Memory usage high: 82.1%
2025-06-06 15:01:07,689 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 15:01:08,026 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 15:01:08,026 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 15:01:12,503 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 75,\n  "reasoning": "Given the strong disagreement between VOLATILITY\'s BUY signal (100% confidence) and ORDERFLOW/RSI\'s SELL signals (both at 100%), I am inclined to prioritize risk management in this sideways market. The recent performance pattern indicates variable results, suggesting caution is warranted despite the VOLATILITY model\'s high confidence level.",\n  "risk_assessment": "MEDIUM",\n  "model_consensus": "CONFLI'
2025-06-06 15:01:12,504 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 342 chars
2025-06-06 15:01:12,504 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 75.0, "reasoning": "Given the strong disagreement between VOLATILITY's BUY signal (100% confidence) and ORDERFLOW/RSI's SELL signals (both at 100%), I am inclined to prioritize risk management in this sideways market. The recent performance pattern indicates variable results, suggesting caution is warranted despite the VOLATILITY model's high confidence level.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 15:01:12,504 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 15:01:12,504 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 75%)
2025-06-06 15:01:13,025 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 15:01:19,261 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 15:01:19,261 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 15:01:28,522 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 82.3%
2025-06-06 15:01:28,529 - utils.system_monitor - INFO - Garbage collection freed 87 objects
2025-06-06 15:01:28,648 - utils.system_monitor - WARNING - Memory usage high: 82.3%
2025-06-06 15:01:59,674 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 15:01:59,674 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 15:02:02,051 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 75,\n  "reasoning": "Given the strong disagreement between ORDERFLOW\'s SELL signal (100% confidence) and VOLATILITY\'s BUY signal (100% confidence), I resolve this conflict by prioritizing recent market context. The current sideways price trend, low volatility, and strong selling pressure suggest a cautious approach is warranted despite the conflicting signals from our models.",\n  "risk_assessment": "MEDIUM",\n  "model_consensus": "CONFLICT'
2025-06-06 15:02:02,051 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 357 chars
2025-06-06 15:02:02,051 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 75.0, "reasoning": "Given the strong disagreement between ORDERFLOW's SELL signal (100% confidence) and VOLATILITY's BUY signal (100% confidence), I resolve this conflict by prioritizing recent market context. The current sideways price trend, low volatility, and strong selling pressure suggest a cautious approach is warranted despite the conflicting signals from our models.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 15:02:02,051 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 15:02:02,052 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 75%)
2025-06-06 15:02:04,684 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 15:02:11,315 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 15:02:11,315 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 15:02:28,774 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.5%
2025-06-06 15:02:28,780 - utils.system_monitor - INFO - Garbage collection freed 238 objects
2025-06-06 15:02:28,899 - utils.system_monitor - WARNING - Memory usage high: 80.5%
2025-06-06 15:02:51,817 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 75,\n  "reasoning": "Given the strong disagreement between ORDERFLOW\'s SELL signal (100% confidence) and VOLATILITY\'s BUY signal (100% confidence), I have resolved this conflict by prioritizing recent market context. The current sideways price trend, coupled with low volatility but strong selling pressure in the volume profile suggests a cautious approach is warranted.",\n  "risk_assessment": "MEDIUM",\n  "model_consensus": "CONFLICTED'
2025-06-06 15:02:51,817 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 350 chars
2025-06-06 15:02:51,817 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 75.0, "reasoning": "Given the strong disagreement between ORDERFLOW's SELL signal (100% confidence) and VOLATILITY's BUY signal (100% confidence), I have resolved this conflict by prioritizing recent market context. The current sideways price trend, coupled with low volatility but strong selling pressure in the volume profile suggests a cautious approach is warranted.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 15:02:51,817 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 15:02:51,818 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 75%)
2025-06-06 15:03:29,011 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.1%
2025-06-06 15:03:29,017 - utils.system_monitor - INFO - Garbage collection freed 128 objects
2025-06-06 15:03:29,147 - utils.system_monitor - WARNING - Memory usage high: 81.1%
2025-06-06 15:03:32,663 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 15:03:32,663 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 15:03:37,679 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 15:03:40,623 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 15:03:40,624 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 15:03:41,646 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 85,\n  "reasoning": "Given the strong disagreement between ORDERFLOW\'s SELL signal (100% confidence) and both RSI and VOLATILITY models indicating a BUY with equal confidence of 100%, I am inclined to prioritize risk management in this sideways market. The recent performance feedback suggests variable results, which further supports the decision to wait for clearer signals or additional confirmation from other reliable sources.",\n  "risk_assessment": "MEDIUM",\n  "model_consensus": "CONFLICT'
2025-06-06 15:03:41,646 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 410 chars
2025-06-06 15:03:41,646 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 85.0, "reasoning": "Given the strong disagreement between ORDERFLOW's SELL signal (100% confidence) and both RSI and VOLATILITY models indicating a BUY with equal confidence of 100%, I am inclined to prioritize risk management in this sideways market. The recent performance feedback suggests variable results, which further supports the decision to wait for clearer signals or additional confirmation from other reliable sources.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 15:03:41,647 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 15:03:41,647 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 15:04:29,278 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 85.3%
2025-06-06 15:04:29,286 - utils.system_monitor - INFO - Garbage collection freed 149 objects
2025-06-06 15:04:29,404 - utils.system_monitor - WARNING - Memory usage high: 85.3%
2025-06-06 15:04:30,684 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 15:04:30,684 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 15:04:31,386 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 85,\n  "reasoning": "Given the strong disagreement between ORDERFLOW (SELL) at a confidence of 100%, RSI\'s SELL signal with 97% confidence, and VOLATILITY\'s BUY recommendation also at full conviction, I am inclined to prioritize caution. The current market is in a ranging/sideways regime where trend signals are less reliable (Adaptive Tone). Although the ORDERFLOW model has not provided reasoning and RSI indicates an overbought condition with no specifics on why'
2025-06-06 15:04:31,387 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 469 chars
2025-06-06 15:04:31,387 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 85.0, "reasoning": "Given the strong disagreement between ORDERFLOW (SELL) at a confidence of 100%, RSI's SELL signal with 97% confidence, and VOLATILITY's BUY recommendation also at full conviction, I am inclined to prioritize caution. The current market is in a ranging/sideways regime where trend signals are less reliable (Adaptive Tone). Although the ORDERFLOW model has not provided reasoning and RSI indicates an overbought condition with no specifics on why... [response truncated]", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 15:04:31,388 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 15:04:31,388 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 15:04:35,693 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 15:04:39,507 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 15:04:39,508 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 15:05:19,928 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 15:05:19,929 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 15:05:21,173 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 75,\n  "reasoning": "Given the strong disagreement between VOLATILITY\'s BUY signal (100% confidence) and ORDERFLOW/RSI\'s SELL signals (both at 100%), I am inclined to prioritize risk management in this sideways market. The recent performance pattern indicates variable results, suggesting that the current models may not be reliable indicators of future price movements under these conditions.",\n  "risk_assessment": "MEDIUM",\n  "model_consensus": "CONFLICTED'
2025-06-06 15:05:21,173 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 372 chars
2025-06-06 15:05:21,173 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 75.0, "reasoning": "Given the strong disagreement between VOLATILITY's BUY signal (100% confidence) and ORDERFLOW/RSI's SELL signals (both at 100%), I am inclined to prioritize risk management in this sideways market. The recent performance pattern indicates variable results, suggesting that the current models may not be reliable indicators of future price movements under these conditions.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 15:05:21,174 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 15:05:21,174 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 75%)
2025-06-06 15:05:24,927 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 15:05:27,188 - storage.live_store - INFO - Old data cleanup completed
2025-06-06 15:05:29,530 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 80.9%
2025-06-06 15:05:29,538 - utils.system_monitor - INFO - Garbage collection freed 246 objects
2025-06-06 15:05:29,657 - utils.system_monitor - WARNING - Memory usage high: 80.9%
2025-06-06 15:05:36,102 - feeds.binance_ws_client - ERROR - ❌ Failed to connect to Binance WebSocket: 
2025-06-06 15:05:36,102 - __main__ - ERROR - [WS_ERROR] WebSocket error: 
2025-06-06 15:05:36,102 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 2/10
2025-06-06 15:05:41,102 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 15:05:43,310 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 15:05:43,310 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 15:06:08,577 - models.llm_integration - INFO - [LLM] Server health check: AVAILABLE
2025-06-06 15:06:13,400 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": <70>, // Lowered due to conflicting signals; not at full confidence level because of the ranging market regime\'s impact on signal reliability, which is currently less reliable.\n  "reasoning": "Given that ORDERFLOW and RSI both suggest a SELL action with high conviction (100%), but VOLATILITY indicates BUY at full confidence level, the conflicting signals necessitate caution in decision-making. The current market is experiencing sideways movement within a ranging regime where trend indicators are less reliable as per recent performance patterns and adaptive guidance'
2025-06-06 15:06:13,400 - models.llm_integration - ERROR - Enhanced extraction also failed
2025-06-06 15:06:13,400 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - LONG
2025-06-06 15:06:13,400 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - LONG (confidence: 50%)
2025-06-06 15:06:24,439 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 15:06:24,439 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 15:06:29,462 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 15:06:29,792 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.7%
2025-06-06 15:06:29,798 - utils.system_monitor - INFO - Garbage collection freed 323 objects
2025-06-06 15:06:29,917 - utils.system_monitor - WARNING - Memory usage high: 81.6%
2025-06-06 15:06:39,466 - feeds.binance_ws_client - ERROR - ❌ Failed to connect to Binance WebSocket: 
2025-06-06 15:06:39,466 - __main__ - ERROR - [WS_ERROR] WebSocket error: 
2025-06-06 15:06:39,466 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 2/10
2025-06-06 15:06:44,490 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 15:06:47,012 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 15:06:47,012 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 15:07:03,139 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 75,\n  "reasoning": "Given the strong disagreement between ORDERFLOW\'s SELL signal (100% confidence) and both RSI and VOLATILITY models indicating a BUY with equal confidence of 100%, I am inclined to prioritize risk management in this sideways market. The recent performance pattern shows mixed results, suggesting that the current signals may not be reliable predictors for immediate action.",\n  "risk_assessment": "MEDIUM",\n  "model_consensus": "CONFLICTED",\n '
2025-06-06 15:07:03,140 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 372 chars
2025-06-06 15:07:03,140 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 75.0, "reasoning": "Given the strong disagreement between ORDERFLOW's SELL signal (100% confidence) and both RSI and VOLATILITY models indicating a BUY with equal confidence of 100%, I am inclined to prioritize risk management in this sideways market. The recent performance pattern shows mixed results, suggesting that the current signals may not be reliable predictors for immediate action.", "risk_assessment": "MEDIUM", "model_consensus": "CONFLICTED"}
2025-06-06 15:07:03,140 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 15:07:03,140 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 75%)
2025-06-06 15:07:27,408 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 15:07:27,408 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 15:07:30,022 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 83.3%
2025-06-06 15:07:30,030 - utils.system_monitor - INFO - Garbage collection freed 256 objects
2025-06-06 15:07:30,146 - utils.system_monitor - WARNING - Memory usage high: 83.3%
2025-06-06 15:07:32,412 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 15:07:35,016 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 15:07:35,016 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 15:07:52,924 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 75.0,\n  "reasoning": "Given the strong disagreement between VWAP\'s NEAR signal (100% confidence) and RSI\'s OVERBOUGHT warning (96%), along with a sideways market trend in a ranging regime that typically reduces reliability of trend signals, I am inclined to prioritize risk management. The VWAP model has historically been more reliable than the oscillators like RSI during such conditions; however, without recent performance data for these models and considering mixed results from past decisions in similar situations (0'
2025-06-06 15:07:52,924 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 525 chars
2025-06-06 15:07:52,924 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 75.0, "reasoning": "Given the strong disagreement between VWAP's NEAR signal (100% confidence) and RSI's OVERBOUGHT warning (96%), along with a sideways market trend in a ranging regime that typically reduces reliability of trend signals, I am inclined to prioritize risk management. The VWAP model has historically been more reliable than the oscillators like RSI during such conditions; however, without recent performance data for these models and considering mixed results from past decisions in similar situations (0... [response truncated]", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 15:07:52,924 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 15:07:52,924 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 75%)
2025-06-06 15:08:25,049 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 15:08:25,049 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 15:08:30,041 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 15:08:30,268 - utils.system_monitor - WARNING - System health: WARNING - Warnings: High memory usage: 81.1%
2025-06-06 15:08:30,275 - utils.system_monitor - INFO - Garbage collection freed 239 objects
2025-06-06 15:08:30,387 - utils.system_monitor - WARNING - Memory usage high: 81.2%
2025-06-06 15:08:37,083 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 15:08:37,085 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 15:08:42,695 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 85.0,\n  "reasoning": "Given the strong disagreement between VWAP\'s NEAR signal (96.8%) and RSI\'s OVERBOUGHT warning (100%), along with a sideways market trend in a ranging regime where recent performance patterns show mixed results, I am opting for caution by choosing to WAIT. The VWAP model has historically been reliable but is currently conflicting with the RSI\'s strong sell signal amidst low volatility and buying pressure.",\n  "risk_assess'
2025-06-06 15:08:42,695 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 407 chars
2025-06-06 15:08:42,695 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 85.0, "reasoning": "Given the strong disagreement between VWAP's NEAR signal (96.8%) and RSI's OVERBOUGHT warning (100%), along with a sideways market trend in a ranging regime where recent performance patterns show mixed results, I am opting for caution by choosing to WAIT. The VWAP model has historically been reliable but is currently conflicting with the RSI's strong sell signal amidst low volatility and buying pressure.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 15:08:42,695 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 15:08:42,695 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 15:09:14,548 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 15:09:14,549 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 15:09:19,573 - feeds.binance_ws_client - INFO - Connecting to Binance WebSocket: wss://stream.binance.com:9443/ws
2025-06-06 15:09:23,773 - feeds.binance_ws_client - INFO - ✅ Connected to Binance WebSocket successfully
2025-06-06 15:09:23,773 - feeds.binance_ws_client - INFO - 🎧 Starting Binance WebSocket message listener
2025-06-06 15:09:30,511 - utils.system_monitor - INFO - System health: HEALTHY - CPU: 14.1%, Memory: 77.1%, Signals/min: 0.1
2025-06-06 15:09:30,518 - utils.system_monitor - INFO - Garbage collection freed 144 objects
2025-06-06 15:09:30,636 - utils.system_monitor - WARNING - Memory usage high: 77.1%
2025-06-06 15:09:32,509 - models.llm_integration - ERROR - No valid JSON found in LLM response. Full content: '{\n  "final_decision": "WAIT",\n  "confidence": 85,\n  "reasoning": "Given the strong disagreement between ORDERFLOW\'s SELL signal (100% confidence) and both RSI and VOLATILITY models indicating a BUY with their respective high confidences of 99.1% and 100%, I am inclined to prioritize caution in this volatile market context, especially considering the sideways price trend which has historically reduced reliability for these signals as per our adaptive tone guidelines.",\n  "risk_assessment": "MEDIUM",'
2025-06-06 15:09:32,510 - models.llm_integration - INFO - Manual extraction successful with preserved reasoning: 404 chars
2025-06-06 15:09:32,510 - models.llm_integration - INFO - Enhanced extraction succeeded: {"final_decision": "WAIT", "confidence": 85.0, "reasoning": "Given the strong disagreement between ORDERFLOW's SELL signal (100% confidence) and both RSI and VOLATILITY models indicating a BUY with their respective high confidences of 99.1% and 100%, I am inclined to prioritize caution in this volatile market context, especially considering the sideways price trend which has historically reduced reliability for these signals as per our adaptive tone guidelines.", "risk_assessment": "MEDIUM", "model_consensus": "UNKNOWN"}
2025-06-06 15:09:32,510 - storage.live_store - INFO - 🧠 LLM decision stored: BTC-USDT - WAIT
2025-06-06 15:09:32,510 - models.llm_integration - INFO - 🧠 LLM decision: BTC-USDT - WAIT (confidence: 85%)
2025-06-06 15:09:42,054 - __main__ - INFO - [SIGNAL] Received signal 2
2025-06-06 15:09:42,469 - __main__ - INFO - [STOP] Stopping Epinnox V6 AI Strategy Tuner...
2025-06-06 15:09:42,469 - feeds.binance_ws_client - INFO - 🔌 Disconnecting from Binance WebSocket
2025-06-06 15:10:02,869 - storage.live_store - INFO - Live Data Store shutdown complete
2025-06-06 15:10:02,869 - __main__ - INFO - [OK] Application stopped gracefully
2025-06-06 15:10:02,869 - __main__ - INFO - [COMPLETE] Epinnox V6 AI Strategy Tuner completed successfully
2025-06-06 15:10:02,869 - feeds.binance_ws_client - WARNING - Binance WebSocket connection closed
2025-06-06 15:10:02,869 - feeds.binance_ws_client - WARNING - 🔄 Attempting Binance reconnection 1/10
2025-06-06 15:10:02,870 - ui.ai_strategy_tuner - INFO - WebSocket client disconnected (0 total)
