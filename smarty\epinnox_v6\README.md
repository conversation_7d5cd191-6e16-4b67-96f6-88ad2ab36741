# 🧠 Epinnox V6 - AI Strategy Tuner

**Production-Ready Standalone AI Trading Analysis Platform**

Epinnox V6 is a complete refactor of the AI Strategy Tuner, designed as a standalone application that connects directly to HTX Futures WebSocket feeds for real-time market analysis. This eliminates all dependencies on the SQLite bus system and orchestrator, creating a clean, production-ready architecture.

## 🎯 **Core Features**

### **Real-Time Market Data**
- ✅ **HTX Futures WebSocket Integration**: Direct connection to live market feeds
- ✅ **Multi-Symbol Support**: BTC-USDT, ETH-USDT, DOGE-USDT, SOL-USDT, ADA-USDT
- ✅ **Trade & Order Book Processing**: Real-time trade flow and depth analysis
- ✅ **Feature Extraction**: Volume delta, order flow imbalance, price velocity

### **AI Model Analysis**
- ✅ **Smart Strategy Engine**: Ensemble of RSI, VWAP, Orderflow, and Volatility models
- ✅ **Real-Time Signal Generation**: Trading signals with confidence scores
- ✅ **Model Performance Tracking**: Individual model accuracy and contribution analysis
- ✅ **Parameter Hot-Reload**: Dynamic configuration updates without restart

### **LLM Integration**
- ✅ **Phi-3.1 Model Support**: Local LLM integration via LMStudio API
- ✅ **Decision Synthesis**: AI model output analysis and final trading recommendations
- ✅ **Reasoning Transparency**: Detailed explanation of LLM decision process
- ✅ **Rate Limiting**: Intelligent LLM call management

### **Enhanced Dashboard**
- ✅ **Real-Time Updates**: WebSocket-powered live data streaming
- ✅ **Professional UI**: Dark-themed fintech design with responsive layout
- ✅ **Symbol Switching**: Instant symbol selection with per-symbol analytics
- ✅ **Parameter Presets**: Conservative, Balanced, and Aggressive configurations
- ✅ **Performance Analytics**: Win rates, P&L tracking, signal timeline

## 🚀 **Quick Start**

### **1. Installation**
```bash
cd smarty/epinnox_v6
pip install -r requirements.txt
```

### **2. Configuration**
Edit `config/strategy.yaml` to customize:
- Trading symbols and parameters
- Model weights and thresholds
- LLM integration settings
- Dashboard preferences

### **3. Start Application**
```bash
python run_epinnox_v6.py
```

### **4. Access Dashboard**
Open your browser to: **http://localhost:8086**

## 📁 **Architecture Overview**

```
epinnox_v6/
├── run_epinnox_v6.py          # Main application entry point
├── config/
│   └── strategy.yaml          # Configuration file
├── feeds/
│   ├── htx_ws_client.py      # HTX WebSocket client
│   └── trade_parser.py       # Real-time feature extraction
├── models/
│   ├── smart_strategy.py     # AI strategy engine
│   └── llm_integration.py    # LLM decision synthesis
├── storage/
│   └── live_store.py         # In-memory data storage
├── ui/
│   └── ai_strategy_tuner.py  # Web dashboard interface
└── utils/
    ├── symbols.py            # Symbol management
    └── timer.py              # Background task scheduler
```

## 🔧 **Configuration**

### **Trading Symbols**
```yaml
symbols:
  enabled:
    - "BTC-USDT"
    - "ETH-USDT"
    - "DOGE-USDT"
    - "SOL-USDT"
    - "ADA-USDT"
  default: "BTC-USDT"
```

### **Model Weights**
```yaml
models:
  weights:
    rsi: 1.2
    vwap: 1.0
    orderflow: 1.5
    sentiment: 0.8
    volatility: 1.1
```

### **LLM Integration**
```yaml
llm:
  enabled: true
  api_url: "http://localhost:1234/v1/chat/completions"
  model_path: "path/to/phi-3.1-model.gguf"
  temperature: 0.1
  max_tokens: 150
```

## 🎛️ **Dashboard Features**

### **AI Model Outputs Panel**
- Real-time model predictions and confidence scores
- Visual confidence bars and action indicators
- Model-specific signal strength display

### **Trading Signals Panel**
- Recent signals with timestamps and confidence
- Signal timeline with P&L tracking
- Action classification (LONG/SHORT/WAIT)

### **LLM Decisions Panel**
- LLM reasoning and decision process
- Confidence scores and risk assessment
- Expandable reasoning text

### **Performance Analytics Panel**
- Win rates and profitability metrics
- Per-symbol performance breakdown
- Model contribution analysis

## ⚡ **Performance Specifications**

- **Signal Generation Latency**: <100ms target
- **WebSocket Processing**: <50ms per message
- **Memory Usage**: <512MB typical
- **Update Frequency**: 2-second dashboard refresh
- **Data Retention**: 500 trades per symbol in memory

## 🔒 **Security & Risk Management**

### **Built-in Safeguards**
- ✅ **Testnet Mode**: Safe testing environment
- ✅ **Rate Limiting**: API call frequency control
- ✅ **Circuit Breakers**: Automatic stop on excessive losses
- ✅ **Position Limits**: Maximum position size controls
- ✅ **Signal Cooldown**: Prevents over-trading

### **Risk Parameters**
```yaml
risk:
  max_position_size: 100      # USD
  stop_loss_percent: 2.0
  take_profit_percent: 4.0
  max_daily_trades: 50
```

## 🎯 **Key Advantages Over Previous Versions**

### **✅ Standalone Architecture**
- No SQLite bus dependencies
- No orchestrator complexity
- Self-contained application
- Simplified deployment

### **✅ Real-Time Performance**
- Direct WebSocket connections
- In-memory data processing
- <100ms signal generation
- Optimized for low latency

### **✅ Production Ready**
- Comprehensive error handling
- Graceful shutdown procedures
- Background task management
- Performance monitoring

### **✅ Enhanced User Experience**
- Professional fintech UI
- Real-time data visualization
- Intuitive parameter controls
- Mobile-responsive design

## 🛠️ **Development & Customization**

### **Adding New Models**
1. Create model class in `models/`
2. Implement `analyze()` method
3. Register in `SmartStrategy`
4. Update configuration

### **Custom Indicators**
1. Add calculation to `TradeParser`
2. Include in `MarketFeatures`
3. Use in model analysis
4. Display in dashboard

### **LLM Customization**
1. Modify prompt templates in config
2. Adjust model parameters
3. Implement custom decision logic
4. Add new reasoning categories

## 📊 **Monitoring & Logging**

### **Log Files**
- `logs/epinnox_v6.log`: Main application log
- `logs/real_signals.jsonl`: Trading signals (optional)
- `logs/errors.log`: Error tracking

### **Performance Metrics**
- WebSocket connection status
- Signal generation statistics
- Model accuracy tracking
- LLM response times

## 🎉 **Success Metrics**

Epinnox V6 represents a **complete transformation** from simulation to production-ready trading analysis:

- ✅ **Real Market Data**: Live HTX Futures feeds
- ✅ **AI-Powered Analysis**: Multi-model ensemble with LLM synthesis
- ✅ **Professional Interface**: Grade A+ fintech dashboard
- ✅ **Production Architecture**: Scalable, maintainable, reliable

## 🚀 **Getting Started**

Ready to experience real-time AI trading analysis? 

```bash
cd smarty/epinnox_v6
python run_epinnox_v6.py
```

Open **http://localhost:8086** and watch live market data flow through AI models in real-time!

---

**Epinnox V6 - Where AI Meets Real Markets** 🧠📈
