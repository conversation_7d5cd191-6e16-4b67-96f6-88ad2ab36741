# Epinnox V6 - AI Strategy Tuner Configuration
# Production-ready standalone configuration

# Trading symbols configuration - LIVE TRADING SETUP
symbols:
  enabled:
    - "DOGE-USDT"  # Primary live trading symbol
    - "BTC-USDT"
    - "ETH-USDT"
    - "SOL-USDT"
    - "ADA-USDT"
  default: "DOGE-USDT"  # Live trading with DOGE/USDT:USDT

# HTX API Configuration - LIVE TRADING
exchange:
  name: "htx"
  testnet: false
  live_trading: true

  # API Credentials (set in .env file)
  api_key: "${HTX_API_KEY}"
  api_secret: "${HTX_API_SECRET}"
  passphrase: "${HTX_PASSPHRASE}"  # If required

  # HTX USDT-M Futures API Endpoints
  rest_url: "https://api-usdt.linear.contract.huobi.pro"
  ws_url: "wss://api-usdt.linear.contract.huobi.pro/ws"
  ws_private_url: "wss://api-usdt.linear.contract.huobi.pro/notification"

  # Account Configuration
  account_balance: 5.0  # $5 USD live account
  max_position_size: 4.0  # Max $4 position (80% of account)
  emergency_stop_loss: 2.5  # Emergency stop at 50% account loss ($2.50)

  # Trading Pair Configuration
  trading_pair: "DOGE-USDT"  # HTX format
  contract_type: "swap"  # Perpetual futures
  margin_mode: "isolated"  # Isolated margin mode

# HTX Futures WebSocket configuration
htx_websocket:
  url: "wss://api-usdt.linear.contract.huobi.pro/ws"
  channels:
    - "trade.detail"
    - "depth.step0"
  reconnect_interval: 5
  max_reconnect_attempts: 10
  ping_interval: 20

# Real-time data storage configuration
data_storage:
  trade_window_size: 500        # Number of recent trades to keep per symbol
  feature_update_interval: 1    # Seconds between feature calculations
  memory_cleanup_interval: 300  # Seconds between memory cleanup
  persist_state: true          # Save state to symbol_cache.json

# AI Model configuration
models:
  # Model weights for ensemble decision making
  weights:
    rsi: 1.2
    vwap: 1.0
    orderflow: 1.5
    sentiment: 0.8
    volatility: 1.1

  # RSI Model parameters
  rsi:
    period: 14
    overbought: 70
    oversold: 30

  # VWAP Model parameters
  vwap:
    period: 20
    deviation_threshold: 0.02

  # Orderflow Model parameters
  orderflow:
    imbalance_threshold: 0.3
    volume_window: 50

  # Sentiment Model parameters (placeholder for future implementation)
  sentiment:
    enabled: false
    source: "twitter"

  # Volatility Model parameters
  volatility:
    period: 20
    threshold_high: 0.05
    threshold_low: 0.01

# Trading signal generation
signals:
  confidence_threshold: 0.6     # Minimum confidence for signal generation
  signal_cooldown: 30          # Seconds between signals for same symbol
  max_signals_per_hour: 20     # Rate limiting

  # Signal strength thresholds
  thresholds:
    strong_buy: 0.8
    buy: 0.6
    neutral: 0.4
    sell: -0.6
    strong_sell: -0.8

# LLM Integration configuration
llm:
  enabled: true               # Set to true to enable LLM integration
  auto_detect: true           # Automatically detect if LLM server is available
  model_path: "C:\\Users\\<USER>\\.lmstudio\\models\\lmstudio-community\\Phi-3.1-mini-128k-instruct-GGUF\\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf"
  api_url: "http://localhost:1234/v1/chat/completions"  # LMStudio API
  max_tokens: 150
  temperature: 0.1
  call_interval: 45            # Seconds between LLM calls
  timeout: 10                  # Request timeout in seconds

  # LLM prompt configuration
  prompt:
    system: |
      You are an expert cryptocurrency trading analyst. Analyze the provided market data and AI model outputs to make trading decisions.

      Respond with a JSON object containing:
      - action: "LONG", "SHORT", or "WAIT"
      - confidence: float between 0.0 and 1.0
      - reasoning: brief explanation (max 100 words)
      - risk_level: "LOW", "MEDIUM", or "HIGH"

    context_window: 5           # Number of recent signals to include in context

# Web dashboard configuration
dashboard:
  host: "localhost"
  port: 8086
  update_interval: 2           # Seconds between WebSocket updates
  cors_enabled: true

  # UI preferences
  ui:
    theme: "dark"
    compact_mode: true
    show_debug_info: false
    auto_scroll_signals: true

# Logging configuration
logging:
  level: "INFO"
  file_logging: true
  console_logging: true
  signal_logging: true         # Log signals to real_signals.jsonl

  # Log file configuration
  files:
    main_log: "logs/epinnox_v6.log"
    signal_log: "logs/real_signals.jsonl"
    error_log: "logs/errors.log"

  # Log rotation
  rotation:
    max_size_mb: 50
    backup_count: 5

# Performance monitoring
performance:
  enable_metrics: true
  latency_targets:
    signal_generation: 100     # milliseconds
    websocket_processing: 50   # milliseconds
    llm_response: 5000        # milliseconds

  # Memory management
  memory:
    max_memory_mb: 512
    cleanup_threshold: 0.8    # Trigger cleanup at 80% memory usage

# Risk management - LIVE TRADING $5 ACCOUNT
risk:
  max_position_size: 4.0      # USD - 80% of $5 account
  stop_loss_percent: 5.0      # 5% stop loss for small account
  take_profit_percent: 10.0   # 10% take profit for small account
  max_daily_trades: 10        # Conservative for $5 account
  min_position_size: 0.50     # Minimum $0.50 position

  # Circuit breaker - CRITICAL for live trading
  circuit_breaker:
    enabled: true
    loss_threshold: -2.5      # USD daily loss limit (50% of account)
    consecutive_losses: 3     # Stop after 3 consecutive losses
    max_drawdown_percent: 50  # Stop at 50% account drawdown

# Development and testing - LIVE TRADING MODE
development:
  debug_mode: true            # Enable debug for live trading monitoring
  simulate_trades: false      # LIVE TRADING - Execute real trades
  mock_llm: false            # Use real LLM instead of mock responses
  verbose_logging: true      # Detailed logging for live trading

  # Testing configuration
  testing:
    enable_test_data: false
    test_symbol: "DOGE-USDT"  # Live trading symbol
    test_duration_minutes: 60

# Phase 6: Execution Intelligence Configuration
execution_intelligence:
  auto_execute: true
  reflection_enabled: true
  performance_tracking: true

execution:
  mode: "live_trading"  # LIVE TRADING MODE
  max_position_size: 4.0      # $4 max position
  base_position_size: 1.0     # $1 base position
  min_position_size: 0.5      # $0.50 minimum position
  confidence_multiplier: 1.5  # Conservative for live trading
  conviction_multiplier: 1.2  # Conservative for live trading
  regime_multipliers:
    trending_up: 1.1          # Conservative multipliers
    trending_down: 1.1
    ranging: 0.9
    high_volatility: 0.7
    unknown: 0.6

adaptive_orders:
  low_volatility_threshold: 0.005
  high_volatility_threshold: 0.02
  low_volume_threshold: 0.5
  high_volume_threshold: 2.0
  strong_momentum_threshold: 0.01
  weak_momentum_threshold: 0.002
  large_order_threshold: 500.0
  iceberg_threshold: 1000.0

execution_memory:
  data_dir: "data/execution"
  max_history: 10000

post_trade_reflection:
  reflection_threshold: 0.1
  batch_reflection_size: 5
  reflection_frequency: 3600

# Phase 7: Autonomy Loop Configuration
autonomy_loop:
  enabled: true
  analysis_interval: 600  # 10 minutes
  review_interval: 21600  # 6 hours
  tuning_interval: 86400  # 24 hours
  trade_review_threshold: 50
  auto_tuning_enabled: true

autonomy:
  data_dir: "data/autonomy"
  max_snapshots: 1000
  analysis_window_hours: 24
  critical_threshold: 0.3
  min_win_rate: 0.45
  max_drawdown: 0.15
  confidence_tolerance: 0.1

model_tuning:
  data_dir: "data/autonomy"
  max_weight_change: 0.1
  max_threshold_change: 0.05
  min_weight: 0.05
  max_weight: 0.5
  tuning_cooldown: 3600

final_decision_enhancer:
  confidence_threshold_high: 0.8
  confidence_threshold_low: 0.5
  consensus_threshold: 0.7
  conflict_threshold: 0.3
  max_history: 100
  risk_factors:
    volatility_weight: 0.3
    confidence_weight: 0.3
    consensus_weight: 0.2
    regime_weight: 0.2

# PHASE 10: Autonomous Trading Configuration
autonomous_trading:
  enabled: false  # Start disabled for safety
  max_daily_trades: 20
  max_concurrent_positions: 3
  daily_profit_target: 0.05  # 5% daily profit target
  max_drawdown: 0.1  # 10% maximum drawdown before emergency stop

  # Market intelligence configuration
  market_intelligence:
    data_dir: "data/market_intelligence"
    analysis_interval: 30  # seconds between market analysis
    confidence_threshold: 0.7

    # Liquidity analysis
    liquidity_thresholds:
      very_high: 100000
      high: 50000
      medium: 10000
      low: 1000
      very_low: 0

    # Position sizing intelligence
    position_sizing:
      base_risk_pct: 0.03  # 3% base risk per trade
      max_risk_pct: 0.05   # 5% maximum risk per trade
      stealth_threshold: 2.0  # Multiplier for stealth execution
      chunk_delay_seconds: 30  # Delay between order chunks

    # Profit optimization
    profit_targets:
      base_take_profit: 0.02  # 2% base take profit
      base_stop_loss: 0.01    # 1% base stop loss
      volatility_adjustment: true
      regime_adjustment: true
      liquidity_adjustment: true

# Feature flags
features:
  enable_advanced_analytics: true
  enable_social_sentiment: false
  enable_news_analysis: false
  enable_technical_indicators: true
  enable_machine_learning: false  # For future ML model integration
  enable_execution_intelligence: true  # Phase 6 execution intelligence
  enable_autonomy_loop: true  # Phase 7 autonomy loop
  enable_autonomous_trading: true  # PHASE 10: Full autonomous trading
