# Epinnox V6 - AI Strategy Tuner Configuration
# Production-ready standalone configuration

# Trading symbols configuration
symbols:
  enabled:
    - "BTC-USDT"
    - "ETH-USDT" 
    - "DOGE-USDT"
    - "SOL-USDT"
    - "ADA-USDT"
  default: "BTC-USDT"

# HTX Futures WebSocket configuration
htx_websocket:
  url: "wss://api-usdt.linear.contract.huobi.pro/ws"
  channels:
    - "trade.detail"
    - "depth.step0"
  reconnect_interval: 5
  max_reconnect_attempts: 10
  ping_interval: 20

# Real-time data storage configuration
data_storage:
  trade_window_size: 500        # Number of recent trades to keep per symbol
  feature_update_interval: 1    # Seconds between feature calculations
  memory_cleanup_interval: 300  # Seconds between memory cleanup
  persist_state: true          # Save state to symbol_cache.json

# AI Model configuration
models:
  # Model weights for ensemble decision making
  weights:
    rsi: 1.2
    vwap: 1.0
    orderflow: 1.5
    sentiment: 0.8
    volatility: 1.1
  
  # RSI Model parameters
  rsi:
    period: 14
    overbought: 70
    oversold: 30
    
  # VWAP Model parameters  
  vwap:
    period: 20
    deviation_threshold: 0.02
    
  # Orderflow Model parameters
  orderflow:
    imbalance_threshold: 0.3
    volume_window: 50
    
  # Sentiment Model parameters (placeholder for future implementation)
  sentiment:
    enabled: false
    source: "twitter"
    
  # Volatility Model parameters
  volatility:
    period: 20
    threshold_high: 0.05
    threshold_low: 0.01

# Trading signal generation
signals:
  confidence_threshold: 0.6     # Minimum confidence for signal generation
  signal_cooldown: 30          # Seconds between signals for same symbol
  max_signals_per_hour: 20     # Rate limiting
  
  # Signal strength thresholds
  thresholds:
    strong_buy: 0.8
    buy: 0.6
    neutral: 0.4
    sell: -0.6
    strong_sell: -0.8

# LLM Integration configuration
llm:
  enabled: true
  model_path: "C:\\Users\\<USER>\\.lmstudio\\models\\lmstudio-community\\Phi-3.1-mini-128k-instruct-GGUF\\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf"
  api_url: "http://localhost:1234/v1/chat/completions"  # LMStudio API
  max_tokens: 150
  temperature: 0.1
  call_interval: 45            # Seconds between LLM calls
  timeout: 10                  # Request timeout in seconds
  
  # LLM prompt configuration
  prompt:
    system: |
      You are an expert cryptocurrency trading analyst. Analyze the provided market data and AI model outputs to make trading decisions.
      
      Respond with a JSON object containing:
      - action: "LONG", "SHORT", or "WAIT"
      - confidence: float between 0.0 and 1.0
      - reasoning: brief explanation (max 100 words)
      - risk_level: "LOW", "MEDIUM", or "HIGH"
    
    context_window: 5           # Number of recent signals to include in context

# Web dashboard configuration
dashboard:
  host: "localhost"
  port: 8086
  update_interval: 2           # Seconds between WebSocket updates
  cors_enabled: true
  
  # UI preferences
  ui:
    theme: "dark"
    compact_mode: true
    show_debug_info: false
    auto_scroll_signals: true

# Logging configuration
logging:
  level: "INFO"
  file_logging: true
  console_logging: true
  signal_logging: true         # Log signals to real_signals.jsonl
  
  # Log file configuration
  files:
    main_log: "logs/epinnox_v6.log"
    signal_log: "logs/real_signals.jsonl"
    error_log: "logs/errors.log"
  
  # Log rotation
  rotation:
    max_size_mb: 50
    backup_count: 5

# Performance monitoring
performance:
  enable_metrics: true
  latency_targets:
    signal_generation: 100     # milliseconds
    websocket_processing: 50   # milliseconds
    llm_response: 5000        # milliseconds
  
  # Memory management
  memory:
    max_memory_mb: 512
    cleanup_threshold: 0.8    # Trigger cleanup at 80% memory usage

# Risk management
risk:
  max_position_size: 100      # USD
  stop_loss_percent: 2.0
  take_profit_percent: 4.0
  max_daily_trades: 50
  
  # Circuit breaker
  circuit_breaker:
    enabled: true
    loss_threshold: -50       # USD daily loss limit
    consecutive_losses: 5     # Stop after N consecutive losses

# Development and testing
development:
  debug_mode: false
  simulate_trades: true       # Don't execute real trades
  mock_llm: false            # Use real LLM instead of mock responses
  verbose_logging: false
  
  # Testing configuration
  testing:
    enable_test_data: false
    test_symbol: "BTC-USDT"
    test_duration_minutes: 60

# Feature flags
features:
  enable_advanced_analytics: true
  enable_social_sentiment: false
  enable_news_analysis: false
  enable_technical_indicators: true
  enable_machine_learning: false  # For future ML model integration
