#!/usr/bin/env python3
"""
Multi-Timeframe Analyzer for Strategic Intelligence
Extracts regime and signal context across multiple timeframes (1m, 5m, 15m, 1h, 4h)
"""

import logging
import time
import asyncio
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import ccxt

logger = logging.getLogger(__name__)

class TimeframeRegime(Enum):
    STRONG_UPTREND = "strong_uptrend"
    UPTREND = "uptrend"
    ACCUMULATION = "accumulation"
    RANGING = "ranging"
    DISTRIBUTION = "distribution"
    DOWNTREND = "downtrend"
    STRONG_DOWNTREND = "strong_downtrend"
    REVERSAL = "reversal"
    BREAKOUT = "breakout"

class VWAPBias(Enum):
    STRONG_ABOVE = "strong_above"
    ABOVE = "above"
    FLAT = "flat"
    BELOW = "below"
    STRONG_BELOW = "strong_below"

@dataclass
class TimeframeContext:
    """Context for a specific timeframe."""
    timeframe: str
    regime: TimeframeRegime
    vwap_bias: VWAPBias
    momentum_score: float  # -1 to 1
    volatility_level: float  # 0 to 1
    volume_profile: str  # "increasing", "decreasing", "stable"
    support_resistance: Dict[str, float]  # {"support": price, "resistance": price}
    confidence: float  # 0 to 1
    timestamp: float

@dataclass
class MultiTimeframeAnalysis:
    """Complete multi-timeframe analysis result."""
    symbol: str
    timeframes: Dict[str, TimeframeContext]
    alignment_score: float  # How aligned are the timeframes
    dominant_trend: str  # Overall market direction
    risk_level: str  # "low", "medium", "high"
    execution_recommendation: str  # "aggressive", "normal", "conservative", "avoid"
    confidence: float
    timestamp: float

class MultiTimeframeAnalyzer:
    """
    Multi-Timeframe Strategic Intelligence Analyzer

    Analyzes market context across multiple timeframes to provide
    strategic intelligence for enhanced trading decisions.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.mta_config = config.get('multi_timeframe', {})

        # 🎯 STRATEGY-MODE-AWARE TIMEFRAME SELECTION
        self.strategy_timeframes = {
            'scalping': {
                'timeframes': ['1m', '5m', '15m'],  # 🔧 FIXED: Removed unsupported 3m timeframe
                'weights': {
                    '1m': 0.50,   # Primary execution timing (increased)
                    '5m': 0.35,   # Short-term momentum (increased)
                    '15m': 0.15   # Context only (increased)
                }
            },
            'intraday': {
                'timeframes': ['5m', '15m', '30m', '1h'],
                'weights': {
                    '5m': 0.25,   # Entry timing
                    '15m': 0.35,  # Primary trend
                    '30m': 0.25,  # Medium-term context
                    '1h': 0.15    # Macro context
                }
            },
            'swing': {
                'timeframes': ['1h', '2h', '4h', '1d'],
                'weights': {
                    '1h': 0.20,   # Entry timing
                    '2h': 0.30,   # Primary trend
                    '4h': 0.35,   # Major trend
                    '1d': 0.15    # Macro trend
                }
            },
            'investor': {
                'timeframes': ['4h', '1d', '1w'],
                'weights': {
                    '4h': 0.25,   # Entry timing
                    '1d': 0.50,   # Primary trend
                    '1w': 0.25    # Long-term trend
                }
            }
        }

        # Default to scalping if no strategy mode specified
        self.current_strategy_mode = config.get('strategy_mode', 'scalping')

        # Set timeframes and weights based on strategy mode
        strategy_config = self.strategy_timeframes.get(self.current_strategy_mode,
                                                      self.strategy_timeframes['scalping'])
        self.timeframes = strategy_config['timeframes']
        self.timeframe_weights = strategy_config['weights']

        # Analysis parameters
        self.lookback_periods = {
            '1m': 60,    # 1 hour of 1m candles
            '5m': 48,    # 4 hours of 5m candles
            '15m': 32,   # 8 hours of 15m candles
            '30m': 24,   # 12 hours of 30m candles
            '1h': 24,    # 24 hours of 1h candles
            '2h': 24,    # 48 hours of 2h candles
            '4h': 12,    # 48 hours of 4h candles
            '1d': 30,    # 30 days of daily candles
            '1w': 12     # 12 weeks of weekly candles
        }

        # Cache for OHLCV data
        self.ohlcv_cache: Dict[str, Dict[str, List]] = {}
        self.cache_expiry: Dict[str, float] = {}

        # Performance tracking
        self.analysis_count = 0
        self.cache_hits = 0

        logger.info("🧱 Multi-Timeframe Analyzer initialized")
        logger.info(f"   🎯 Strategy Mode: {self.current_strategy_mode.upper()}")
        logger.info(f"   📊 Timeframes: {self.timeframes}")
        logger.info(f"   ⚖️ Weights: {self.timeframe_weights}")

    def set_strategy_mode(self, strategy_mode: str):
        """🎯 Dynamically update strategy mode and timeframes."""
        if strategy_mode not in self.strategy_timeframes:
            logger.warning(f"⚠️ Unknown strategy mode: {strategy_mode}, defaulting to scalping")
            strategy_mode = 'scalping'

        if strategy_mode != self.current_strategy_mode:
            self.current_strategy_mode = strategy_mode
            strategy_config = self.strategy_timeframes[strategy_mode]
            self.timeframes = strategy_config['timeframes']
            self.timeframe_weights = strategy_config['weights']

            # Clear cache when strategy mode changes
            self.ohlcv_cache = {}
            self.cache_expiry = {}

            logger.info(f"🎯 Strategy mode updated to: {strategy_mode.upper()}")
            logger.info(f"   📊 New timeframes: {self.timeframes}")
            logger.info(f"   ⚖️ New weights: {self.timeframe_weights}")

    def get_strategy_mode(self) -> str:
        """Get current strategy mode."""
        return self.current_strategy_mode

    async def analyze_symbol(self, symbol: str, exchange_client) -> MultiTimeframeAnalysis:
        """
        Perform comprehensive multi-timeframe analysis for a symbol.

        Args:
            symbol: Trading symbol (e.g., 'DOGE/USDT:USDT')
            exchange_client: CCXT exchange client for data fetching

        Returns:
            MultiTimeframeAnalysis with complete context
        """
        try:
            start_time = time.time()
            self.analysis_count += 1

            # Fetch OHLCV data for all timeframes
            ohlcv_data = await self._fetch_multi_timeframe_data(symbol, exchange_client)

            # Analyze each timeframe
            timeframe_contexts = {}
            for timeframe in self.timeframes:
                if timeframe in ohlcv_data and ohlcv_data[timeframe]:
                    context = await self._analyze_timeframe(
                        symbol, timeframe, ohlcv_data[timeframe]
                    )
                    if context:
                        timeframe_contexts[timeframe] = context

            # Generate overall analysis
            analysis = await self._generate_multi_timeframe_analysis(
                symbol, timeframe_contexts
            )

            processing_time = (time.time() - start_time) * 1000
            logger.info(f"🧱 MTA analysis complete in {processing_time:.1f}ms: "
                       f"{symbol} - {analysis.dominant_trend} (confidence: {analysis.confidence:.2f})")

            return analysis

        except Exception as e:
            logger.error(f"❌ Error in multi-timeframe analysis: {e}")
            return self._get_fallback_analysis(symbol)

    async def _fetch_multi_timeframe_data(self, symbol: str, exchange_client) -> Dict[str, List]:
        """Fetch OHLCV data for all timeframes with caching."""
        ohlcv_data = {}

        for timeframe in self.timeframes:
            try:
                # Check cache first
                cache_key = f"{symbol}_{timeframe}"
                current_time = time.time()

                if (cache_key in self.ohlcv_cache and
                    cache_key in self.cache_expiry and
                    current_time < self.cache_expiry[cache_key]):
                    ohlcv_data[timeframe] = self.ohlcv_cache[cache_key]
                    self.cache_hits += 1
                    continue

                # Fetch fresh data
                limit = self.lookback_periods[timeframe]
                ohlcv = await exchange_client.fetch_ohlcv(
                    symbol, timeframe, limit=limit
                )

                if ohlcv and len(ohlcv) > 10:  # Minimum data requirement
                    ohlcv_data[timeframe] = ohlcv

                    # Cache the data
                    self.ohlcv_cache[cache_key] = ohlcv
                    # Cache expiry based on timeframe
                    cache_duration = self._get_cache_duration(timeframe)
                    self.cache_expiry[cache_key] = current_time + cache_duration

                    logger.debug(f"📊 Fetched {len(ohlcv)} {timeframe} candles for {symbol}")
                else:
                    logger.warning(f"⚠️ Insufficient {timeframe} data for {symbol}")

            except Exception as e:
                logger.error(f"❌ Error fetching {timeframe} data for {symbol}: {e}")

        return ohlcv_data

    def _get_cache_duration(self, timeframe: str) -> float:
        """Get cache duration in seconds based on timeframe."""
        durations = {
            '1m': 30,     # 30 seconds
            '5m': 150,    # 2.5 minutes
            '15m': 450,   # 7.5 minutes
            '1h': 1800,   # 30 minutes
            '4h': 7200    # 2 hours
        }
        return durations.get(timeframe, 300)

    async def _analyze_timeframe(self, symbol: str, timeframe: str,
                                ohlcv: List) -> Optional[TimeframeContext]:
        """Analyze a specific timeframe and generate context."""
        try:
            if len(ohlcv) < 10:
                return None

            # Convert OHLCV to numpy arrays for analysis
            timestamps = np.array([candle[0] for candle in ohlcv])
            opens = np.array([candle[1] for candle in ohlcv])
            highs = np.array([candle[2] for candle in ohlcv])
            lows = np.array([candle[3] for candle in ohlcv])
            closes = np.array([candle[4] for candle in ohlcv])
            volumes = np.array([candle[5] for candle in ohlcv])

            # Calculate technical indicators
            regime = self._detect_regime(closes, highs, lows, volumes)
            vwap_bias = self._calculate_vwap_bias(closes, highs, lows, volumes)
            momentum_score = self._calculate_momentum(closes)
            volatility_level = self._calculate_volatility(closes)
            volume_profile = self._analyze_volume_profile(volumes)
            support_resistance = self._find_support_resistance(highs, lows, closes)
            confidence = self._calculate_confidence(closes, volumes, len(ohlcv))

            return TimeframeContext(
                timeframe=timeframe,
                regime=regime,
                vwap_bias=vwap_bias,
                momentum_score=momentum_score,
                volatility_level=volatility_level,
                volume_profile=volume_profile,
                support_resistance=support_resistance,
                confidence=confidence,
                timestamp=time.time()
            )

        except Exception as e:
            logger.error(f"❌ Error analyzing {timeframe} timeframe: {e}")
            return None

    def _detect_regime(self, closes: np.ndarray, highs: np.ndarray,
                      lows: np.ndarray, volumes: np.ndarray) -> TimeframeRegime:
        """Detect market regime for the timeframe."""
        try:
            # Calculate trend indicators
            sma_short = np.mean(closes[-10:])  # Short-term average
            sma_long = np.mean(closes[-20:]) if len(closes) >= 20 else np.mean(closes)

            # Price momentum
            price_change = (closes[-1] - closes[-10]) / closes[-10] if len(closes) >= 10 else 0

            # Volatility
            volatility = np.std(closes[-20:]) / np.mean(closes[-20:]) if len(closes) >= 20 else 0

            # Volume trend
            volume_trend = np.mean(volumes[-5:]) / np.mean(volumes[-10:]) if len(volumes) >= 10 else 1

            # Regime classification
            if price_change > 0.03 and sma_short > sma_long * 1.02:
                return TimeframeRegime.STRONG_UPTREND
            elif price_change > 0.01 and sma_short > sma_long:
                return TimeframeRegime.UPTREND
            elif price_change < -0.03 and sma_short < sma_long * 0.98:
                return TimeframeRegime.STRONG_DOWNTREND
            elif price_change < -0.01 and sma_short < sma_long:
                return TimeframeRegime.DOWNTREND
            elif volatility > 0.05:
                return TimeframeRegime.BREAKOUT if volume_trend > 1.2 else TimeframeRegime.REVERSAL
            elif abs(price_change) < 0.005:
                return TimeframeRegime.RANGING
            elif volume_trend > 1.1 and price_change > 0:
                return TimeframeRegime.ACCUMULATION
            elif volume_trend > 1.1 and price_change < 0:
                return TimeframeRegime.DISTRIBUTION
            else:
                return TimeframeRegime.RANGING

        except Exception as e:
            logger.error(f"Error detecting regime: {e}")
            return TimeframeRegime.RANGING

    def _calculate_vwap_bias(self, closes: np.ndarray, highs: np.ndarray,
                           lows: np.ndarray, volumes: np.ndarray) -> VWAPBias:
        """Calculate VWAP bias for the timeframe."""
        try:
            # Calculate VWAP
            typical_prices = (highs + lows + closes) / 3
            vwap = np.sum(typical_prices * volumes) / np.sum(volumes)

            current_price = closes[-1]
            vwap_deviation = (current_price - vwap) / vwap

            if vwap_deviation > 0.02:
                return VWAPBias.STRONG_ABOVE
            elif vwap_deviation > 0.005:
                return VWAPBias.ABOVE
            elif vwap_deviation < -0.02:
                return VWAPBias.STRONG_BELOW
            elif vwap_deviation < -0.005:
                return VWAPBias.BELOW
            else:
                return VWAPBias.FLAT

        except Exception as e:
            logger.error(f"Error calculating VWAP bias: {e}")
            return VWAPBias.FLAT

    def _calculate_momentum(self, closes: np.ndarray) -> float:
        """Calculate momentum score (-1 to 1)."""
        try:
            if len(closes) < 5:
                return 0.0

            # Rate of change over different periods
            roc_short = (closes[-1] - closes[-3]) / closes[-3] if len(closes) >= 3 else 0
            roc_medium = (closes[-1] - closes[-7]) / closes[-7] if len(closes) >= 7 else 0
            roc_long = (closes[-1] - closes[-14]) / closes[-14] if len(closes) >= 14 else 0

            # Weighted momentum score
            momentum = (roc_short * 0.5 + roc_medium * 0.3 + roc_long * 0.2)

            # Clamp to [-1, 1]
            return max(-1.0, min(1.0, momentum * 10))  # Scale by 10 for sensitivity

        except Exception as e:
            logger.error(f"Error calculating momentum: {e}")
            return 0.0

    def _calculate_volatility(self, closes: np.ndarray) -> float:
        """Calculate volatility level (0 to 1)."""
        try:
            if len(closes) < 10:
                return 0.5

            # Calculate rolling standard deviation
            returns = np.diff(closes) / closes[:-1]
            volatility = np.std(returns[-20:]) if len(returns) >= 20 else np.std(returns)

            # Normalize to 0-1 scale (assuming max volatility of 10%)
            normalized_vol = min(1.0, volatility / 0.1)

            return normalized_vol

        except Exception as e:
            logger.error(f"Error calculating volatility: {e}")
            return 0.5

    def _analyze_volume_profile(self, volumes: np.ndarray) -> str:
        """Analyze volume profile trend."""
        try:
            if len(volumes) < 10:
                return "stable"

            recent_avg = np.mean(volumes[-5:])
            historical_avg = np.mean(volumes[-15:-5]) if len(volumes) >= 15 else np.mean(volumes[:-5])

            if historical_avg == 0:
                return "stable"

            volume_change = (recent_avg - historical_avg) / historical_avg

            if volume_change > 0.2:
                return "increasing"
            elif volume_change < -0.2:
                return "decreasing"
            else:
                return "stable"

        except Exception as e:
            logger.error(f"Error analyzing volume profile: {e}")
            return "stable"

    def _find_support_resistance(self, highs: np.ndarray, lows: np.ndarray,
                               closes: np.ndarray) -> Dict[str, float]:
        """Find key support and resistance levels."""
        try:
            if len(closes) < 20:
                current_price = closes[-1]
                return {
                    "support": current_price * 0.98,
                    "resistance": current_price * 1.02
                }

            # Simple support/resistance using recent highs and lows
            recent_high = np.max(highs[-20:])
            recent_low = np.min(lows[-20:])
            current_price = closes[-1]

            # Find nearest support (recent low or psychological level)
            support = recent_low
            if current_price - recent_low > (recent_high - recent_low) * 0.5:
                # Look for intermediate support
                mid_lows = lows[-10:]
                support = max(np.min(mid_lows), recent_low)

            # Find nearest resistance (recent high or psychological level)
            resistance = recent_high
            if recent_high - current_price > (recent_high - recent_low) * 0.5:
                # Look for intermediate resistance
                mid_highs = highs[-10:]
                resistance = min(np.max(mid_highs), recent_high)

            return {
                "support": float(support),
                "resistance": float(resistance)
            }

        except Exception as e:
            logger.error(f"Error finding support/resistance: {e}")
            current_price = closes[-1] if len(closes) > 0 else 1.0
            return {
                "support": current_price * 0.98,
                "resistance": current_price * 1.02
            }

    def _calculate_confidence(self, closes: np.ndarray, volumes: np.ndarray,
                            data_points: int) -> float:
        """Calculate confidence in the analysis."""
        try:
            # Base confidence on data quality
            confidence = 0.5

            # More data points = higher confidence
            if data_points >= 50:
                confidence += 0.3
            elif data_points >= 30:
                confidence += 0.2
            elif data_points >= 20:
                confidence += 0.1

            # Consistent volume = higher confidence
            if len(volumes) >= 10:
                volume_cv = np.std(volumes[-10:]) / np.mean(volumes[-10:])
                if volume_cv < 0.5:  # Low coefficient of variation
                    confidence += 0.1

            # Price stability = higher confidence
            if len(closes) >= 10:
                price_cv = np.std(closes[-10:]) / np.mean(closes[-10:])
                if price_cv < 0.02:  # Low price volatility
                    confidence += 0.1

            return min(0.95, confidence)

        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return 0.5

    async def _generate_multi_timeframe_analysis(self, symbol: str,
                                               timeframe_contexts: Dict[str, TimeframeContext]) -> MultiTimeframeAnalysis:
        """Generate overall multi-timeframe analysis."""
        try:
            if not timeframe_contexts:
                return self._get_fallback_analysis(symbol)

            # Calculate alignment score
            alignment_score = self._calculate_alignment_score(timeframe_contexts)

            # Determine dominant trend
            dominant_trend = self._determine_dominant_trend(timeframe_contexts)

            # Assess risk level
            risk_level = self._assess_risk_level(timeframe_contexts, alignment_score)

            # Generate execution recommendation
            execution_recommendation = self._generate_execution_recommendation(
                alignment_score, risk_level, timeframe_contexts
            )

            # Calculate overall confidence
            confidence = self._calculate_overall_confidence(timeframe_contexts, alignment_score)

            return MultiTimeframeAnalysis(
                symbol=symbol,
                timeframes=timeframe_contexts,
                alignment_score=alignment_score,
                dominant_trend=dominant_trend,
                risk_level=risk_level,
                execution_recommendation=execution_recommendation,
                confidence=confidence,
                timestamp=time.time()
            )

        except Exception as e:
            logger.error(f"❌ Error generating multi-timeframe analysis: {e}")
            return self._get_fallback_analysis(symbol)

    def _calculate_alignment_score(self, timeframe_contexts: Dict[str, TimeframeContext]) -> float:
        """Calculate how aligned the timeframes are."""
        try:
            if len(timeframe_contexts) < 2:
                return 0.5

            # Get momentum scores for alignment calculation
            momentum_scores = []
            weights = []

            for timeframe, context in timeframe_contexts.items():
                momentum_scores.append(context.momentum_score)
                weights.append(self.timeframe_weights.get(timeframe, 0.2))

            if not momentum_scores:
                return 0.5

            # Calculate alignment as inverse of momentum variance
            momentum_variance = np.var(momentum_scores)
            alignment = 1.0 / (1.0 + momentum_variance * 10)  # Scale variance

            return min(0.95, max(0.05, alignment))

        except Exception as e:
            logger.error(f"Error calculating alignment score: {e}")
            return 0.5

    def _determine_dominant_trend(self, timeframe_contexts: Dict[str, TimeframeContext]) -> str:
        """Determine the dominant trend across timeframes."""
        try:
            trend_scores = {"bullish": 0, "bearish": 0, "neutral": 0}

            for timeframe, context in timeframe_contexts.items():
                weight = self.timeframe_weights.get(timeframe, 0.2)

                # Score based on regime
                if context.regime in [TimeframeRegime.STRONG_UPTREND, TimeframeRegime.UPTREND,
                                    TimeframeRegime.ACCUMULATION, TimeframeRegime.BREAKOUT]:
                    trend_scores["bullish"] += weight
                elif context.regime in [TimeframeRegime.STRONG_DOWNTREND, TimeframeRegime.DOWNTREND,
                                      TimeframeRegime.DISTRIBUTION]:
                    trend_scores["bearish"] += weight
                else:
                    trend_scores["neutral"] += weight

                # Additional scoring from momentum
                if context.momentum_score > 0.3:
                    trend_scores["bullish"] += weight * 0.5
                elif context.momentum_score < -0.3:
                    trend_scores["bearish"] += weight * 0.5
                else:
                    trend_scores["neutral"] += weight * 0.5

            # Return dominant trend
            return max(trend_scores, key=trend_scores.get)

        except Exception as e:
            logger.error(f"Error determining dominant trend: {e}")
            return "neutral"

    def _assess_risk_level(self, timeframe_contexts: Dict[str, TimeframeContext],
                          alignment_score: float) -> str:
        """Assess overall risk level."""
        try:
            risk_factors = 0

            # Low alignment = higher risk
            if alignment_score < 0.3:
                risk_factors += 2
            elif alignment_score < 0.6:
                risk_factors += 1

            # High volatility = higher risk
            avg_volatility = np.mean([ctx.volatility_level for ctx in timeframe_contexts.values()])
            if avg_volatility > 0.7:
                risk_factors += 2
            elif avg_volatility > 0.4:
                risk_factors += 1

            # Conflicting regimes = higher risk
            regimes = [ctx.regime for ctx in timeframe_contexts.values()]
            bullish_regimes = sum(1 for r in regimes if r in [TimeframeRegime.UPTREND, TimeframeRegime.STRONG_UPTREND])
            bearish_regimes = sum(1 for r in regimes if r in [TimeframeRegime.DOWNTREND, TimeframeRegime.STRONG_DOWNTREND])

            if bullish_regimes > 0 and bearish_regimes > 0:
                risk_factors += 1

            # Determine risk level
            if risk_factors >= 4:
                return "high"
            elif risk_factors >= 2:
                return "medium"
            else:
                return "low"

        except Exception as e:
            logger.error(f"Error assessing risk level: {e}")
            return "medium"

    def _generate_execution_recommendation(self, alignment_score: float, risk_level: str,
                                         timeframe_contexts: Dict[str, TimeframeContext]) -> str:
        """Generate execution recommendation."""
        try:
            # High alignment + low risk = aggressive
            if alignment_score > 0.7 and risk_level == "low":
                return "aggressive"

            # High risk = avoid or conservative
            if risk_level == "high":
                return "avoid" if alignment_score < 0.4 else "conservative"

            # Medium conditions = normal
            if alignment_score > 0.5 and risk_level in ["low", "medium"]:
                return "normal"

            # Default to conservative
            return "conservative"

        except Exception as e:
            logger.error(f"Error generating execution recommendation: {e}")
            return "conservative"

    def _calculate_overall_confidence(self, timeframe_contexts: Dict[str, TimeframeContext],
                                    alignment_score: float) -> float:
        """Calculate overall confidence in the analysis."""
        try:
            # Average individual confidences
            individual_confidences = [ctx.confidence for ctx in timeframe_contexts.values()]
            avg_confidence = np.mean(individual_confidences)

            # Boost confidence with alignment
            alignment_boost = alignment_score * 0.2

            # Number of timeframes analyzed
            coverage_boost = min(0.1, len(timeframe_contexts) * 0.02)

            overall_confidence = avg_confidence + alignment_boost + coverage_boost

            return min(0.95, max(0.1, overall_confidence))

        except Exception as e:
            logger.error(f"Error calculating overall confidence: {e}")
            return 0.5

    def _get_fallback_analysis(self, symbol: str) -> MultiTimeframeAnalysis:
        """Get fallback analysis when data is insufficient."""
        return MultiTimeframeAnalysis(
            symbol=symbol,
            timeframes={},
            alignment_score=0.5,
            dominant_trend="neutral",
            risk_level="medium",
            execution_recommendation="conservative",
            confidence=0.3,
            timestamp=time.time()
        )

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        cache_hit_rate = self.cache_hits / max(1, self.analysis_count * len(self.timeframes))

        return {
            "total_analyses": self.analysis_count,
            "cache_hits": self.cache_hits,
            "cache_hit_rate": cache_hit_rate,
            "cached_symbols": len(self.ohlcv_cache),
            "timeframes_analyzed": len(self.timeframes)
        }
