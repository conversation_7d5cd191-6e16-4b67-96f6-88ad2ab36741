#!/usr/bin/env python3
"""
Onnyx UI with Live Data - Phase 9.1
Runs the Onnyx UI with simulated market data to show signals
"""

import asyncio
import logging
import yaml
import signal
import sys
import time
import random
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

from ui.ai_strategy_tuner import AIStrategyTunerDashboard
from storage.live_store import LiveDataStore
from execution.execution_controller import ExecutionController
from feeds.trade_parser import MarketFeatures
from autonomy.autonomous_trader import AutonomousTrader
from monitoring.account_tracker import LiveAccountTracker

class OnnxWithDataRunner:
    """Onnyx UI with simulated market data for demonstration."""

    def __init__(self):
        self.ui = None
        self.data_store = None
        self.execution_controller = None
        self.account_tracker = None
        self.autonomous_trader = None
        self.runner = None
        self.shutdown_event = asyncio.Event()
        self.data_generator_task = None

    async def start(self):
        """Start the Onnyx UI with data simulation."""
        try:
            logger.info("🚀 Starting Onnyx UI with Live Data - Phase 9.1")

            # Load configuration
            script_dir = Path(__file__).parent
            config_path = script_dir / "config" / "strategy.yaml"
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)

            # Initialize components
            logger.info("📊 Initializing data store...")
            self.data_store = LiveDataStore(config)

            logger.info("🎯 Initializing execution controller...")
            self.execution_controller = ExecutionController(config)

            logger.info("🏦 Initializing account tracker...")
            self.account_tracker = LiveAccountTracker(config)

            logger.info("🤖 Initializing autonomous trader...")
            self.autonomous_trader = AutonomousTrader(
                config, self.data_store, self.execution_controller, self.account_tracker
            )

            logger.info("🎨 Initializing Onnyx UI...")
            self.ui = AIStrategyTunerDashboard(config, self.data_store, self.execution_controller)

            # Pass autonomous trader reference to UI
            self.ui.autonomous_trader = self.autonomous_trader

            # Start data simulation
            logger.info("📡 Starting market data simulation...")
            self.data_generator_task = asyncio.create_task(self.simulate_market_data())

            # Start the dashboard
            logger.info("🌐 Starting dashboard server...")
            self.runner = await self.ui.start_server(host='localhost', port=8086)

            # Mark strategy as running in UI
            self.ui.set_strategy_running(True)

            logger.info("✅ Onnyx UI with Live Data started successfully!")
            logger.info("🌐 Dashboard URL: http://localhost:8086")
            logger.info("\n🎯 Active Features:")
            logger.info("   ✅ Onnyx-themed interface")
            logger.info("   ✅ Live Account Metrics panel")
            logger.info("   ✅ Simulated market data")
            logger.info("   ✅ AI model outputs")
            logger.info("   ✅ Trading signals generation")
            logger.info("   ✅ Real-time updates")
            logger.info("   🤖 Autonomous Trading Intelligence")
            logger.info("   📊 Market Microstructure Analysis")
            logger.info("   🎯 Profit Optimization Engine")

            logger.info("\n💰 Trading Configuration:")
            logger.info("   Account: $5.90 | Leverage: 20x | Symbol: DOGE/USDT:USDT")
            logger.info("   Data: SIMULATED | Signals: ACTIVE | Autonomous: READY")

            logger.info("\n⌨️ Press Ctrl+C to stop the system")

            # Wait for shutdown signal
            await self.shutdown_event.wait()

        except Exception as e:
            logger.error(f"❌ Error starting Onnyx UI: {e}")
            raise

    async def simulate_market_data(self):
        """Simulate market data and generate signals."""
        symbol = "DOGE-USDT"  # Match config format

        # 🎯 Get real DOGE price as base instead of fake price
        try:
            real_price_data = await self.ui_dashboard._get_ticker_data(symbol)
            base_price = real_price_data['price']
            logger.info(f"📈 Using real DOGE price as base: ${base_price}")
        except Exception as e:
            logger.warning(f"Failed to get real price, using fallback: {e}")
            base_price = 0.179  # Real DOGE price fallback

        logger.info("📈 Market data simulation started")

        # 🔧 FIX: Throttling variables for LLM decisions
        last_llm_decision_time = 0
        llm_decision_interval = 10  # Only generate LLM decisions every 10 seconds
        cycle_count = 0

        while not self.shutdown_event.is_set():
            try:
                # Generate realistic market features
                current_time = time.time()
                cycle_count += 1

                # Simulate price movement
                price_change = random.uniform(-0.002, 0.002)  # ±0.2% change
                current_price = base_price * (1 + price_change)
                base_price = current_price  # Update base for next iteration

                # Create market features
                features = MarketFeatures(
                    symbol=symbol,
                    timestamp=int(current_time * 1000),  # milliseconds
                    last_price=current_price,
                    price_change_1m=random.uniform(-0.02, 0.02),
                    price_velocity=random.uniform(-0.001, 0.001),
                    volume_1m=random.uniform(1000000, 5000000),
                    buy_volume_ratio=random.uniform(0.4, 0.6),
                    volume_delta=random.uniform(-100000, 100000),
                    order_flow_imbalance=random.uniform(-0.5, 0.5),
                    trade_intensity=random.uniform(10, 50),
                    avg_trade_size=random.uniform(100, 1000),
                    rsi=random.uniform(25, 75),
                    vwap=current_price * random.uniform(0.998, 1.002),
                    volatility=random.uniform(0.01, 0.05),
                    bid_ask_spread=random.uniform(0.0001, 0.0005),
                    market_depth=random.uniform(10000, 50000)
                )

                # Store features
                self.data_store.store_features(features)

                # Generate model outputs
                await self.generate_model_outputs(features)

                # 🔧 FIX: Check if we can trade before generating signals/decisions
                can_trade = await self._can_execute_new_trades()

                # Generate trading signals occasionally (only if we can trade)
                if can_trade and random.random() < 0.3:  # 30% chance
                    await self.generate_trading_signal(features)

                # 🔧 FIX: Throttled LLM decisions - only every 10 seconds and if we can trade
                llm_decision = None
                time_since_last_llm = current_time - last_llm_decision_time

                if can_trade and time_since_last_llm >= llm_decision_interval:
                    llm_decision = await self.generate_llm_decision(features)
                    last_llm_decision_time = current_time
                    logger.info(f"🧠 LLM Decision generated (next in {llm_decision_interval}s)")
                elif not can_trade:
                    # 🔧 FIX: Log intent when positions are maxed
                    if cycle_count % 10 == 0:  # Log every 10 cycles to avoid spam
                        current_positions = await self._get_current_position_count()
                        logger.info(f"📤 Skipping LLM inference - positions maxed ({current_positions}/2)")

                # PHASE 10: Autonomous Trading Intelligence (only if we have a decision and can trade)
                if llm_decision and can_trade and self.autonomous_trader:
                    try:
                        # Get model outputs for autonomous decision
                        model_outputs = self.data_store.get_model_outputs(features.symbol)

                        # Process autonomous trading cycle
                        autonomous_result = await self.autonomous_trader.process_autonomous_trading_cycle(
                            llm_decision, model_outputs, features.__dict__
                        )

                        if autonomous_result:
                            logger.info(f"🤖 Autonomous trade result: {autonomous_result.action} "
                                       f"{autonomous_result.position_size:.2f} @ ${autonomous_result.execution_price:.4f}")

                    except Exception as e:
                        logger.error(f"❌ Error in autonomous trading cycle: {e}")

                # Wait before next update - STANDARDIZED TO 2-SECOND BASE CYCLE
                await asyncio.sleep(2)  # Update every 2 seconds

            except Exception as e:
                logger.error(f"Error in data simulation: {e}")
                await asyncio.sleep(5)

    async def generate_model_outputs(self, features: MarketFeatures):
        """Generate AI model outputs."""
        models = ['rsi', 'vwap', 'orderflow', 'volatility']

        for model_name in models:
            # Generate realistic model output
            if model_name == 'rsi':
                if features.rsi < 30:
                    action, confidence = 'BUY', random.uniform(0.7, 0.9)
                elif features.rsi > 70:
                    action, confidence = 'SELL', random.uniform(0.7, 0.9)
                else:
                    action, confidence = 'WAIT', random.uniform(0.3, 0.6)
            elif model_name == 'vwap':
                price_vs_vwap = (features.last_price - features.vwap) / features.vwap
                if price_vs_vwap > 0.01:
                    action, confidence = 'SELL', random.uniform(0.6, 0.8)
                elif price_vs_vwap < -0.01:
                    action, confidence = 'BUY', random.uniform(0.6, 0.8)
                else:
                    action, confidence = 'WAIT', random.uniform(0.4, 0.6)
            else:
                action = random.choice(['BUY', 'SELL', 'WAIT'])
                confidence = random.uniform(0.5, 0.9)

            # Store model output
            self.data_store.store_model_output(
                features.symbol,
                model_name,
                {
                    'action': action,
                    'confidence': confidence,
                    'signal': f'{action}_SIGNAL',
                    'value': random.uniform(0, 100)
                }
            )

    async def generate_trading_signal(self, features: MarketFeatures):
        """Generate a trading signal with REAL P&L calculation (Phase 9.4)."""
        actions = ['LONG', 'SHORT', 'WAIT']
        action = random.choice(actions)
        confidence = random.uniform(0.6, 0.95)

        # PHASE 9.4: Calculate REALISTIC P&L based on actual account size and leverage
        realistic_pnl = self._calculate_realistic_pnl(action, features.last_price, confidence)

        signal_data = {
            'symbol': features.symbol,
            'action': action,
            'confidence': confidence,
            'score': random.uniform(-1, 1),
            'reasoning': f"AI ensemble signal based on {random.choice(['RSI', 'VWAP', 'orderflow'])} analysis",
            'timestamp': time.time(),
            'price': features.last_price,
            'pnl': realistic_pnl  # PHASE 9.4: Real P&L calculation
        }

        self.data_store.store_signal(signal_data)
        logger.info(f"📊 Signal: {action} {features.symbol} @ ${features.last_price:.4f}")

    def _calculate_realistic_pnl(self, action: str, current_price: float, confidence: float) -> float:
        """Calculate realistic P&L based on actual account size and leverage (Phase 9.4)."""
        try:
            # Get account tracker for real account data
            account_tracker = None
            if hasattr(self, 'execution_controller') and self.execution_controller:
                account_tracker = getattr(self.execution_controller, 'account_tracker', None)

            if not account_tracker:
                # Fallback to conservative estimates
                account_balance = 5.0
                leverage = 20.0
            else:
                # Get real account data
                snapshot = account_tracker.get_current_snapshot()
                if snapshot:
                    account_balance = snapshot.available_balance
                    leverage = snapshot.leverage
                else:
                    account_balance = 5.0
                    leverage = 20.0

            # Calculate realistic position size (2-5% of account)
            risk_percentage = 0.02 + (confidence * 0.03)  # 2-5% based on confidence
            position_value = account_balance * risk_percentage * leverage
            position_size = position_value / current_price

            # Simulate realistic price movement (0.1% to 2% based on confidence)
            if action == 'WAIT':
                return 0.0

            # Price movement simulation
            base_movement = 0.001 + (confidence * 0.019)  # 0.1% to 2%
            direction = 1 if action in ['LONG', 'BUY'] else -1

            # Add some randomness for realism
            movement_multiplier = random.uniform(0.3, 1.8)
            price_change = current_price * base_movement * direction * movement_multiplier

            # Calculate P&L
            pnl = price_change * position_size

            # Apply trading fees (0.04% typical for futures)
            fees = position_value * 0.0004
            net_pnl = pnl - fees

            # Cap P&L to realistic ranges for account size
            max_pnl = account_balance * 0.1  # Max 10% of account per signal
            min_pnl = -account_balance * 0.05  # Max 5% loss per signal

            realistic_pnl = max(min_pnl, min(max_pnl, net_pnl))

            return round(realistic_pnl, 4)

        except Exception as e:
            logger.error(f"Error calculating realistic P&L: {e}")
            # Fallback to small realistic values
            return random.uniform(-0.25, 0.50)

    async def _can_execute_new_trades(self) -> bool:
        """🔧 FIX: Check if we can execute new trades (positions not maxed)."""
        try:
            if not self.autonomous_trader:
                return True  # If no autonomous trader, allow signals

            # Check if autonomous trading conditions allow new trades
            current_positions = await self._get_current_position_count()
            max_positions = getattr(self.autonomous_trader, 'max_concurrent_positions', 2)

            return current_positions < max_positions

        except Exception as e:
            logger.error(f"Error checking trade conditions: {e}")
            return True  # Default to allowing trades

    async def _get_current_position_count(self) -> int:
        """🔧 FIX: Get current number of open positions from LIVE account tracker."""
        try:
            # 🎯 PRIMARY SOURCE: Use account tracker for LIVE position data
            if hasattr(self, 'execution_controller') and self.execution_controller:
                account_tracker = getattr(self.execution_controller, 'account_tracker', None)
                if account_tracker and hasattr(account_tracker, 'current_snapshot'):
                    snapshot = account_tracker.current_snapshot
                    if snapshot:
                        live_positions = snapshot.open_positions
                        logger.debug(f"🔍 Live position count from account tracker: {live_positions}")
                        return live_positions

            # 🎯 SECONDARY SOURCE: Check autonomous trader
            if self.autonomous_trader:
                if hasattr(self.autonomous_trader, '_get_current_position_count'):
                    trader_positions = await self.autonomous_trader._get_current_position_count()
                    logger.debug(f"🔍 Position count from autonomous trader: {trader_positions}")
                    return trader_positions

                # Fallback: count from autonomous trades
                if hasattr(self.autonomous_trader, 'autonomous_trades'):
                    active_trades = [trade for trade in self.autonomous_trader.autonomous_trades
                                   if trade.trade_status in ['executed', 'pending']]
                    fallback_count = len(active_trades)
                    logger.debug(f"🔍 Position count from trade list fallback: {fallback_count}")
                    return fallback_count

            logger.debug("🔍 No position tracking available - defaulting to 0")
            return 0

        except Exception as e:
            logger.error(f"Error getting position count: {e}")
            return 0

    async def generate_llm_decision(self, features: MarketFeatures):
        """🔧 FIX: Generate an LLM decision with better intent logging."""
        # 🔧 FIX: Get recent signal to align decisions
        recent_signal = self.data_store.get_latest_signal(features.symbol)
        signal_action = recent_signal.get('action', 'WAIT') if recent_signal else 'WAIT'

        # 🔧 FIX: Align LLM decision with signal or make independent decision
        if signal_action != 'WAIT' and random.random() < 0.7:  # 70% chance to align with signal
            action = signal_action
            reasoning = f"LLM aligned with signal: {signal_action}"
        else:
            # Independent LLM decision
            actions = ['LONG', 'SHORT', 'WAIT']
            action = random.choice(actions)
            reasoning = f"LLM independent analysis suggests {action.lower()} position"

        confidence = random.uniform(0.7, 0.95)

        decision_data = {
            'symbol': features.symbol,
            'action': action,
            'confidence': confidence,
            'reasoning': reasoning,
            'timestamp': time.time(),
            'price': features.last_price,
            'signal_alignment': signal_action == action,  # Track alignment
            'signal_action': signal_action  # Track what signal said
        }

        self.data_store.store_llm_decision(decision_data)

        # 🔧 FIX: Better intent logging
        alignment_status = "✅ ALIGNED" if signal_action == action else "⚠️ DIVERGENT"
        logger.info(f"🧠 LLM Decision: {action} {features.symbol} | Signal: {signal_action} | {alignment_status}")

        # Return decision for autonomous trading
        return decision_data

    async def stop(self):
        """Stop all components."""
        try:
            logger.info("🛑 Shutting down Onnyx UI...")

            # Stop data generation
            if self.data_generator_task:
                self.data_generator_task.cancel()
                try:
                    await self.data_generator_task
                except asyncio.CancelledError:
                    pass

            # Stop execution controller
            if self.execution_controller and hasattr(self.execution_controller, 'account_tracker'):
                await self.execution_controller.account_tracker.stop_monitoring()

            # Stop web server
            if self.runner:
                await self.runner.cleanup()

            logger.info("✅ Onnyx UI shutdown complete")

        except Exception as e:
            logger.error(f"❌ Error during shutdown: {e}")

    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"📡 Received signal {signum}, shutting down...")
        self.shutdown_event.set()

async def main():
    """Main function."""
    runner = OnnxWithDataRunner()

    # Setup signal handlers
    signal.signal(signal.SIGINT, runner.signal_handler)
    signal.signal(signal.SIGTERM, runner.signal_handler)

    try:
        await runner.start()
    except KeyboardInterrupt:
        logger.info("⌨️ Keyboard interrupt received")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
    finally:
        await runner.stop()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("👋 System shutdown complete!")
    except Exception as e:
        logger.error(f"❌ Application error: {e}")
        sys.exit(1)
