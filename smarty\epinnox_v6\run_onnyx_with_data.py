#!/usr/bin/env python3
"""
Onnyx UI with Live Data - Phase 9.1
Runs the Onnyx UI with simulated market data to show signals
"""

import asyncio
import logging
import yaml
import signal
import sys
import time
import random
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

from ui.ai_strategy_tuner import AIStrategyTunerDashboard
from storage.live_store import LiveDataStore
from execution.execution_controller import ExecutionController
from feeds.trade_parser import MarketFeatures

class OnnxWithDataRunner:
    """Onnyx UI with simulated market data for demonstration."""

    def __init__(self):
        self.ui = None
        self.data_store = None
        self.execution_controller = None
        self.runner = None
        self.shutdown_event = asyncio.Event()
        self.data_generator_task = None

    async def start(self):
        """Start the Onnyx UI with data simulation."""
        try:
            logger.info("🚀 Starting Onnyx UI with Live Data - Phase 9.1")

            # Load configuration
            script_dir = Path(__file__).parent
            config_path = script_dir / "config" / "strategy.yaml"
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)

            # Initialize components
            logger.info("📊 Initializing data store...")
            self.data_store = LiveDataStore(config)

            logger.info("🎯 Initializing execution controller...")
            self.execution_controller = ExecutionController(config)

            logger.info("🎨 Initializing Onnyx UI...")
            self.ui = AIStrategyTunerDashboard(config, self.data_store, self.execution_controller)

            # Start data simulation
            logger.info("📡 Starting market data simulation...")
            self.data_generator_task = asyncio.create_task(self.simulate_market_data())

            # Start the dashboard
            logger.info("🌐 Starting dashboard server...")
            self.runner = await self.ui.start_server(host='localhost', port=8086)

            # Mark strategy as running in UI
            self.ui.set_strategy_running(True)

            logger.info("✅ Onnyx UI with Live Data started successfully!")
            logger.info("🌐 Dashboard URL: http://localhost:8086")
            logger.info("\n🎯 Active Features:")
            logger.info("   ✅ Onnyx-themed interface")
            logger.info("   ✅ Live Account Metrics panel")
            logger.info("   ✅ Simulated market data")
            logger.info("   ✅ AI model outputs")
            logger.info("   ✅ Trading signals generation")
            logger.info("   ✅ Real-time updates")

            logger.info("\n💰 Trading Configuration:")
            logger.info("   Account: $5.90 | Leverage: 20x | Symbol: DOGE/USDT:USDT")
            logger.info("   Data: SIMULATED | Signals: ACTIVE")

            logger.info("\n⌨️ Press Ctrl+C to stop the system")

            # Wait for shutdown signal
            await self.shutdown_event.wait()

        except Exception as e:
            logger.error(f"❌ Error starting Onnyx UI: {e}")
            raise

    async def simulate_market_data(self):
        """Simulate market data and generate signals."""
        symbol = "DOGE/USDT:USDT"
        base_price = 0.08  # DOGE base price

        logger.info("📈 Market data simulation started")

        while not self.shutdown_event.is_set():
            try:
                # Generate realistic market features
                current_time = time.time()

                # Simulate price movement
                price_change = random.uniform(-0.002, 0.002)  # ±0.2% change
                current_price = base_price * (1 + price_change)
                base_price = current_price  # Update base for next iteration

                # Create market features
                features = MarketFeatures(
                    symbol=symbol,
                    timestamp=int(current_time * 1000),  # milliseconds
                    last_price=current_price,
                    price_change_1m=random.uniform(-0.02, 0.02),
                    price_velocity=random.uniform(-0.001, 0.001),
                    volume_1m=random.uniform(1000000, 5000000),
                    buy_volume_ratio=random.uniform(0.4, 0.6),
                    volume_delta=random.uniform(-100000, 100000),
                    order_flow_imbalance=random.uniform(-0.5, 0.5),
                    trade_intensity=random.uniform(10, 50),
                    avg_trade_size=random.uniform(100, 1000),
                    rsi=random.uniform(25, 75),
                    vwap=current_price * random.uniform(0.998, 1.002),
                    volatility=random.uniform(0.01, 0.05),
                    bid_ask_spread=random.uniform(0.0001, 0.0005),
                    market_depth=random.uniform(10000, 50000)
                )

                # Store features
                self.data_store.store_features(features)

                # Generate model outputs
                await self.generate_model_outputs(features)

                # Generate trading signals occasionally
                if random.random() < 0.3:  # 30% chance
                    await self.generate_trading_signal(features)

                # Generate LLM decisions occasionally
                if random.random() < 0.2:  # 20% chance
                    await self.generate_llm_decision(features)

                # Wait before next update
                await asyncio.sleep(3)  # Update every 3 seconds

            except Exception as e:
                logger.error(f"Error in data simulation: {e}")
                await asyncio.sleep(5)

    async def generate_model_outputs(self, features: MarketFeatures):
        """Generate AI model outputs."""
        models = ['rsi', 'vwap', 'orderflow', 'volatility']

        for model_name in models:
            # Generate realistic model output
            if model_name == 'rsi':
                if features.rsi < 30:
                    action, confidence = 'BUY', random.uniform(0.7, 0.9)
                elif features.rsi > 70:
                    action, confidence = 'SELL', random.uniform(0.7, 0.9)
                else:
                    action, confidence = 'WAIT', random.uniform(0.3, 0.6)
            elif model_name == 'vwap':
                price_vs_vwap = (features.last_price - features.vwap) / features.vwap
                if price_vs_vwap > 0.01:
                    action, confidence = 'SELL', random.uniform(0.6, 0.8)
                elif price_vs_vwap < -0.01:
                    action, confidence = 'BUY', random.uniform(0.6, 0.8)
                else:
                    action, confidence = 'WAIT', random.uniform(0.4, 0.6)
            else:
                action = random.choice(['BUY', 'SELL', 'WAIT'])
                confidence = random.uniform(0.5, 0.9)

            # Store model output
            self.data_store.store_model_output(
                features.symbol,
                model_name,
                {
                    'action': action,
                    'confidence': confidence,
                    'signal': f'{action}_SIGNAL',
                    'value': random.uniform(0, 100)
                }
            )

    async def generate_trading_signal(self, features: MarketFeatures):
        """Generate a trading signal."""
        actions = ['LONG', 'SHORT', 'WAIT']
        action = random.choice(actions)
        confidence = random.uniform(0.6, 0.95)

        signal_data = {
            'symbol': features.symbol,
            'action': action,
            'confidence': confidence,
            'score': random.uniform(-1, 1),
            'reasoning': f"AI ensemble signal based on {random.choice(['RSI', 'VWAP', 'orderflow'])} analysis",
            'timestamp': time.time(),
            'price': features.last_price,
            'pnl': random.uniform(-50, 100)  # Simulated P&L
        }

        self.data_store.store_signal(signal_data)
        logger.info(f"📊 Signal: {action} {features.symbol} @ ${features.last_price:.4f}")

    async def generate_llm_decision(self, features: MarketFeatures):
        """Generate an LLM decision."""
        actions = ['LONG', 'SHORT', 'WAIT']
        action = random.choice(actions)
        confidence = random.uniform(0.7, 0.95)

        decision_data = {
            'symbol': features.symbol,
            'action': action,
            'confidence': confidence,
            'reasoning': f"LLM analysis suggests {action.lower()} position based on market conditions",
            'timestamp': time.time(),
            'price': features.last_price
        }

        self.data_store.store_llm_decision(decision_data)
        logger.info(f"🧠 LLM Decision: {action} {features.symbol}")

    async def stop(self):
        """Stop all components."""
        try:
            logger.info("🛑 Shutting down Onnyx UI...")

            # Stop data generation
            if self.data_generator_task:
                self.data_generator_task.cancel()
                try:
                    await self.data_generator_task
                except asyncio.CancelledError:
                    pass

            # Stop execution controller
            if self.execution_controller and hasattr(self.execution_controller, 'account_tracker'):
                await self.execution_controller.account_tracker.stop_monitoring()

            # Stop web server
            if self.runner:
                await self.runner.cleanup()

            logger.info("✅ Onnyx UI shutdown complete")

        except Exception as e:
            logger.error(f"❌ Error during shutdown: {e}")

    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"📡 Received signal {signum}, shutting down...")
        self.shutdown_event.set()

async def main():
    """Main function."""
    runner = OnnxWithDataRunner()

    # Setup signal handlers
    signal.signal(signal.SIGINT, runner.signal_handler)
    signal.signal(signal.SIGTERM, runner.signal_handler)

    try:
        await runner.start()
    except KeyboardInterrupt:
        logger.info("⌨️ Keyboard interrupt received")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
    finally:
        await runner.stop()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("👋 System shutdown complete!")
    except Exception as e:
        logger.error(f"❌ Application error: {e}")
        sys.exit(1)
