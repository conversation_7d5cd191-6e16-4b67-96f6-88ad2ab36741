2025-06-06 11:01:11,428 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-06-06 11:01:11,428 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250606_11_smart_model_integrated.log
2025-06-06 11:01:11,428 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250606_11_smart_model_integrated_events.json
2025-06-06 11:01:11,428 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-06-06 11:01:11,428 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-06-06 11:01:11
2025-06-06 11:01:11,428 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-06-06 11:01:11,428 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]
2025-06-06 11:01:11,428 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-06-06 11:01:11,437 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-06-06 11:01:11,437 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-06-06 11:01:11,437 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-06-06 11:01:11,437 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: True
2025-06-06 11:01:11,437 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-06-06 11:01:11,439 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-06-06 11:01:11,439 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-06-06 11:01:11,439 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-06-06 11:01:11,439 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-06-06 11:01:11,439 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-06-06 11:01:12,608 - strategy.smart_model_integrated - WARNING - [warning:93] - SignalStar client not initialized, social sentiment model disabled
2025-06-06 11:01:12,608 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-06-06 11:01:12,624 - strategy.smart_model_integrated - INFO - [info:89] - Starting orchestrator...
2025-06-06 11:01:12,624 - strategy.smart_model_integrated - INFO - [info:89] - 🔄 Attempting Multi-Exchange connection...
2025-06-06 11:01:14,308 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Connected to Multi-Exchange client
2025-06-06 11:02:06,068 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-06-06 11:02:06,068 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250606_11_smart_model_integrated.log
2025-06-06 11:02:06,068 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250606_11_smart_model_integrated_events.json
2025-06-06 11:02:06,068 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-06-06 11:02:06,068 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-06-06 11:02:06
2025-06-06 11:02:06,068 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-06-06 11:02:06,068 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]
2025-06-06 11:02:06,068 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-06-06 11:02:06,072 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-06-06 11:02:06,072 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-06-06 11:02:06,072 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-06-06 11:02:06,072 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: True
2025-06-06 11:02:06,072 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-06-06 11:02:06,076 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-06-06 11:02:06,076 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-06-06 11:02:06,077 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-06-06 11:02:06,077 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-06-06 11:02:06,077 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-06-06 11:02:07,018 - strategy.smart_model_integrated - WARNING - [warning:93] - SignalStar client not initialized, social sentiment model disabled
2025-06-06 11:02:07,018 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-06-06 11:02:07,022 - strategy.smart_model_integrated - INFO - [info:89] - Starting orchestrator...
2025-06-06 11:02:07,022 - strategy.smart_model_integrated - INFO - [info:89] - 🔄 Attempting Multi-Exchange connection...
2025-06-06 11:02:08,752 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Connected to Multi-Exchange client
2025-06-06 11:02:12,743 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Using Multi-Exchange as primary data source
2025-06-06 11:02:12,744 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Set up message bus subscriptions for Binance fallback data
2025-06-06 11:02:14,048 - strategy.smart_model_integrated - INFO - [info:89] - Loaded 60 historical funding rates for BTC-USDT
2025-06-06 11:02:14,048 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer started successfully
2025-06-06 11:02:14,050 - strategy.smart_model_integrated - INFO - [info:89] - 🧠 LLM Model: Unknown
2025-06-06 11:02:14,050 - strategy.smart_model_integrated - INFO - [info:89] - Position manager started
2025-06-06 11:02:14,050 - strategy.smart_model_integrated - INFO - [info:89] - Starting event loop
2025-06-06 11:02:14,050 - strategy.smart_model_integrated - INFO - [info:89] - Started bus maintenance task
2025-06-06 11:02:14,051 - strategy.smart_model_integrated - INFO - [info:89] - Starting account information update task
2025-06-06 11:02:14,051 - strategy.smart_model_integrated - ERROR - [error:97] - Error updating account information: REST client not initialized
2025-06-06 11:02:14,051 - strategy.smart_model_integrated - INFO - [info:89] - Starting health check task
2025-06-06 11:02:14,051 - strategy.smart_model_integrated - INFO - [info:89] - Starting position monitoring task
2025-06-06 11:02:14,051 - strategy.smart_model_integrated - INFO - [info:89] - Starting funding rate fetching task
2025-06-06 11:02:14,054 - strategy.smart_model_integrated - INFO - [info:89] - Starting open interest fetching task
2025-06-06 11:02:14,059 - strategy.smart_model_integrated - INFO - [info:89] - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
2025-06-06 11:03:08,621 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-06-06 11:03:08,621 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250606_11_smart_model_integrated.log
2025-06-06 11:03:08,622 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250606_11_smart_model_integrated_events.json
2025-06-06 11:03:08,622 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-06-06 11:03:08,623 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-06-06 11:03:08
2025-06-06 11:03:08,624 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-06-06 11:03:08,624 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]
2025-06-06 11:03:08,624 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-06-06 11:03:08,626 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-06-06 11:03:08,627 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-06-06 11:03:08,627 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-06-06 11:03:08,628 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-06-06 11:03:08,628 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-06-06 11:03:08,629 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-06-06 11:03:08,633 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-06-06 11:03:08,633 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-06-06 11:03:08,634 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-06-06 11:03:08,635 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-06-06 11:03:08,635 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-06-06 11:03:08,636 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-06-06 11:03:08,638 - strategy.smart_model_integrated - INFO - [info:89] - Starting orchestrator...
2025-06-06 11:03:08,638 - strategy.smart_model_integrated - INFO - [info:89] - 🔄 Attempting Multi-Exchange connection...
2025-06-06 11:03:10,298 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Connected to Multi-Exchange client
2025-06-06 11:03:10,299 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Using Multi-Exchange as primary data source
2025-06-06 11:03:10,299 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Set up message bus subscriptions for Binance fallback data
2025-06-06 11:03:10,299 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer started successfully
2025-06-06 11:03:10,299 - strategy.smart_model_integrated - INFO - [info:89] - 🧠 LLM Model: Unknown
2025-06-06 11:03:10,303 - strategy.smart_model_integrated - INFO - [info:89] - Position manager started
2025-06-06 11:03:10,303 - strategy.smart_model_integrated - INFO - [info:89] - Starting event loop
2025-06-06 11:03:10,305 - strategy.smart_model_integrated - INFO - [info:89] - Started bus maintenance task
2025-06-06 11:03:10,306 - strategy.smart_model_integrated - INFO - [info:89] - Starting account information update task
2025-06-06 11:03:10,306 - strategy.smart_model_integrated - ERROR - [error:97] - Error updating account information: REST client not initialized
2025-06-06 11:03:10,307 - strategy.smart_model_integrated - INFO - [info:89] - Starting health check task
2025-06-06 11:03:10,307 - strategy.smart_model_integrated - INFO - [info:89] - Starting position monitoring task
2025-06-06 11:03:10,308 - strategy.smart_model_integrated - INFO - [info:89] - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
2025-06-06 11:03:11,678 - strategy.smart_model_integrated - ERROR - [error:97] - Error calculating features for BTC-USDT: 'RSIModel' object has no attribute '_calculate_rsi'
2025-06-06 11:03:12,725 - strategy.smart_model_integrated - ERROR - [error:97] - Error calculating features for BTC-USDT: 'RSIModel' object has no attribute '_calculate_rsi'
2025-06-06 11:03:13,683 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Smart strategy generated signal: BUY BTC-USDT with score 0.760
2025-06-06 11:03:13,683 - strategy.smart_model_integrated - INFO - [info:89] - 📝 Rationale: Smart-Integrated BUY: technical(0.50) → 0.65
2025-06-06 11:03:13,683 - strategy.smart_model_integrated - INFO - [info:89] - Trading disabled, not executing signal: BUY BTC-USDT
2025-06-06 11:03:13,747 - strategy.smart_model_integrated - ERROR - [error:97] - Error calculating features for BTC-USDT: 'RSIModel' object has no attribute '_calculate_rsi'
2025-06-06 11:03:14,759 - strategy.smart_model_integrated - ERROR - [error:97] - Error calculating features for BTC-USDT: 'RSIModel' object has no attribute '_calculate_rsi'
2025-06-06 11:03:16,223 - strategy.smart_model_integrated - ERROR - [error:97] - Error calculating features for BTC-USDT: 'RSIModel' object has no attribute '_calculate_rsi'
2025-06-06 11:03:17,303 - strategy.smart_model_integrated - ERROR - [error:97] - Error calculating features for BTC-USDT: 'RSIModel' object has no attribute '_calculate_rsi'
2025-06-06 11:03:18,341 - strategy.smart_model_integrated - ERROR - [error:97] - Error calculating features for BTC-USDT: 'RSIModel' object has no attribute '_calculate_rsi'
2025-06-06 11:03:18,638 - strategy.smart_model_integrated - INFO - [info:89] - Event loop cancelled
2025-06-06 11:03:18,638 - strategy.smart_model_integrated - INFO - [info:89] - Stopping orchestrator...
2025-06-06 11:03:19,354 - strategy.smart_model_integrated - INFO - [info:89] - Position manager stopped
2025-06-06 11:03:19,354 - strategy.smart_model_integrated - INFO - [info:89] - Enhanced LLM Consumer stopped
2025-06-06 11:03:28,024 - strategy.smart_model_integrated - INFO - [info:89] - Message bus closed
2025-06-06 11:03:28,025 - strategy.smart_model_integrated - INFO - [info:89] - Orchestrator stopped
2025-06-06 11:04:51,047 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-06-06 11:04:51,048 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250606_11_smart_model_integrated.log
2025-06-06 11:04:51,048 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250606_11_smart_model_integrated_events.json
2025-06-06 11:04:51,048 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-06-06 11:04:51,048 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-06-06 11:04:51
2025-06-06 11:04:51,048 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-06-06 11:04:51,048 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]
2025-06-06 11:04:51,048 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-06-06 11:05:03,315 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-06-06 11:05:03,315 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250606_11_smart_model_integrated.log
2025-06-06 11:05:03,315 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250606_11_smart_model_integrated_events.json
2025-06-06 11:05:03,315 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-06-06 11:05:03,315 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-06-06 11:05:03
2025-06-06 11:05:03,315 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-06-06 11:05:03,315 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]
2025-06-06 11:05:03,318 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-06-06 11:06:03,323 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-06-06 11:06:03,323 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250606_11_smart_model_integrated.log
2025-06-06 11:06:03,324 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250606_11_smart_model_integrated_events.json
2025-06-06 11:06:03,324 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-06-06 11:06:03,324 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-06-06 11:06:03
2025-06-06 11:06:03,324 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-06-06 11:06:03,325 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]
2025-06-06 11:06:03,325 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-06-06 11:06:03,326 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-06-06 11:06:03,327 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-06-06 11:06:03,327 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-06-06 11:06:03,327 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: True
2025-06-06 11:06:03,327 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-06-06 11:06:03,331 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-06-06 11:06:03,331 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-06-06 11:06:03,331 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-06-06 11:06:03,332 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-06-06 11:06:03,332 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-06-06 11:06:04,594 - strategy.smart_model_integrated - WARNING - [warning:93] - SignalStar client not initialized, social sentiment model disabled
2025-06-06 11:06:04,663 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-06-06 11:06:04,668 - strategy.smart_model_integrated - INFO - [info:89] - Starting orchestrator...
2025-06-06 11:06:04,671 - strategy.smart_model_integrated - INFO - [info:89] - 🔄 Attempting Multi-Exchange connection...
2025-06-06 11:06:06,399 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Connected to Multi-Exchange client
