#!/usr/bin/env python3
"""
Epinnox V6 - Standalone AI Strategy Tuner
Main Application Startup
"""

import asyncio
import logging
import signal
import sys
import yaml
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from feeds.htx_ws_client import HTXWebSocketClient
from feeds.binance_ws_client import BinanceWebSocketClient
from feeds.trade_parser import TradeParser
from storage.live_store import LiveDataStore
from models.smart_strategy import SmartStrategy
from models.llm_integration import LLMIntegration
from ui.ai_strategy_tuner import AIStrategyTunerDashboard

# Configure logging with Windows Unicode support
import sys
import os

def setup_logging():
    """Setup logging with proper Unicode support for Windows."""
    # Ensure logs directory exists
    os.makedirs('logs', exist_ok=True)

    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Console handler with UTF-8 encoding
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(detailed_formatter)

    # File handler with UTF-8 encoding
    file_handler = logging.FileHandler('logs/epinnox_v6.log', encoding='utf-8')
    file_handler.setFormatter(detailed_formatter)

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)

    # Set console encoding to UTF-8 if possible
    try:
        if sys.platform == 'win32':
            # Try to set console to UTF-8 mode
            os.system('chcp 65001 > nul 2>&1')
    except:
        pass

# Setup logging
setup_logging()

logger = logging.getLogger(__name__)

class EpinnoxV6Application:
    """
    Main application class for Epinnox V6 AI Strategy Tuner.
    Coordinates all components for real-time trading analysis.
    """

    def __init__(self, config_path: str = 'config/strategy.yaml'):
        self.config_path = config_path
        self.config = None
        self.running = False

        # Core components
        self.data_store = None
        self.trade_parser = None
        self.ws_client = None
        self.backup_ws_client = None
        self.active_exchange = None
        self.strategy_engine = None
        self.llm_integration = None
        self.dashboard = None

        logger.info("[INIT] Epinnox V6 AI Strategy Tuner initializing...")

    async def load_config(self):
        """Load configuration from YAML file."""
        try:
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)

            logger.info(f"[OK] Configuration loaded from {self.config_path}")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to load configuration: {e}")
            return False

    async def initialize_components(self):
        """Initialize all application components."""
        try:
            logger.info("🔧 Initializing components...")

            # Initialize data store
            self.data_store = LiveDataStore(self.config)
            await self.data_store.start_background_tasks()
            logger.info("✅ Data store initialized")

            # Initialize trade parser
            self.trade_parser = TradeParser(self.config)
            logger.info("✅ Trade parser initialized")

            # Initialize WebSocket clients (HTX primary, Binance backup)
            self.ws_client = HTXWebSocketClient(self.config)
            self.backup_ws_client = BinanceWebSocketClient(self.config)

            # Set up data handlers for both clients
            for client in [self.ws_client, self.backup_ws_client]:
                client.set_trade_handler(self.handle_trade_data)
                client.set_depth_handler(self.handle_depth_data)
                client.set_error_handler(self.handle_ws_error)

            logger.info("✅ WebSocket clients initialized (HTX + Binance backup)")

            # Initialize strategy engine
            self.strategy_engine = SmartStrategy(self.config, self.data_store)
            logger.info("✅ Strategy engine initialized")

            # Initialize LLM integration
            self.llm_integration = LLMIntegration(self.config, self.data_store)
            await self.llm_integration.start_background_processing()
            logger.info("✅ LLM integration initialized")

            # Initialize dashboard
            self.dashboard = AIStrategyTunerDashboard(self.config, self.data_store)
            logger.info("✅ Dashboard initialized")

            return True

        except Exception as e:
            logger.error(f"❌ Failed to initialize components: {e}")
            return False

    async def handle_trade_data(self, trade_data):
        """Handle incoming trade data from WebSocket."""
        try:
            # Process trade through parser
            features = await self.trade_parser.process_trade(trade_data)

            if features:
                # Store features
                self.data_store.store_features(features)

                # Process through strategy engine
                signal = await self.strategy_engine.process_features(features)

                if signal:
                    # Process through LLM if enabled
                    if self.llm_integration.enabled:
                        await self.llm_integration.process_signal(signal, features)

        except Exception as e:
            logger.error(f"Error handling trade data: {e}")

    async def handle_depth_data(self, depth_data):
        """Handle incoming order book depth data."""
        try:
            await self.trade_parser.process_depth(depth_data)
        except Exception as e:
            logger.error(f"Error handling depth data: {e}")

    async def handle_ws_error(self, error):
        """Handle WebSocket errors."""
        logger.error(f"WebSocket error: {error}")

    async def start_websocket(self):
        """Start WebSocket connection and data streaming with fallback."""
        try:
            # Try HTX first
            logger.info("🌐 Connecting to HTX WebSocket...")

            if await self.ws_client.connect():
                logger.info("✅ HTX WebSocket connected successfully")
                self.active_exchange = "HTX"

                # Start listening in background
                asyncio.create_task(self.ws_client.listen())
                return True
            else:
                logger.warning("⚠️ HTX connection failed, trying Binance backup...")

                # Try Binance backup
                if await self.backup_ws_client.connect():
                    logger.info("✅ Binance WebSocket connected successfully")
                    self.active_exchange = "Binance"

                    # Start listening in background
                    asyncio.create_task(self.backup_ws_client.listen())
                    return True
                else:
                    logger.error("❌ Both HTX and Binance connections failed")
                    # Continue without WebSocket for testing
                    logger.info("🔄 Continuing without WebSocket for testing purposes")
                    self.active_exchange = "None (Testing Mode)"
                    return True

        except Exception as e:
            logger.error(f"❌ WebSocket startup error: {e}")
            # Continue without WebSocket for testing
            logger.info("🔄 Continuing without WebSocket for testing purposes")
            self.active_exchange = "None (Testing Mode)"
            return True

    async def start_dashboard(self):
        """Start the web dashboard."""
        try:
            host = self.config['dashboard']['host']
            port = self.config['dashboard']['port']

            logger.info(f"🌐 Starting dashboard on http://{host}:{port}")

            # Set strategy running status
            self.dashboard.set_strategy_running(True)

            # Start dashboard server
            await self.dashboard.start_server(host, port)

            logger.info("✅ Dashboard started successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to start dashboard: {e}")
            return False

    async def start(self):
        """Start the complete application."""
        try:
            logger.info("🚀 Starting Epinnox V6 AI Strategy Tuner...")

            # Load configuration
            if not await self.load_config():
                return False

            # Initialize components
            if not await self.initialize_components():
                return False

            # Start WebSocket connection
            if not await self.start_websocket():
                return False

            # Start dashboard
            if not await self.start_dashboard():
                return False

            self.running = True

            logger.info("🎉 Epinnox V6 AI Strategy Tuner started successfully!")
            logger.info("=" * 60)
            logger.info("🌐 Dashboard: http://localhost:8086")
            logger.info(f"📊 Market data source: {self.active_exchange}")
            logger.info("🤖 AI model analysis and LLM integration")
            logger.info("📈 Live trading signal generation")
            logger.info("=" * 60)

            return True

        except Exception as e:
            logger.error(f"❌ Failed to start application: {e}")
            return False

    async def stop(self):
        """Stop the application gracefully."""
        logger.info("🛑 Stopping Epinnox V6 AI Strategy Tuner...")

        self.running = False

        try:
            # Disconnect WebSockets
            if self.ws_client:
                await self.ws_client.disconnect()
            if self.backup_ws_client:
                await self.backup_ws_client.disconnect()

            # Shutdown data store
            if self.data_store:
                self.data_store.shutdown()

            logger.info("✅ Application stopped gracefully")

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

    async def run_forever(self):
        """Run the application until interrupted."""
        if not await self.start():
            return False

        try:
            # Keep running until interrupted
            while self.running:
                await asyncio.sleep(1)

                # Optional: Add periodic health checks here

        except KeyboardInterrupt:
            logger.info("Received interrupt signal")
        except Exception as e:
            logger.error(f"Runtime error: {e}")
        finally:
            await self.stop()

        return True

async def main():
    """Main entry point."""
    # Ensure logs directory exists
    Path('logs').mkdir(exist_ok=True)

    # Create and run application
    app = EpinnoxV6Application()

    # Setup signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}")
        app.running = False

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Run application
    success = await app.run_forever()

    if success:
        logger.info("🎯 Epinnox V6 AI Strategy Tuner completed successfully")
    else:
        logger.error("❌ Epinnox V6 AI Strategy Tuner failed to start")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Application interrupted by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
