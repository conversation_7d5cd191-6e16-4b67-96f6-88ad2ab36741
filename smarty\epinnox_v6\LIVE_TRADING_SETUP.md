# 🚀 Live Trading Setup Guide - HTX USDT-M Futures

## ⚠️ CRITICAL SAFETY NOTICE
**YOU ARE ABOUT TO TRADE WITH REAL MONEY. PLEASE READ CAREFULLY.**

This system is configured for live trading with a $5 account on HTX USDT-M Futures using DOGE/USDT:USDT. All safety mechanisms are in place, but **you are responsible for monitoring and controlling your trades**.

## 📋 Prerequisites

### 1. HTX Account Setup
- Create an HTX account at https://www.htx.com
- Complete KYC verification
- Enable USDT-M Futures trading
- Deposit $5 USD to your futures account
- Create API credentials with futures trading permissions

### 2. API Permissions Required
- **Read**: Account information, position information
- **Trade**: Place orders, cancel orders
- **Futures**: USDT-M futures trading access

## 🔑 API Configuration

### 1. Get HTX API Credentials
1. Log into HTX account
2. Go to API Management
3. Create new API key with futures trading permissions
4. **IMPORTANT**: Restrict IP access to your trading machine
5. Note down:
   - API Key
   - API Secret
   - Passphrase (if required)

### 2. Configure Environment Variables
Edit the `.env` file in the `epinnox_v6` directory:

```bash
# HTX API Configuration for Live Trading
HTX_API_KEY=your_actual_api_key_here
HTX_API_SECRET=your_actual_api_secret_here
HTX_PASSPHRASE=your_passphrase_if_required

# Trading Configuration
LIVE_TRADING=true
ACCOUNT_BALANCE=5.0
MAX_POSITION_SIZE=4.0
TRADING_SYMBOL=DOGE-USDT
```

## 🛡️ Safety Configuration

The system is pre-configured with conservative safety settings:

### Risk Management
- **Account Balance**: $5.00
- **Max Position Size**: $4.00 (80% of account)
- **Min Position Size**: $0.50
- **Stop Loss**: 5% (automatic)
- **Take Profit**: 10% (automatic)
- **Daily Loss Limit**: $2.50 (50% of account)
- **Max Daily Trades**: 10
- **Consecutive Loss Limit**: 3 trades

### Circuit Breakers
- **Emergency Stop**: Triggered at 50% account loss
- **Auto-disable**: After 3 consecutive losses
- **Position Limits**: Maximum $4 per trade
- **Leverage**: 1x (no leverage for safety)

## 🧪 Testing Before Live Trading

### 1. Run Connection Test
```bash
python test_live_trading.py
```

Expected output:
- ✅ Live Execution: PASS
- ✅ Safety Mechanisms: PASS
- ❌ HTX Connection: FAIL (until API keys are set)

### 2. Set Real API Credentials
After setting real HTX API credentials, run test again:
```bash
python test_live_trading.py
```

All tests should pass:
- ✅ HTX Connection: PASS
- ✅ Live Execution: PASS
- ✅ Safety Mechanisms: PASS

## 🚀 Starting Live Trading

### 1. Start the System
```bash
python run_epinnox_v6_clean.py
```

### 2. Monitor Dashboard
Open http://localhost:8086 in your browser to monitor:
- Real-time market data
- AI trading signals
- LLM decisions
- Live trade executions
- Position status
- P&L tracking

### 3. Watch for Live Trades
Look for log messages like:
```
🚀 LIVE TRADE EXECUTED: DOGE-USDT LONG 1.25 @ $0.0823
```

## 📊 Trading Behavior

### What the System Will Do
1. **Monitor DOGE/USDT market data** via Binance WebSocket (HTX backup)
2. **Generate AI signals** using RSI, VWAP, OrderFlow, and Volatility models
3. **Make LLM decisions** using phi-3.1 mini model
4. **Execute live trades** through HTX USDT-M Futures API
5. **Manage positions** with automatic stop-loss and take-profit
6. **Self-tune parameters** using Phase 7 autonomy loop

### Position Sizing
- **Base Position**: $1.00
- **Confidence Multiplier**: 1.5x (conservative)
- **Conviction Multiplier**: 1.2x (conservative)
- **Maximum Position**: $4.00 (capped)

### Example Trade
- Signal: DOGE-USDT LONG (90% confidence, 4-star conviction)
- Position Size: $1.00 × 1.5 × 1.2 = $1.80
- Entry: Market order at current price
- Stop Loss: -5% from entry
- Take Profit: +10% from entry

## ⚠️ Important Warnings

### 1. Start Small
- Begin with minimum position sizes
- Monitor first few trades closely
- Gradually increase confidence as system proves reliable

### 2. Monitor Continuously
- Keep dashboard open during trading hours
- Watch for unusual behavior or errors
- Be ready to manually intervene if needed

### 3. Emergency Procedures
- **Stop Trading**: Press Ctrl+C to stop the system
- **Close Positions**: Use HTX web interface to manually close positions
- **Disable API**: Revoke API permissions in HTX if needed

### 4. Risk Awareness
- **Cryptocurrency trading is highly risky**
- **You can lose your entire $5 investment**
- **Past performance does not guarantee future results**
- **The AI system is experimental and may make mistakes**

## 🔧 Troubleshooting

### Common Issues

1. **"HTX Connection Failed"**
   - Check API credentials in .env file
   - Verify HTX API permissions
   - Check internet connection
   - Try VPN if geo-blocked

2. **"Position Size Too Small"**
   - Check account balance
   - Verify minimum position requirements
   - Adjust base_position_size in config

3. **"Order Failed"**
   - Check account balance
   - Verify trading permissions
   - Check symbol format (DOGE-USDT)
   - Review HTX API status

### Log Files
- **Main Log**: `logs/epinnox_v6.log`
- **Signal Log**: `logs/real_signals.jsonl`
- **Error Log**: `logs/errors.log`

## 📞 Support

If you encounter issues:
1. Check log files for error details
2. Verify API credentials and permissions
3. Test with simulation mode first
4. Review HTX API documentation
5. Check system requirements and dependencies

## 🎯 Success Metrics

Monitor these metrics for system performance:
- **Win Rate**: Target >50%
- **Average P&L**: Positive over time
- **Execution Quality**: >90%
- **System Uptime**: >95%
- **Risk Compliance**: No safety violations

## 🔄 Next Steps After Setup

1. **Monitor Performance**: Track trades for first week
2. **Adjust Parameters**: Fine-tune based on results
3. **Scale Gradually**: Increase position sizes if profitable
4. **Enhance Strategy**: Add more models or improve logic
5. **Automate Further**: Implement additional autonomy features

---

**Remember: This is experimental trading software. Trade responsibly and never risk more than you can afford to lose.**
