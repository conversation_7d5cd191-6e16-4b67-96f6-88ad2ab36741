#!/usr/bin/env python3
"""
Model Tuner
Auto-adjusts parameters using insights from autonomy_tracker
"""

import logging
import time
import json
import os
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

class TuningAction(Enum):
    INCREASE_WEIGHT = "increase_weight"
    DECREASE_WEIGHT = "decrease_weight"
    ADJUST_THRESHOLD = "adjust_threshold"
    MODIFY_SENSITIVITY = "modify_sensitivity"
    DISABLE_MODEL = "disable_model"
    ENABLE_MODEL = "enable_model"

@dataclass
class TuningEvent:
    """Record of a tuning adjustment."""
    timestamp: float
    parameter: str
    old_value: float
    new_value: float
    action: TuningAction
    reason: str
    source: str  # 'auto', 'llm', 'manual'
    impact_score: float
    success_probability: float

@dataclass
class ModelWeights:
    """Current model weights configuration."""
    rsi: float = 0.25
    vwap: float = 0.25
    orderflow: float = 0.25
    volatility: float = 0.25

    def to_dict(self) -> Dict[str, float]:
        return asdict(self)

    def from_dict(self, data: Dict[str, float]):
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

@dataclass
class TradingThresholds:
    """Current trading thresholds configuration."""
    buy_threshold: float = 0.7
    sell_threshold: float = 0.7
    confidence_threshold: float = 0.6
    conviction_threshold: int = 3

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

    def from_dict(self, data: Dict[str, Any]):
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

class ModelTuner:
    """
    Auto-adjusts model parameters using insights from autonomy tracker.
    Supports static rule tuning and LLM-based proposals.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.tuning_config = config.get('model_tuning', {})

        # Storage configuration
        self.data_dir = self.tuning_config.get('data_dir', 'data/autonomy')
        self.weights_file = os.path.join(self.data_dir, 'model_weights.json')
        self.thresholds_file = os.path.join(self.data_dir, 'trading_thresholds.json')
        self.tuning_history_file = os.path.join(self.data_dir, 'tuning_history.json')

        # Ensure data directory exists
        os.makedirs(self.data_dir, exist_ok=True)

        # Current configuration
        self.model_weights = ModelWeights()
        self.trading_thresholds = TradingThresholds()
        self.tuning_history: List[TuningEvent] = []

        # Tuning parameters
        self.max_weight_change = self.tuning_config.get('max_weight_change', 0.1)
        self.max_threshold_change = self.tuning_config.get('max_threshold_change', 0.05)
        self.min_weight = self.tuning_config.get('min_weight', 0.05)
        self.max_weight = self.tuning_config.get('max_weight', 0.5)
        self.tuning_cooldown = self.tuning_config.get('tuning_cooldown', 3600)  # 1 hour

        # Performance tracking
        self.last_tuning_time = 0
        self.tuning_effectiveness = {}

        # Load existing configuration
        self._load_configuration()

        logger.info("Model Tuner initialized with current weights and thresholds")

    def apply_tuning_recommendations(self, recommendations: List[Dict[str, Any]],
                                   source: str = 'auto') -> List[TuningEvent]:
        """Apply tuning recommendations and return executed events."""
        try:
            executed_events = []
            current_time = time.time()

            # Check cooldown
            if current_time - self.last_tuning_time < self.tuning_cooldown:
                logger.debug(f"Tuning cooldown active, skipping recommendations")
                return []

            for recommendation in recommendations:
                event = self._apply_single_recommendation(recommendation, source, current_time)
                if event:
                    executed_events.append(event)
                    self.tuning_history.append(event)

            if executed_events:
                self.last_tuning_time = current_time
                self._save_configuration()
                logger.info(f"Applied {len(executed_events)} tuning adjustments")

            return executed_events

        except Exception as e:
            logger.error(f"Error applying tuning recommendations: {e}")
            return []

    def _apply_single_recommendation(self, recommendation: Dict[str, Any],
                                   source: str, timestamp: float) -> Optional[TuningEvent]:
        """Apply a single tuning recommendation."""
        try:
            rec_type = recommendation.get('type')
            target = recommendation.get('target')
            action = recommendation.get('action')
            suggested_value = recommendation.get('suggested_value')
            reason = recommendation.get('reason', 'No reason provided')

            if rec_type == 'model_weight':
                return self._adjust_model_weight(target, action, suggested_value, reason, source, timestamp)
            elif rec_type == 'confidence_threshold':
                return self._adjust_threshold(target, suggested_value, reason, source, timestamp)
            elif rec_type == 'regime_sensitivity':
                return self._adjust_regime_sensitivity(target, action, suggested_value, reason, source, timestamp)
            else:
                logger.warning(f"Unknown recommendation type: {rec_type}")
                return None

        except Exception as e:
            logger.error(f"Error applying single recommendation: {e}")
            return None

    def _adjust_model_weight(self, model_name: str, action: str, suggested_value: Optional[float],
                           reason: str, source: str, timestamp: float) -> Optional[TuningEvent]:
        """Adjust model weight based on recommendation."""
        try:
            # Get current weight
            current_weight = getattr(self.model_weights, model_name.lower(), None)
            if current_weight is None:
                logger.warning(f"Unknown model: {model_name}")
                return None

            # Calculate new weight
            if suggested_value is not None:
                new_weight = suggested_value
            elif action == 'increase':
                new_weight = min(current_weight + self.max_weight_change, self.max_weight)
            elif action == 'decrease':
                new_weight = max(current_weight - self.max_weight_change, self.min_weight)
            else:
                logger.warning(f"Unknown action: {action}")
                return None

            # Apply weight change
            if abs(new_weight - current_weight) < 0.01:
                logger.debug(f"Weight change too small for {model_name}")
                return None

            setattr(self.model_weights, model_name.lower(), new_weight)

            # Normalize weights to sum to 1.0
            self._normalize_weights()

            # Create tuning event
            tuning_action = TuningAction.INCREASE_WEIGHT if new_weight > current_weight else TuningAction.DECREASE_WEIGHT

            return TuningEvent(
                timestamp=timestamp,
                parameter=f"{model_name}_weight",
                old_value=current_weight,
                new_value=new_weight,
                action=tuning_action,
                reason=reason,
                source=source,
                impact_score=abs(new_weight - current_weight),
                success_probability=0.7  # Default probability
            )

        except Exception as e:
            logger.error(f"Error adjusting model weight: {e}")
            return None

    def _adjust_threshold(self, threshold_name: str, suggested_value: float,
                         reason: str, source: str, timestamp: float) -> Optional[TuningEvent]:
        """Adjust trading threshold based on recommendation."""
        try:
            # Get current threshold
            current_threshold = getattr(self.trading_thresholds, threshold_name, None)
            if current_threshold is None:
                logger.warning(f"Unknown threshold: {threshold_name}")
                return None

            # Calculate new threshold
            new_threshold = suggested_value

            # Apply bounds checking
            if threshold_name in ['buy_threshold', 'sell_threshold', 'confidence_threshold']:
                new_threshold = max(0.1, min(0.95, new_threshold))
            elif threshold_name == 'conviction_threshold':
                new_threshold = max(1, min(5, int(new_threshold)))

            # Apply threshold change
            if abs(new_threshold - current_threshold) < 0.01:
                logger.debug(f"Threshold change too small for {threshold_name}")
                return None

            setattr(self.trading_thresholds, threshold_name, new_threshold)

            # Create tuning event
            return TuningEvent(
                timestamp=timestamp,
                parameter=threshold_name,
                old_value=current_threshold,
                new_value=new_threshold,
                action=TuningAction.ADJUST_THRESHOLD,
                reason=reason,
                source=source,
                impact_score=abs(new_threshold - current_threshold),
                success_probability=0.6
            )

        except Exception as e:
            logger.error(f"Error adjusting threshold: {e}")
            return None

    def _adjust_regime_sensitivity(self, regime: str, action: str, suggested_value: float,
                                 reason: str, source: str, timestamp: float) -> Optional[TuningEvent]:
        """Adjust regime sensitivity (placeholder for regime-specific tuning)."""
        try:
            # This would adjust regime-specific multipliers
            # For now, we'll create a placeholder event

            return TuningEvent(
                timestamp=timestamp,
                parameter=f"{regime}_sensitivity",
                old_value=1.0,  # Default sensitivity
                new_value=suggested_value,
                action=TuningAction.MODIFY_SENSITIVITY,
                reason=reason,
                source=source,
                impact_score=abs(suggested_value - 1.0),
                success_probability=0.5
            )

        except Exception as e:
            logger.error(f"Error adjusting regime sensitivity: {e}")
            return None

    def _normalize_weights(self):
        """Normalize model weights to sum to 1.0."""
        try:
            weights_dict = self.model_weights.to_dict()
            total_weight = sum(weights_dict.values())

            if total_weight > 0:
                for key in weights_dict:
                    normalized_weight = weights_dict[key] / total_weight
                    setattr(self.model_weights, key, normalized_weight)

        except Exception as e:
            logger.error(f"Error normalizing weights: {e}")

    def get_current_configuration(self) -> Dict[str, Any]:
        """Get current model configuration."""
        try:
            return {
                'model_weights': self.model_weights.to_dict(),
                'trading_thresholds': self.trading_thresholds.to_dict(),
                'last_tuning_time': self.last_tuning_time,
                'tuning_history_count': len(self.tuning_history)
            }

        except Exception as e:
            logger.error(f"Error getting current configuration: {e}")
            return {}

    def get_recent_tuning_events(self, hours: int = 24) -> List[TuningEvent]:
        """Get recent tuning events."""
        try:
            cutoff_time = time.time() - (hours * 3600)
            return [event for event in self.tuning_history if event.timestamp >= cutoff_time]

        except Exception as e:
            logger.error(f"Error getting recent tuning events: {e}")
            return []

    def revert_last_tuning(self) -> bool:
        """Revert the last tuning adjustment."""
        try:
            if not self.tuning_history:
                logger.warning("No tuning history to revert")
                return False

            last_event = self.tuning_history[-1]

            # Revert the change
            if 'weight' in last_event.parameter:
                model_name = last_event.parameter.replace('_weight', '')
                setattr(self.model_weights, model_name, last_event.old_value)
                self._normalize_weights()
            elif last_event.parameter in ['buy_threshold', 'sell_threshold', 'confidence_threshold', 'conviction_threshold']:
                setattr(self.trading_thresholds, last_event.parameter, last_event.old_value)

            # Remove from history
            self.tuning_history.pop()

            # Save configuration
            self._save_configuration()

            logger.info(f"Reverted tuning: {last_event.parameter} back to {last_event.old_value}")
            return True

        except Exception as e:
            logger.error(f"Error reverting last tuning: {e}")
            return False

    def _load_configuration(self):
        """Load model configuration from disk."""
        try:
            # Load model weights
            if os.path.exists(self.weights_file):
                with open(self.weights_file, 'r') as f:
                    weights_data = json.load(f)
                    self.model_weights.from_dict(weights_data)

            # Load trading thresholds
            if os.path.exists(self.thresholds_file):
                with open(self.thresholds_file, 'r') as f:
                    thresholds_data = json.load(f)
                    self.trading_thresholds.from_dict(thresholds_data)

            # Load tuning history
            if os.path.exists(self.tuning_history_file):
                with open(self.tuning_history_file, 'r') as f:
                    history_data = json.load(f)
                    self.tuning_history = [TuningEvent(**event) for event in history_data]

        except Exception as e:
            logger.error(f"Error loading model configuration: {e}")

    def _save_configuration(self):
        """Save model configuration to disk."""
        try:
            # Save model weights
            with open(self.weights_file, 'w') as f:
                json.dump(self.model_weights.to_dict(), f, indent=2)

            # Save trading thresholds
            with open(self.thresholds_file, 'w') as f:
                json.dump(self.trading_thresholds.to_dict(), f, indent=2)

            # Save tuning history (keep last 100 events)
            recent_history = self.tuning_history[-100:]
            history_data = [asdict(event) for event in recent_history]
            with open(self.tuning_history_file, 'w') as f:
                json.dump(history_data, f, indent=2)

        except Exception as e:
            logger.error(f"Error saving model configuration: {e}")

    def get_tuning_summary(self) -> Dict[str, Any]:
        """Get comprehensive tuning summary."""
        try:
            recent_events = self.get_recent_tuning_events(24)

            return {
                'current_weights': self.model_weights.to_dict(),
                'current_thresholds': self.trading_thresholds.to_dict(),
                'recent_tuning_count': len(recent_events),
                'last_tuning_time': self.last_tuning_time,
                'recent_events': [asdict(event) for event in recent_events[-5:]],  # Last 5 events
                'total_tuning_events': len(self.tuning_history),
                'cooldown_remaining': max(0, self.tuning_cooldown - (time.time() - self.last_tuning_time))
            }

        except Exception as e:
            logger.error(f"Error getting tuning summary: {e}")
            return {}
