#!/usr/bin/env python3
"""
Smart AI Strategy Engine for Real-Time Trading Analysis
Epinnox V6 - Standalone AI Strategy Tuner
"""

import logging
import time
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import numpy as np

from feeds.trade_parser import MarketFeatures
from storage.live_store import LiveDataStore
from autonomy.multi_timeframe_analyzer import MultiTimeframeAnalyzer, MultiTimeframeAnalysis
from autonomy.strategy_evaluator import StrategyEvaluator, SignalEvaluation
from autonomy.strategy_tracker import StrategyTracker, TradeResult

logger = logging.getLogger(__name__)

@dataclass
class ModelOutput:
    """AI model output structure."""
    model_name: str
    symbol: str
    action: str  # 'BUY', 'SELL', 'WAIT'
    confidence: float
    signal: str
    value: Optional[float] = None
    timestamp: float = None

@dataclass
class TradingSignal:
    """Trading signal structure."""
    symbol: str
    action: str  # 'LONG', 'SHORT', 'WAIT'
    confidence: float
    score: float
    reasoning: str
    model_contributions: Dict[str, float]
    timestamp: float
    price: Optional[float] = None

class SmartStrategy:
    """
    AI-powered trading strategy engine.
    Processes market features through multiple AI models and generates trading signals.
    """

    def __init__(self, config: Dict[str, Any], data_store: LiveDataStore):
        self.config = config
        self.data_store = data_store

        # Model configuration
        self.model_weights = config['models']['weights']
        self.signal_config = config['signals']

        # Signal generation state
        self.last_signal_time = {}
        self.signal_cooldown = config['signals']['signal_cooldown']

        # Model instances
        self.models = {
            'rsi': RSIModel(config['models']['rsi']),
            'vwap': VWAPModel(config['models']['vwap']),
            'orderflow': OrderflowModel(config['models']['orderflow']),
            'volatility': VolatilityModel(config['models']['volatility'])
        }

        # 🧠 STRATEGIC INTELLIGENCE MODULES
        self.mta_analyzer = MultiTimeframeAnalyzer(config)
        self.strategy_evaluator = StrategyEvaluator(config)
        self.strategy_tracker = StrategyTracker(config)

        # Exchange client for MTA (will be injected)
        self.exchange_client = None

        # Account tracker for real balance data (will be injected)
        self.account_tracker = None

        # Enhanced signal tracking
        self.mta_context_cache = {}
        self.last_mta_analysis = {}

        logger.info("🧠 Smart Strategy Engine initialized with Strategic Intelligence")

    async def process_features(self, features: MarketFeatures) -> Optional[TradingSignal]:
        """
        🧠 ENHANCED: Process market features through AI models with strategic intelligence.

        Args:
            features: Market features from trade parser

        Returns:
            TradingSignal if conditions are met, None otherwise
        """
        try:
            symbol = features.symbol
            current_time = time.time()

            # Check signal cooldown
            if self._is_in_cooldown(symbol, current_time):
                return None

            # 🧱 PHASE 1: Multi-Timeframe Analysis
            mta_analysis = None
            if self.exchange_client:
                mta_analysis = await self.mta_analyzer.analyze_symbol(symbol, self.exchange_client)
                self.last_mta_analysis[symbol] = mta_analysis

            # Run all AI models
            model_outputs = await self._run_models(features)

            # Store model outputs
            for output in model_outputs:
                self.data_store.store_model_output(
                    symbol,
                    output.model_name,
                    {
                        'action': output.action,
                        'confidence': output.confidence,
                        'signal': output.signal,
                        'value': output.value
                    }
                )

            # Generate ensemble signal
            signal = await self._generate_ensemble_signal(features, model_outputs)

            if signal and signal.confidence >= self.signal_config['confidence_threshold']:

                # 🧠 PHASE 2: Strategy Evaluation
                if mta_analysis:
                    # 💰 Get REAL account data for evaluation
                    account_data = self._get_real_account_data()

                    # Get model outputs for evaluation
                    model_output_dict = {output.model_name: output for output in model_outputs}

                    # Evaluate signal with strategic intelligence
                    evaluation = await self.strategy_evaluator.evaluate_signal(
                        {
                            'symbol': signal.symbol,
                            'action': signal.action,
                            'confidence': signal.confidence,
                            'model_contributions': signal.model_contributions
                        },
                        mta_analysis,
                        account_data,
                        model_output_dict
                    )

                    # Apply evaluation results
                    if evaluation.decision.value == 'reject':
                        logger.info(f"🚫 Signal rejected by evaluator: {evaluation.reasoning}")
                        return None
                    elif evaluation.decision.value == 'defer':
                        logger.info(f"⏸️ Signal deferred by evaluator: {evaluation.reasoning}")
                        return None

                    # Apply confidence and size adjustments
                    signal.confidence *= evaluation.confidence_adjustment

                    # Add evaluation metadata to signal
                    signal.reasoning += f" | Eval: {evaluation.reasoning}"

                # Update last signal time
                self.last_signal_time[symbol] = current_time

                # Store signal with enhanced data
                signal_data = {
                    'symbol': signal.symbol,
                    'action': signal.action,
                    'confidence': signal.confidence,
                    'score': signal.score,
                    'reasoning': signal.reasoning,
                    'model_contributions': signal.model_contributions,
                    'timestamp': signal.timestamp,
                    'price': signal.price,
                    'pnl': 0.0,  # Will be updated when position is closed
                    'mta_alignment_score': mta_analysis.alignment_score if mta_analysis else 0.5,
                    'execution_recommendation': mta_analysis.execution_recommendation if mta_analysis else 'normal'
                }

                self.data_store.store_signal(signal_data)

                logger.info(f"🎯 Enhanced signal generated: {symbol} - {signal.action} "
                           f"(confidence: {signal.confidence:.2%}, MTA: {mta_analysis.alignment_score if mta_analysis else 0.5:.2f})")
                return signal

            return None

        except Exception as e:
            logger.error(f"Error processing features: {e}")
            return None

    async def _run_models(self, features: MarketFeatures) -> List[ModelOutput]:
        """Run all AI models on market features."""
        outputs = []

        for model_name, model in self.models.items():
            try:
                output = await model.analyze(features)
                if output:
                    outputs.append(output)
            except Exception as e:
                logger.error(f"Error running {model_name} model: {e}")

        return outputs

    async def _generate_ensemble_signal(self, features: MarketFeatures, model_outputs: List[ModelOutput]) -> Optional[TradingSignal]:
        """Generate ensemble trading signal from model outputs."""
        if not model_outputs:
            return None

        # Calculate weighted scores
        total_score = 0.0
        total_weight = 0.0
        model_contributions = {}

        for output in model_outputs:
            weight = self.model_weights.get(output.model_name, 1.0)

            # Convert action to score
            action_score = self._action_to_score(output.action, output.confidence)
            weighted_score = action_score * weight

            total_score += weighted_score
            total_weight += weight
            model_contributions[output.model_name] = weighted_score

        if total_weight == 0:
            return None

        # Normalize score
        ensemble_score = total_score / total_weight

        # Determine action and confidence
        action, confidence = self._score_to_action(ensemble_score)

        # Generate reasoning
        reasoning = self._generate_reasoning(features, model_outputs, ensemble_score)

        return TradingSignal(
            symbol=features.symbol,
            action=action,
            confidence=confidence,
            score=ensemble_score,
            reasoning=reasoning,
            model_contributions=model_contributions,
            timestamp=time.time(),
            price=features.last_price
        )

    def _action_to_score(self, action: str, confidence: float) -> float:
        """Convert model action to numerical score."""
        action_map = {
            'BUY': 1.0,
            'LONG': 1.0,
            'SELL': -1.0,
            'SHORT': -1.0,
            'WAIT': 0.0,
            'NEUTRAL': 0.0
        }

        base_score = action_map.get(action.upper(), 0.0)
        return base_score * confidence

    def _score_to_action(self, score: float) -> Tuple[str, float]:
        """Convert ensemble score to action and confidence."""
        abs_score = abs(score)
        confidence = min(abs_score, 1.0)

        thresholds = self.signal_config['thresholds']

        if score >= thresholds['strong_buy']:
            return 'LONG', confidence
        elif score >= thresholds['buy']:
            return 'LONG', confidence
        elif score <= thresholds['strong_sell']:
            return 'SHORT', confidence
        elif score <= thresholds['sell']:
            return 'SHORT', confidence
        else:
            return 'WAIT', confidence

    def _generate_reasoning(self, features: MarketFeatures, model_outputs: List[ModelOutput], score: float) -> str:
        """Generate human-readable reasoning for the signal."""
        active_models = [output.model_name for output in model_outputs if output.action != 'WAIT']

        if score > 0.6:
            sentiment = "bullish"
        elif score < -0.6:
            sentiment = "bearish"
        else:
            sentiment = "neutral"

        reasoning = f"Ensemble {sentiment} signal (score: {score:.2f}) from {len(active_models)} models: {', '.join(active_models)}"

        # Add key feature insights
        if features.rsi is not None:
            if features.rsi > 70:
                reasoning += f". RSI overbought ({features.rsi:.1f})"
            elif features.rsi < 30:
                reasoning += f". RSI oversold ({features.rsi:.1f})"

        if features.order_flow_imbalance > 0.3:
            reasoning += ". Strong buy pressure"
        elif features.order_flow_imbalance < -0.3:
            reasoning += ". Strong sell pressure"

        return reasoning

    def _is_in_cooldown(self, symbol: str, current_time: float) -> bool:
        """Check if symbol is in signal cooldown period."""
        last_time = self.last_signal_time.get(symbol, 0)
        return (current_time - last_time) < self.signal_cooldown

    def get_model_stats(self) -> Dict[str, Any]:
        """Get model performance statistics."""
        stats = {}
        for model_name, model in self.models.items():
            stats[model_name] = model.get_stats()
        return stats

    def set_exchange_client(self, exchange_client):
        """🔧 Inject exchange client for multi-timeframe analysis."""
        self.exchange_client = exchange_client
        logger.info("🔗 Exchange client injected for MTA analysis")

    def set_account_tracker(self, account_tracker):
        """💰 Inject account tracker for real balance data."""
        self.account_tracker = account_tracker
        logger.info("💰 Account tracker injected for real balance data")

    def set_strategy_mode(self, strategy_mode: str):
        """🎯 Set strategy mode and update timeframes accordingly."""
        if self.mta_analyzer:
            self.mta_analyzer.set_strategy_mode(strategy_mode)
            logger.info(f"🎯 Strategy mode set to: {strategy_mode.upper()}")
        else:
            logger.warning("⚠️ MTA analyzer not available for strategy mode update")

    def get_strategy_mode(self) -> str:
        """🎯 Get current strategy mode."""
        if self.mta_analyzer:
            return self.mta_analyzer.get_strategy_mode()
        return 'scalping'  # Default fallback

    def _get_real_account_data(self) -> Dict[str, Any]:
        """💰 Get real account data from HTX account tracker."""
        try:
            if self.account_tracker:
                # Get real account summary from HTX
                account_summary = self.account_tracker.get_account_summary()

                real_account_data = {
                    'margin_used_pct': account_summary.get('margin_used_pct', 0.0),
                    'available_balance': account_summary.get('available_balance', 0.0),
                    'total_balance': account_summary.get('total_balance', 0.0),
                    'unrealized_pnl': account_summary.get('unrealized_pnl', 0.0),
                    'liquidation_buffer': account_summary.get('liquidation_buffer', 0.0),
                    'open_positions': account_summary.get('open_positions', 0),
                    'risk_level': account_summary.get('risk_level', 'unknown')
                }

                logger.debug(f"💰 Real account data: Balance=${real_account_data['available_balance']:.2f}, "
                           f"Margin={real_account_data['margin_used_pct']:.1f}%, "
                           f"Risk={real_account_data['risk_level']}")

                return real_account_data
            else:
                # Fallback to safe defaults if account tracker not available
                logger.warning("⚠️ Account tracker not available, using safe defaults")
                return {
                    'margin_used_pct': 0.0,
                    'available_balance': 0.0,
                    'total_balance': 0.0,
                    'unrealized_pnl': 0.0,
                    'liquidation_buffer': 100.0,
                    'open_positions': 0,
                    'risk_level': 'safe'
                }

        except Exception as e:
            logger.error(f"❌ Error getting real account data: {e}")
            # Return safe defaults on error
            return {
                'margin_used_pct': 0.0,
                'available_balance': 0.0,
                'total_balance': 0.0,
                'unrealized_pnl': 0.0,
                'liquidation_buffer': 100.0,
                'open_positions': 0,
                'risk_level': 'safe'
            }

    def get_strategic_intelligence_stats(self) -> Dict[str, Any]:
        """🧠 Get comprehensive strategic intelligence statistics."""
        try:
            stats = {
                'mta_analyzer': self.mta_analyzer.get_performance_stats(),
                'strategy_evaluator': self.strategy_evaluator.get_evaluation_stats(),
                'strategy_tracker': self.strategy_tracker.get_tracker_stats(),
                'model_performance': self.get_model_stats(),
                'last_mta_analysis': {
                    symbol: {
                        'alignment_score': analysis.alignment_score,
                        'dominant_trend': analysis.dominant_trend,
                        'risk_level': analysis.risk_level,
                        'execution_recommendation': analysis.execution_recommendation,
                        'confidence': analysis.confidence
                    } for symbol, analysis in self.last_mta_analysis.items()
                }
            }
            return stats
        except Exception as e:
            logger.error(f"Error getting strategic intelligence stats: {e}")
            return {}

    async def record_trade_result(self, trade_result_data: Dict[str, Any]):
        """🧠 Record completed trade result for strategy tracking."""
        try:
            trade_result = TradeResult(
                trade_id=trade_result_data.get('trade_id', ''),
                symbol=trade_result_data.get('symbol', ''),
                action=trade_result_data.get('action', ''),
                entry_price=trade_result_data.get('entry_price', 0.0),
                exit_price=trade_result_data.get('exit_price', 0.0),
                size=trade_result_data.get('size', 0.0),
                pnl=trade_result_data.get('pnl', 0.0),
                duration_minutes=trade_result_data.get('duration_minutes', 0.0),
                model_contributions=trade_result_data.get('model_contributions', {}),
                mta_alignment_score=trade_result_data.get('mta_alignment_score', 0.5),
                signal_confidence=trade_result_data.get('signal_confidence', 0.0),
                execution_quality=trade_result_data.get('execution_quality', 0.8),
                timestamp=trade_result_data.get('timestamp', time.time())
            )

            self.strategy_tracker.record_trade_result(trade_result)
            logger.info(f"📊 Trade result recorded for strategy tracking: {trade_result.symbol} PnL: ${trade_result.pnl:.2f}")

        except Exception as e:
            logger.error(f"Error recording trade result: {e}")

    def update_model_weights(self, new_weights: Dict[str, float]):
        """🔧 Update model weights based on performance feedback."""
        try:
            for model_name, new_weight in new_weights.items():
                if model_name in self.model_weights:
                    old_weight = self.model_weights[model_name]
                    self.model_weights[model_name] = new_weight
                    logger.info(f"🔧 Updated {model_name} weight: {old_weight:.2f} → {new_weight:.2f}")

            # Update strategy evaluator model performance
            for model_name, weight in new_weights.items():
                if model_name in self.models:
                    model_stats = self.models[model_name].get_stats()
                    win_rate = model_stats.get('accuracy', 0.5)
                    self.strategy_evaluator.update_model_performance(
                        model_name, win_rate, 0.01, weight  # Mock avg_return and use weight as reliability
                    )

        except Exception as e:
            logger.error(f"Error updating model weights: {e}")

    async def start_background_tasks(self):
        """🧠 Start strategic intelligence background tasks."""
        try:
            await self.strategy_tracker.start_background_tasks()
            logger.info("🧠 Strategic intelligence background tasks started")
        except Exception as e:
            logger.error(f"Error starting background tasks: {e}")

    def shutdown(self):
        """🧠 Shutdown strategic intelligence modules."""
        try:
            self.strategy_tracker.shutdown()
            logger.info("🧠 Strategic intelligence modules shutdown complete")
        except Exception as e:
            logger.error(f"Error shutting down strategic intelligence: {e}")

# Individual AI Model Classes

class RSIModel:
    """RSI-based trading model."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.period = config['period']
        self.overbought = config['overbought']
        self.oversold = config['oversold']
        self.stats = {'signals_generated': 0, 'accuracy': 0.0}

    async def analyze(self, features: MarketFeatures) -> Optional[ModelOutput]:
        """Analyze features using RSI."""
        if features.rsi is None:
            return None

        rsi = features.rsi

        if rsi <= self.oversold:
            action = 'BUY'
            confidence = min((self.oversold - rsi) / self.oversold, 1.0)
            signal = 'OVERSOLD'
        elif rsi >= self.overbought:
            action = 'SELL'
            confidence = min((rsi - self.overbought) / (100 - self.overbought), 1.0)
            signal = 'OVERBOUGHT'
        else:
            action = 'WAIT'
            confidence = 1.0 - abs(rsi - 50) / 50
            signal = 'NEUTRAL'

        self.stats['signals_generated'] += 1

        return ModelOutput(
            model_name='rsi',
            symbol=features.symbol,
            action=action,
            confidence=confidence,
            signal=signal,
            value=rsi,
            timestamp=time.time()
        )

    def get_stats(self) -> Dict[str, Any]:
        return dict(self.stats)

class VWAPModel:
    """VWAP-based trading model."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.deviation_threshold = config['deviation_threshold']
        self.stats = {'signals_generated': 0, 'accuracy': 0.0}

    async def analyze(self, features: MarketFeatures) -> Optional[ModelOutput]:
        """Analyze features using VWAP."""
        if features.vwap is None or features.last_price <= 0:
            return None

        price = features.last_price
        vwap = features.vwap
        deviation = (price - vwap) / vwap

        if deviation > self.deviation_threshold:
            action = 'SELL'
            confidence = min(abs(deviation) / self.deviation_threshold, 1.0)
            signal = 'ABOVE'
        elif deviation < -self.deviation_threshold:
            action = 'BUY'
            confidence = min(abs(deviation) / self.deviation_threshold, 1.0)
            signal = 'BELOW'
        else:
            action = 'WAIT'
            confidence = 1.0 - abs(deviation) / self.deviation_threshold
            signal = 'NEAR'

        self.stats['signals_generated'] += 1

        return ModelOutput(
            model_name='vwap',
            symbol=features.symbol,
            action=action,
            confidence=confidence,
            signal=signal,
            value=deviation,
            timestamp=time.time()
        )

    def get_stats(self) -> Dict[str, Any]:
        return dict(self.stats)

class OrderflowModel:
    """Order flow imbalance trading model."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.imbalance_threshold = config['imbalance_threshold']
        self.stats = {'signals_generated': 0, 'accuracy': 0.0}

    async def analyze(self, features: MarketFeatures) -> Optional[ModelOutput]:
        """Analyze features using order flow."""
        imbalance = features.order_flow_imbalance

        if imbalance > self.imbalance_threshold:
            action = 'BUY'
            confidence = min(imbalance / self.imbalance_threshold, 1.0)
            signal = 'BUY_PRESSURE'
        elif imbalance < -self.imbalance_threshold:
            action = 'SELL'
            confidence = min(abs(imbalance) / self.imbalance_threshold, 1.0)
            signal = 'SELL_PRESSURE'
        else:
            action = 'WAIT'
            confidence = 1.0 - abs(imbalance) / self.imbalance_threshold
            signal = 'BALANCED'

        self.stats['signals_generated'] += 1

        return ModelOutput(
            model_name='orderflow',
            symbol=features.symbol,
            action=action,
            confidence=confidence,
            signal=signal,
            value=imbalance,
            timestamp=time.time()
        )

    def get_stats(self) -> Dict[str, Any]:
        return dict(self.stats)

class VolatilityModel:
    """Volatility-based trading model."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.threshold_high = config['threshold_high']
        self.threshold_low = config['threshold_low']
        self.stats = {'signals_generated': 0, 'accuracy': 0.0}

    async def analyze(self, features: MarketFeatures) -> Optional[ModelOutput]:
        """Analyze features using volatility."""
        if features.volatility is None:
            return None

        volatility = features.volatility

        if volatility > self.threshold_high:
            action = 'WAIT'  # High volatility = wait
            confidence = min(volatility / self.threshold_high, 1.0)
            signal = 'HIGH'
        elif volatility < self.threshold_low:
            action = 'BUY'  # Low volatility = opportunity
            confidence = min(self.threshold_low / volatility, 1.0) if volatility > 0 else 1.0
            signal = 'LOW'
        else:
            action = 'WAIT'
            confidence = 0.5
            signal = 'NORMAL'

        self.stats['signals_generated'] += 1

        return ModelOutput(
            model_name='volatility',
            symbol=features.symbol,
            action=action,
            confidence=confidence,
            signal=signal,
            value=volatility,
            timestamp=time.time()
        )

    def get_stats(self) -> Dict[str, Any]:
        return dict(self.stats)
