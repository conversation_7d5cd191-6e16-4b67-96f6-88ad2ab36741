#!/usr/bin/env python3
"""
Test script for Phase 6 Execution Intelligence
Tests the complete execution pipeline from decision to reflection
"""

import asyncio
import logging
import yaml
import time
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import execution intelligence components
from execution.execution_controller import ExecutionController
from execution.trade_executor import RiskAwareTradeExecutor
from execution.order_logic import AdaptiveOrderLogic
from execution.execution_memory import ExecutionMemory
from execution.post_trade_reflection import PostTradeReflection

async def test_execution_intelligence():
    """Test the complete execution intelligence pipeline."""
    try:
        logger.info("🚀 Testing Phase 6 Execution Intelligence")
        
        # Load configuration
        config_path = Path("config/strategy.yaml")
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        logger.info("✅ Configuration loaded")
        
        # Initialize execution controller
        execution_controller = ExecutionController(config)
        logger.info("✅ Execution Controller initialized")
        
        # Test data
        test_decision = {
            'symbol': 'BTC-USDT',
            'action': 'LONG',
            'final_decision': 'LONG',
            'confidence': 0.85,
            'conviction_score': 4,
            'reasoning': 'Strong bullish signals from RSI oversold conditions, VWAP support, and positive momentum indicators. Market regime shows trending upward movement with good volume confirmation.',
            'market_regime': 'trending_up',
            'timestamp': time.time()
        }
        
        test_market_data = {
            'symbol': 'BTC-USDT',
            'last_price': 45000.0,
            'volume_24h': 1500000000,
            'volume_1h': 62500000,
            'volatility': 0.012,
            'spread': 0.0002,
            'price_change_1m': 0.003,
            'price_change_5m': 0.008
        }
        
        logger.info("📊 Test data prepared")
        
        # Test 1: Process trading decision
        logger.info("\n🧪 Test 1: Processing Trading Decision")
        execution_result = await execution_controller.process_trading_decision(
            test_decision, test_market_data
        )
        
        if execution_result.execution:
            logger.info(f"✅ Trade executed: {execution_result.execution.symbol} "
                       f"{execution_result.execution.action} {execution_result.execution.size:.2f} "
                       f"@ ${execution_result.execution.execution_price:.2f}")
            logger.info(f"📈 Execution quality: {execution_result.execution_quality:.2f}")
            logger.info(f"🎯 Order strategy: {execution_result.order_recommendation.strategy.value}")
        else:
            logger.info("⏸️ Trade was skipped based on order logic")
        
        # Test 2: Update position status
        logger.info("\n🧪 Test 2: Position Status Update")
        new_price = 45500.0  # Price moved up
        await execution_controller.update_position_status('BTC-USDT', new_price)
        logger.info(f"✅ Position updated with new price: ${new_price}")
        
        # Test 3: Force close position for reflection
        if execution_result.execution:
            logger.info("\n🧪 Test 3: Position Closure and Reflection")
            close_price = 46000.0  # Profitable close
            await execution_controller._close_position_and_reflect(
                execution_result.execution.execution_id, close_price
            )
            logger.info(f"✅ Position closed at ${close_price} with reflection")
        
        # Test 4: Get system status
        logger.info("\n🧪 Test 4: System Status")
        status = execution_controller.get_execution_intelligence_status()
        logger.info(f"✅ System status: {status['system_status']}")
        logger.info(f"📊 Execution rate: {status['execution_rate']:.2f}")
        logger.info(f"💼 Active positions: {status['active_positions']['count']}")
        
        # Test 5: Performance insights
        logger.info("\n🧪 Test 5: Performance Insights")
        insights = execution_controller.get_performance_insights()
        if 'error' not in insights:
            logger.info("✅ Performance insights generated")
            if insights.get('recommendations'):
                logger.info(f"💡 Recommendations: {len(insights['recommendations'])}")
                for rec in insights['recommendations'][:3]:
                    logger.info(f"   - {rec}")
        else:
            logger.info(f"⚠️ Insights error: {insights['error']}")
        
        logger.info("\n🎉 Phase 6 Execution Intelligence test completed successfully!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False

async def test_individual_components():
    """Test individual execution intelligence components."""
    try:
        logger.info("\n🔧 Testing Individual Components")
        
        # Load config
        config_path = Path("config/strategy.yaml")
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Test Trade Executor
        logger.info("\n🧪 Testing Trade Executor")
        executor = RiskAwareTradeExecutor(config)
        
        test_decision = {
            'symbol': 'BTC-USDT',
            'action': 'LONG',
            'confidence': 0.8,
            'conviction_score': 3,
            'market_regime': 'trending_up'
        }
        
        test_market = {
            'last_price': 45000.0,
            'volume_24h': 1000000000,
            'volatility': 0.01
        }
        
        execution = await executor.execute_decision(test_decision, test_market)
        if execution:
            logger.info(f"✅ Executor test: {execution.symbol} {execution.action} "
                       f"{execution.size:.2f} @ ${execution.execution_price:.2f}")
        
        # Test Order Logic
        logger.info("\n🧪 Testing Order Logic")
        order_logic = AdaptiveOrderLogic(config)
        
        recommendation = order_logic.recommend_order_strategy(
            test_decision, test_market, 100.0
        )
        logger.info(f"✅ Order logic test: {recommendation.strategy.value} "
                   f"(confidence: {recommendation.confidence:.2f})")
        
        # Test Execution Memory
        logger.info("\n🧪 Testing Execution Memory")
        memory = ExecutionMemory(config)
        
        if execution:
            execution_id = memory.record_execution({
                'execution_id': execution.execution_id,
                'symbol': execution.symbol,
                'timestamp': execution.timestamp,
                'action': execution.action,
                'size': execution.size,
                'entry_price': execution.entry_price,
                'execution_price': execution.execution_price,
                'slippage': execution.slippage,
                'latency_ms': execution.latency_ms,
                'fill_quality': execution.fill_quality,
                'order_type': execution.order_type,
                'confidence': execution.confidence,
                'conviction_score': execution.conviction_score,
                'market_regime': execution.market_regime
            })
            logger.info(f"✅ Memory test: Execution {execution_id} recorded")
        
        # Test Post-Trade Reflection
        logger.info("\n🧪 Testing Post-Trade Reflection")
        reflection = PostTradeReflection(config)
        
        trade_data = {
            'trade_id': 'test_trade_123',
            'original_decision': test_decision,
            'symbol': 'BTC-USDT',
            'action': 'LONG',
            'size': 100.0,
            'entry_price': 45000.0,
            'exit_price': 46000.0,
            'pnl': 100.0,
            'hold_duration': 2.5,
            'exit_reason': 'take_profit',
            'market_change': 'stable'
        }
        
        reflection_result = await reflection.analyze_completed_trade(trade_data)
        if reflection_result:
            logger.info(f"✅ Reflection test: {reflection_result.actual_outcome.value} "
                       f"(quality: {reflection_result.reasoning_quality_score:.2f})")
        
        logger.info("\n🎉 Individual component tests completed!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Component test failed: {e}")
        return False

async def main():
    """Main test function."""
    logger.info("🚀 Starting Phase 6 Execution Intelligence Tests")
    
    # Ensure data directory exists
    Path('data/execution').mkdir(parents=True, exist_ok=True)
    
    # Run tests
    success1 = await test_individual_components()
    success2 = await test_execution_intelligence()
    
    if success1 and success2:
        logger.info("\n🎉 All tests passed! Phase 6 Execution Intelligence is ready.")
    else:
        logger.error("\n❌ Some tests failed. Check the logs for details.")

if __name__ == "__main__":
    asyncio.run(main())
