account_update_interval: 30
adaptive_throttle: true
call_interval_s: 30
dashboard:
  enabled: true
  host: localhost
  port: 8081
  update_interval: 5
database:
  backup_interval: 3600
  cleanup_interval: 86400
  max_age_days: 7
  path: data/smart_trader.db
debug:
  enabled: false
  generate_test_signals: false
  test_signal_interval: 60
  test_signal_sources:
  - rsi
  - orderflow
  - volatility
  - vwap
  - funding
  test_signal_symbols:
  - BTC-USDT
demo_mode: false
dummy_llm: false
dummy_mode: false
enable_ensemble_model: true
enable_funding_momentum_model: true
enable_garch_volatility_model: true
enable_liquidity_imbalance_model: true
enable_open_interest_momentum_model: true
enable_orderflow_model: true
enable_rsi_model: true
enable_social_sentiment_model: true
enable_volatility_regime_model: true
enable_vwap_deviation_model: true
ensemble_model_config:
  learning_rate: 0.05
  max_weight: 3.0
  min_weight: 0.1
  model_weights:
    funding_momentum: 1.2
    garch_volatility: 1.3
    liquidity_imbalance: 1.0
    open_interest_momentum: 1.1
    orderflow: 1.5
    rsi: 1.0
    social_sentiment: 0.8
    volatility_regime: 1.2
    vwap_deviation: 1.0
  performance_window: 24
exchange:
  api_key: nbtycf4rw2-72d300ec-fb900970-27ef8
  api_secret: b4d92e15-523563a0-72a16ad9-9a275
  base_url: https://api.htx.com
  name: htx
  testnet: true
  ws_url: wss://api.htx.com/ws
feature_store:
  path: data/smart_trader_features.db
  ttl: 86400
  type: sqlite
funding_momentum_config:
  threshold: 1.5
  update_interval: 300
  window: 24
garch_volatility_config:
  p: 1
  q: 1
  update_interval: 300
  window: 48
health_check_interval: 60
liquidity_imbalance_config:
  threshold: 1.5
  update_interval: 60
  window: 12
live_trading:
  alerts:
    email_enabled: false
    log_level: WARNING
    webhook_enabled: false
  enabled: true
  risk_management:
    max_daily_loss: 50.0
    max_position_size: 0.1
    stop_loss_percent: 2.0
    take_profit_percent: 4.0
llm:
  adaptive_throttle: true
  call_interval_s: 30
  confidence_threshold: 0.6
  dummy_mode: false
  error_threshold: 0.1
  latency_threshold: 10.0
  max_memory_size: 10
  max_throttle_interval: 120
  max_tokens: 128
  min_throttle_interval: 10
  model_path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
  n_ctx: 2048
  n_gpu_layers: 0
  n_threads: 4
  prompt_path: llm/prompts/trading_prompt_phi.yaml
  temperature: 0.0
  throttle_volatility_factor: 2.0
  verbose: false
llm_confidence_threshold: 0.6
llm_error_threshold: 0.05
llm_gpu_layers: 0
llm_latency_threshold: 5.0
llm_model_path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
llm_prompt_path: llm/prompts/trading_prompt_phi.yaml
llm_threads: 4
llm_throttle_seconds: 5
logging:
  backup_count: 5
  console: true
  file: logs/smart_trader.log
  level: INFO
  max_size_mb: 10
max_throttle_interval: 120
max_tokens: 128
message_bus:
  batch_size: 100
  batch_timeout: 1.0
  cache_size: 10000
  mmap_size: 268435456
  path: data/bus.db
  poll_interval: 0.1
  sync_mode: NORMAL
  type: sqlite
  wal_mode: true
min_throttle_interval: 10
mode: testnet
model_path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
models:
  funding:
    enabled: true
    update_interval: 60
    window: 24
  open_interest:
    enabled: true
    update_interval: 60
    window: 24
  sentiment:
    api_key: ''
    enabled: true
    update_interval: 120
  volatility:
    enabled: true
    garch_p: 1
    garch_q: 1
    update_interval: 30
    window: 24
  vwap:
    enabled: true
    update_interval: 5
    window: 24
monitoring:
  alert_thresholds:
    max_error_rate: 0.1
    max_latency_ms: 1000
    min_accuracy: 0.6
  enabled: true
  update_interval: 60
n_ctx: 2048
n_gpu_layers: 0
n_threads: 4
open_interest_momentum_config:
  threshold: 2.0
  update_interval: 300
  window: 24
prompt_path: llm/prompts/trading_prompt_phi.yaml
social_sentiment_config:
  contrarian: true
  delta_window: 5
  threshold: 1.2
  update_interval: 300
  z_window: 60
strategy:
  signal_generation_interval: 10
  type: smart_integrated
  use_smart_strategy: true
# 🎯 NEW: Strategy mode configuration for timeframe selection
strategy_mode: scalping  # Options: scalping, intraday, swing, investor

# 🔧 NEW: Data storage configuration
data_storage:
  persist_state: false
  memory_cleanup_interval: 3600
symbols:
  BTC-USDT:
    model_weights:
      funding_momentum: 1.2
      garch_volatility: 1.3
      liquidity_imbalance: 1.0
      open_interest_momentum: 1.1
      orderflow: 1.5
      rsi: 1.0
      social_sentiment: 0.8
      volatility_regime: 1.2
      vwap_deviation: 1.0
    trading_thresholds:
      base_buy_threshold: 0.3
      base_sell_threshold: -0.3
temperature: 0.0
throttle_volatility_factor: 2.0
trading:
  base_buy_threshold: 0.3
  base_sell_threshold: -0.3
  enabled: true
  leverage: 2
  max_positions: 1
  max_slippage_bps: 10
  order_type: MARKET
  position_manager:
    enabled: true
    market_risk_threshold: 80.0
    max_daily_drawdown_pct: 5.0
    max_open_positions: 3
    max_risk_per_symbol_pct: 2.0
    max_risk_per_trade_pct: 1.0
    partial_tp_levels:
    - percent: 25
      price_pct: 2.0
    - percent: 25
      price_pct: 3.0
    - percent: 50
      price_pct: 4.0
    position_sizing_method: volatility
    trailing_stop: true
    trailing_stop_activation: 1.0
    trailing_stop_distance: 0.5
    use_partial_take_profits: true
    use_risk_overlay: true
    volatility_lookback: 20
    volatility_risk_factor: 10.0
    volatility_risk_threshold: 2.0
  position_monitor_interval: 1.0
  position_size_usd: 10.0
  sim_balance: 100.0
  simulation_mode: false
  stop_loss_pct: 3
  symbols:
  - BTC-USDT
  take_profit_pct: 1
vwap_deviation_config:
  threshold: 2.0
  update_interval: 60
  window: 24
