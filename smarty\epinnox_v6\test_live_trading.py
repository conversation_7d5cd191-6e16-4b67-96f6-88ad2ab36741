#!/usr/bin/env python3
"""
Live Trading Test Script
Tests HTX API integration and live trading capabilities
"""

import asyncio
import logging
import yaml
import os
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

from exchange.htx_client import HTXClient
from execution.execution_controller import ExecutionController

async def test_htx_connection():
    """Test HTX API connection and credentials."""
    try:
        logger.info("🔧 Testing HTX API Connection")
        
        # Load configuration
        config_path = Path("config/strategy.yaml")
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Initialize HTX client
        htx_client = HTXClient(config)
        
        # Check trading status
        status = htx_client.get_trading_status()
        logger.info(f"📊 Trading Status:")
        logger.info(f"   Live Trading Enabled: {status['live_trading_enabled']}")
        logger.info(f"   API Configured: {status['api_configured']}")
        logger.info(f"   Trading Symbol: {status['trading_symbol']}")
        logger.info(f"   Account Balance: ${status['account_balance']}")
        logger.info(f"   Max Position Size: ${status['max_position_size']}")
        
        if not status['api_configured']:
            logger.warning("⚠️ HTX API credentials not configured")
            logger.info("📝 Please set HTX_API_KEY and HTX_API_SECRET in .env file")
            return False
        
        # Test API connection
        async with htx_client as client:
            # Test account info
            account_info = await client.get_account_info()
            if account_info:
                logger.info("✅ HTX API connection successful")
                logger.info(f"📊 Account info retrieved: {len(account_info)} accounts")
            else:
                logger.error("❌ Failed to retrieve account info")
                return False
            
            # Test position info
            position_info = await client.get_position_info('DOGE-USDT')
            logger.info(f"📊 Position info: {position_info is not None}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ HTX connection test failed: {e}")
        return False

async def test_live_execution():
    """Test live execution controller with HTX integration."""
    try:
        logger.info("\n🚀 Testing Live Execution Controller")
        
        # Load configuration
        config_path = Path("config/strategy.yaml")
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Initialize execution controller
        execution_controller = ExecutionController(config)
        
        # Check if live trading is enabled
        live_trading = execution_controller.live_trading_enabled
        logger.info(f"📊 Live Trading Mode: {'ENABLED' if live_trading else 'SIMULATION'}")
        
        # Test decision processing
        test_decision = {
            'symbol': 'DOGE-USDT',
            'action': 'WAIT',  # Start with WAIT for safety
            'confidence': 0.75,
            'conviction_score': 3,
            'reasoning': 'Test decision for live trading validation',
            'risk_assessment': 'MEDIUM',
            'market_regime': 'ranging'
        }
        
        test_market_data = {
            'symbol': 'DOGE-USDT',
            'last_price': 0.08,  # Example DOGE price
            'volume_24h': 1000000,
            'volatility': 0.02,
            'spread': 0.0001
        }
        
        logger.info(f"🧪 Processing test decision: {test_decision['action']} {test_decision['symbol']}")
        
        # Process decision
        result = await execution_controller.process_trading_decision(test_decision, test_market_data)
        
        logger.info(f"✅ Decision processed:")
        logger.info(f"   Decision ID: {result.decision_id}")
        logger.info(f"   Executed: {'YES' if result.execution else 'NO'}")
        logger.info(f"   Execution Quality: {result.execution_quality:.2f}")
        logger.info(f"   Order Strategy: {result.order_recommendation.strategy.value}")
        
        if result.execution:
            execution = result.execution
            logger.info(f"🚀 Execution Details:")
            logger.info(f"   Symbol: {execution.symbol}")
            logger.info(f"   Action: {execution.action}")
            logger.info(f"   Size: {execution.size:.2f}")
            logger.info(f"   Price: ${execution.execution_price:.4f}")
            logger.info(f"   Slippage: {execution.slippage*100:.3f}%")
        
        # Get execution intelligence status
        status = execution_controller.get_execution_intelligence_status()
        logger.info(f"📊 Execution Intelligence Status: {status['system_status']}")
        logger.info(f"   Execution Rate: {status['execution_rate']*100:.1f}%")
        logger.info(f"   Active Positions: {status['active_positions']['count']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Live execution test failed: {e}")
        return False

async def test_safety_checks():
    """Test safety mechanisms for live trading."""
    try:
        logger.info("\n🛡️ Testing Safety Mechanisms")
        
        # Load configuration
        config_path = Path("config/strategy.yaml")
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Check risk management settings
        risk_config = config.get('risk', {})
        logger.info(f"📊 Risk Management Configuration:")
        logger.info(f"   Max Position Size: ${risk_config.get('max_position_size', 0)}")
        logger.info(f"   Stop Loss: {risk_config.get('stop_loss_percent', 0)}%")
        logger.info(f"   Take Profit: {risk_config.get('take_profit_percent', 0)}%")
        logger.info(f"   Daily Loss Limit: ${risk_config.get('circuit_breaker', {}).get('loss_threshold', 0)}")
        logger.info(f"   Max Daily Trades: {risk_config.get('max_daily_trades', 0)}")
        
        # Check execution settings
        execution_config = config.get('execution', {})
        logger.info(f"📊 Execution Configuration:")
        logger.info(f"   Mode: {execution_config.get('mode', 'unknown')}")
        logger.info(f"   Max Position: ${execution_config.get('max_position_size', 0)}")
        logger.info(f"   Base Position: ${execution_config.get('base_position_size', 0)}")
        logger.info(f"   Min Position: ${execution_config.get('min_position_size', 0)}")
        
        # Check development settings
        dev_config = config.get('development', {})
        logger.info(f"📊 Development Configuration:")
        logger.info(f"   Debug Mode: {dev_config.get('debug_mode', False)}")
        logger.info(f"   Simulate Trades: {dev_config.get('simulate_trades', True)}")
        logger.info(f"   Verbose Logging: {dev_config.get('verbose_logging', False)}")
        
        # Validate safety thresholds
        safety_checks = []
        
        # Check position size limits
        max_pos = risk_config.get('max_position_size', 0)
        account_balance = config.get('exchange', {}).get('account_balance', 5.0)
        if max_pos > account_balance * 0.9:
            safety_checks.append("⚠️ Max position size > 90% of account balance")
        else:
            safety_checks.append("✅ Position size limits appropriate")
        
        # Check stop loss
        stop_loss = risk_config.get('stop_loss_percent', 0)
        if stop_loss < 2.0:
            safety_checks.append("⚠️ Stop loss < 2% - may be too tight")
        else:
            safety_checks.append("✅ Stop loss percentage appropriate")
        
        # Check circuit breaker
        loss_threshold = abs(risk_config.get('circuit_breaker', {}).get('loss_threshold', 0))
        if loss_threshold > account_balance * 0.6:
            safety_checks.append("⚠️ Daily loss limit > 60% of account")
        else:
            safety_checks.append("✅ Circuit breaker threshold appropriate")
        
        logger.info(f"🛡️ Safety Check Results:")
        for check in safety_checks:
            logger.info(f"   {check}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Safety check failed: {e}")
        return False

async def main():
    """Main test function."""
    logger.info("🚀 Starting Live Trading Tests")
    logger.info("=" * 60)
    
    # Check environment
    api_key = os.getenv('HTX_API_KEY')
    api_secret = os.getenv('HTX_API_SECRET')
    
    if not api_key or not api_secret:
        logger.warning("⚠️ HTX API credentials not found in environment")
        logger.info("📝 Please set HTX_API_KEY and HTX_API_SECRET in .env file")
        logger.info("🔧 Testing will run in simulation mode only")
    
    # Run tests
    test_results = []
    
    # Test 1: HTX Connection
    result1 = await test_htx_connection()
    test_results.append(("HTX Connection", result1))
    
    # Test 2: Live Execution
    result2 = await test_live_execution()
    test_results.append(("Live Execution", result2))
    
    # Test 3: Safety Checks
    result3 = await test_safety_checks()
    test_results.append(("Safety Mechanisms", result3))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("🏁 Live Trading Test Summary")
    logger.info("=" * 60)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\nResults: {passed}/{len(test_results)} tests passed")
    
    if passed == len(test_results):
        logger.info("🎉 All tests passed! System ready for live trading.")
        logger.info("⚠️ IMPORTANT: Start with small position sizes and monitor closely!")
    else:
        logger.warning("⚠️ Some tests failed. Review configuration before live trading.")
    
    logger.info("\n🔧 Next Steps:")
    logger.info("1. Set HTX_API_KEY and HTX_API_SECRET in .env file")
    logger.info("2. Verify account balance and trading permissions")
    logger.info("3. Start with DOGE-USDT and small position sizes")
    logger.info("4. Monitor trades closely and adjust risk parameters")
    logger.info("5. Use dashboard at http://localhost:8086 for monitoring")

if __name__ == "__main__":
    asyncio.run(main())
