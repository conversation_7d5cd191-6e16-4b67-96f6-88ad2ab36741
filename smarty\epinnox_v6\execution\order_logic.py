#!/usr/bin/env python3
"""
Adaptive Order Logic
Uses volatility/volume/price slope to choose market vs limit order, stop strategy, or skip
"""

import logging
import time
import numpy as np
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class OrderStrategy(Enum):
    MARKET_IMMEDIATE = "market_immediate"
    LIMIT_AGGRESSIVE = "limit_aggressive"
    LIMIT_PASSIVE = "limit_passive"
    ICEBERG_LARGE = "iceberg_large"
    DELAYED_ENTRY = "delayed_entry"
    SKIP_UNFAVORABLE = "skip_unfavorable"

@dataclass
class OrderRecommendation:
    """Recommendation for order execution strategy."""
    strategy: OrderStrategy
    order_type: str
    price_offset: float  # Percentage offset from current price
    time_delay: float   # Seconds to wait before execution
    split_ratio: float  # For iceberg orders (0-1)
    confidence: float   # Confidence in recommendation
    reasoning: str      # Why this strategy was chosen

class AdaptiveOrderLogic:
    """
    Intelligent order routing that adapts execution strategy based on
    market conditions, volatility, volume, and price momentum.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.order_config = config.get('adaptive_orders', {})

        # Volatility thresholds
        self.low_volatility_threshold = self.order_config.get('low_volatility_threshold', 0.005)
        self.high_volatility_threshold = self.order_config.get('high_volatility_threshold', 0.02)

        # Volume thresholds
        self.low_volume_threshold = self.order_config.get('low_volume_threshold', 0.5)
        self.high_volume_threshold = self.order_config.get('high_volume_threshold', 2.0)

        # Price momentum thresholds
        self.strong_momentum_threshold = self.order_config.get('strong_momentum_threshold', 0.01)
        self.weak_momentum_threshold = self.order_config.get('weak_momentum_threshold', 0.002)

        # Order size thresholds
        self.large_order_threshold = self.order_config.get('large_order_threshold', 500.0)
        self.iceberg_threshold = self.order_config.get('iceberg_threshold', 1000.0)

        logger.info("Adaptive Order Logic initialized")

    def recommend_order_strategy(self, decision: Dict[str, Any],
                                market_data: Dict[str, Any],
                                order_size: float) -> OrderRecommendation:
        """
        Recommend optimal order execution strategy based on market conditions.

        Args:
            decision: Trading decision with confidence, action, etc.
            market_data: Current market conditions
            order_size: Size of the order to execute

        Returns:
            OrderRecommendation with strategy and parameters
        """
        try:
            # Analyze market conditions
            volatility_regime = self._analyze_volatility(market_data)
            volume_regime = self._analyze_volume(market_data)
            momentum_regime = self._analyze_momentum(market_data)
            liquidity_score = self._calculate_liquidity_score(market_data)

            # Get decision parameters
            confidence = decision.get('confidence', 0.0)
            conviction_score = decision.get('conviction_score', 1)
            action = decision.get('action', decision.get('final_decision', 'WAIT'))

            # Determine optimal strategy
            strategy = self._select_strategy(
                volatility_regime, volume_regime, momentum_regime,
                liquidity_score, confidence, conviction_score, order_size
            )

            # Calculate strategy parameters
            price_offset, time_delay, split_ratio = self._calculate_strategy_params(
                strategy, volatility_regime, momentum_regime, action
            )

            # Generate reasoning
            reasoning = self._generate_reasoning(
                strategy, volatility_regime, volume_regime, momentum_regime,
                liquidity_score, confidence, order_size
            )

            return OrderRecommendation(
                strategy=strategy,
                order_type=self._get_order_type(strategy),
                price_offset=price_offset,
                time_delay=time_delay,
                split_ratio=split_ratio,
                confidence=self._calculate_recommendation_confidence(strategy, market_data),
                reasoning=reasoning
            )

        except Exception as e:
            logger.error(f"Error recommending order strategy: {e}")
            return self._get_fallback_recommendation()

    def _analyze_volatility(self, market_data: Dict[str, Any]) -> str:
        """Analyze current volatility regime."""
        try:
            volatility = market_data.get('volatility', 0.01)

            # Handle None values
            if volatility is None:
                volatility = 0.01

            if volatility < self.low_volatility_threshold:
                return 'low'
            elif volatility > self.high_volatility_threshold:
                return 'high'
            else:
                return 'medium'

        except Exception as e:
            logger.error(f"Error analyzing volatility: {e}")
            return 'medium'

    def _analyze_volume(self, market_data: Dict[str, Any]) -> str:
        """Analyze current volume regime."""
        try:
            current_volume = market_data.get('volume_1h', 0)
            avg_volume = market_data.get('volume_24h', 0) / 24

            if avg_volume == 0:
                return 'medium'

            volume_ratio = current_volume / avg_volume

            if volume_ratio < self.low_volume_threshold:
                return 'low'
            elif volume_ratio > self.high_volume_threshold:
                return 'high'
            else:
                return 'medium'

        except Exception as e:
            logger.error(f"Error analyzing volume: {e}")
            return 'medium'

    def _analyze_momentum(self, market_data: Dict[str, Any]) -> str:
        """Analyze price momentum."""
        try:
            price_change_1m = market_data.get('price_change_1m', 0.0)
            price_change_5m = market_data.get('price_change_5m', 0.0)

            # Average momentum over different timeframes
            avg_momentum = abs((price_change_1m + price_change_5m) / 2)

            if avg_momentum > self.strong_momentum_threshold:
                return 'strong'
            elif avg_momentum < self.weak_momentum_threshold:
                return 'weak'
            else:
                return 'medium'

        except Exception as e:
            logger.error(f"Error analyzing momentum: {e}")
            return 'medium'

    def _calculate_liquidity_score(self, market_data: Dict[str, Any]) -> float:
        """Calculate market liquidity score (0-1)."""
        try:
            # Factors: volume, spread, depth
            volume_24h = market_data.get('volume_24h', 0)
            spread = market_data.get('spread', 0.001)

            # Normalize volume (higher is better)
            volume_score = min(volume_24h / 10000000, 1.0)  # Normalize to 10M volume

            # Normalize spread (lower is better)
            spread_score = max(0, 1.0 - (spread * 1000))  # Penalize spreads > 0.1%

            # Combined liquidity score
            liquidity_score = (volume_score * 0.7) + (spread_score * 0.3)

            return max(0.0, min(1.0, liquidity_score))

        except Exception as e:
            logger.error(f"Error calculating liquidity score: {e}")
            return 0.5

    def _select_strategy(self, volatility: str, volume: str, momentum: str,
                        liquidity: float, confidence: float, conviction: int,
                        order_size: float) -> OrderStrategy:
        """Select optimal order strategy based on conditions."""
        try:
            # Large orders need special handling
            if order_size > self.iceberg_threshold:
                return OrderStrategy.ICEBERG_LARGE

            # Low liquidity markets need careful execution
            if liquidity < 0.3:
                return OrderStrategy.LIMIT_PASSIVE

            # High volatility + strong momentum = immediate execution
            if volatility == 'high' and momentum == 'strong' and confidence > 0.8:
                return OrderStrategy.MARKET_IMMEDIATE

            # Low volatility + weak momentum = passive limit orders
            if volatility == 'low' and momentum == 'weak':
                return OrderStrategy.LIMIT_PASSIVE

            # High confidence + good liquidity = aggressive limit
            if confidence > 0.7 and liquidity > 0.6:
                return OrderStrategy.LIMIT_AGGRESSIVE

            # Medium conditions with high conviction = delayed entry for better price
            if conviction >= 4 and volatility != 'high':
                return OrderStrategy.DELAYED_ENTRY

            # Low confidence or poor conditions = skip
            if confidence < 0.5 or liquidity < 0.2:
                return OrderStrategy.SKIP_UNFAVORABLE

            # Default to aggressive limit
            return OrderStrategy.LIMIT_AGGRESSIVE

        except Exception as e:
            logger.error(f"Error selecting strategy: {e}")
            return OrderStrategy.LIMIT_AGGRESSIVE

    def _calculate_strategy_params(self, strategy: OrderStrategy, volatility: str,
                                 momentum: str, action: str) -> Tuple[float, float, float]:
        """Calculate strategy-specific parameters."""
        try:
            price_offset = 0.0
            time_delay = 0.0
            split_ratio = 1.0

            if strategy == OrderStrategy.MARKET_IMMEDIATE:
                price_offset = 0.0  # Market order
                time_delay = 0.0
                split_ratio = 1.0

            elif strategy == OrderStrategy.LIMIT_AGGRESSIVE:
                # Slightly favorable price
                base_offset = 0.0005  # 0.05%
                if volatility == 'high':
                    base_offset *= 2
                price_offset = base_offset if action.upper() in ['LONG', 'BUY'] else -base_offset
                time_delay = 1.0
                split_ratio = 1.0

            elif strategy == OrderStrategy.LIMIT_PASSIVE:
                # More favorable price, longer wait
                base_offset = 0.002  # 0.2%
                if momentum == 'strong':
                    base_offset *= 0.5  # Less aggressive in strong momentum
                price_offset = -base_offset if action.upper() in ['LONG', 'BUY'] else base_offset
                time_delay = 5.0
                split_ratio = 1.0

            elif strategy == OrderStrategy.ICEBERG_LARGE:
                price_offset = 0.001  # Slight improvement
                time_delay = 2.0
                split_ratio = 0.3  # Show only 30% of order

            elif strategy == OrderStrategy.DELAYED_ENTRY:
                # Wait for better entry
                base_offset = 0.003  # 0.3%
                price_offset = -base_offset if action.upper() in ['LONG', 'BUY'] else base_offset
                time_delay = 10.0
                split_ratio = 1.0

            elif strategy == OrderStrategy.SKIP_UNFAVORABLE:
                # No execution
                price_offset = 0.0
                time_delay = float('inf')
                split_ratio = 0.0

            return price_offset, time_delay, split_ratio

        except Exception as e:
            logger.error(f"Error calculating strategy params: {e}")
            return 0.0, 0.0, 1.0

    def _get_order_type(self, strategy: OrderStrategy) -> str:
        """Get order type for strategy."""
        order_type_map = {
            OrderStrategy.MARKET_IMMEDIATE: 'market',
            OrderStrategy.LIMIT_AGGRESSIVE: 'limit',
            OrderStrategy.LIMIT_PASSIVE: 'limit',
            OrderStrategy.ICEBERG_LARGE: 'iceberg',
            OrderStrategy.DELAYED_ENTRY: 'limit',
            OrderStrategy.SKIP_UNFAVORABLE: 'skip'
        }
        return order_type_map.get(strategy, 'limit')

    def _generate_reasoning(self, strategy: OrderStrategy, volatility: str,
                          volume: str, momentum: str, liquidity: float,
                          confidence: float, order_size: float) -> str:
        """Generate human-readable reasoning for strategy choice."""
        try:
            base_reason = f"Selected {strategy.value} strategy based on: "
            conditions = []

            if volatility == 'high':
                conditions.append("high volatility market")
            elif volatility == 'low':
                conditions.append("low volatility environment")

            if volume == 'high':
                conditions.append("high volume activity")
            elif volume == 'low':
                conditions.append("low volume conditions")

            if momentum == 'strong':
                conditions.append("strong price momentum")
            elif momentum == 'weak':
                conditions.append("weak momentum")

            if liquidity < 0.3:
                conditions.append("poor liquidity")
            elif liquidity > 0.7:
                conditions.append("good liquidity")

            if confidence > 0.8:
                conditions.append("high confidence signal")
            elif confidence < 0.5:
                conditions.append("low confidence signal")

            if order_size > 1000:
                conditions.append("large order size")

            return base_reason + ", ".join(conditions)

        except Exception as e:
            logger.error(f"Error generating reasoning: {e}")
            return f"Selected {strategy.value} strategy"

    def _calculate_recommendation_confidence(self, strategy: OrderStrategy,
                                           market_data: Dict[str, Any]) -> float:
        """Calculate confidence in the recommendation."""
        try:
            base_confidence = 0.7

            # Adjust based on market conditions
            liquidity = self._calculate_liquidity_score(market_data)
            volatility = market_data.get('volatility', 0.01)

            # Higher liquidity = higher confidence
            liquidity_boost = liquidity * 0.2

            # Moderate volatility = higher confidence
            if 0.005 < volatility < 0.015:
                volatility_boost = 0.1
            else:
                volatility_boost = -0.1

            # Strategy-specific adjustments
            strategy_adjustments = {
                OrderStrategy.MARKET_IMMEDIATE: 0.1,
                OrderStrategy.LIMIT_AGGRESSIVE: 0.05,
                OrderStrategy.LIMIT_PASSIVE: 0.0,
                OrderStrategy.ICEBERG_LARGE: -0.05,
                OrderStrategy.DELAYED_ENTRY: -0.1,
                OrderStrategy.SKIP_UNFAVORABLE: 0.2  # High confidence in skipping
            }

            strategy_adjustment = strategy_adjustments.get(strategy, 0.0)

            final_confidence = base_confidence + liquidity_boost + volatility_boost + strategy_adjustment
            return max(0.0, min(1.0, final_confidence))

        except Exception as e:
            logger.error(f"Error calculating recommendation confidence: {e}")
            return 0.5

    def _get_fallback_recommendation(self) -> OrderRecommendation:
        """Get fallback recommendation when analysis fails."""
        return OrderRecommendation(
            strategy=OrderStrategy.LIMIT_AGGRESSIVE,
            order_type='limit',
            price_offset=0.001,
            time_delay=2.0,
            split_ratio=1.0,
            confidence=0.3,
            reasoning="Fallback recommendation due to analysis error"
        )
