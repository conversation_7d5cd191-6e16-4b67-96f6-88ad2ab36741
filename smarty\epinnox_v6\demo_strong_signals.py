#!/usr/bin/env python3
"""
Strong Signal Generation Demo for Epinnox V6
Demonstrates AI signal generation with extreme market conditions
"""

import asyncio
import logging
import sys
import time
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from feeds.trade_parser import MarketFeatures
from storage.live_store import LiveDataStore
from models.smart_strategy import SmartStrategy

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class StrongSignalDemo:
    """Demonstrate AI signal generation with extreme market conditions."""
    
    def __init__(self):
        self.config = {
            'symbols': {
                'enabled': ['BTC-USDT', 'ETH-USDT'],
                'default': 'BTC-USDT'
            },
            'data_storage': {
                'trade_window_size': 500,
                'feature_update_interval': 2,
                'memory_cleanup_interval': 300,
                'persist_state': False
            },
            'models': {
                'weights': {
                    'rsi': 1.2,
                    'vwap': 1.0,
                    'orderflow': 1.5,
                    'volatility': 1.1
                },
                'rsi': {
                    'period': 14,
                    'overbought': 70,
                    'oversold': 30
                },
                'vwap': {
                    'deviation_threshold': 0.02
                },
                'orderflow': {
                    'imbalance_threshold': 0.3
                },
                'volatility': {
                    'threshold_high': 0.05,
                    'threshold_low': 0.01
                }
            },
            'signals': {
                'confidence_threshold': 0.4,  # Lower threshold for demo
                'signal_cooldown': 5,  # Shorter cooldown
                'thresholds': {
                    'strong_buy': 0.6,
                    'buy': 0.3,
                    'sell': -0.3,
                    'strong_sell': -0.6
                }
            }
        }
        
        # Initialize components
        self.data_store = LiveDataStore(self.config)
        self.strategy_engine = SmartStrategy(self.config, self.data_store)
        
        logger.info("🎯 Strong Signal Demo initialized")
    
    def create_extreme_scenario(self, scenario_name: str, symbol: str = 'BTC-USDT') -> MarketFeatures:
        """Create extreme market scenarios that should generate strong signals."""
        base_price = 50000.0
        current_time = int(time.time() * 1000)
        
        scenarios = {
            'extreme_oversold': MarketFeatures(
                symbol=symbol,
                timestamp=current_time,
                last_price=base_price,
                price_change_1m=-0.08,  # 8% crash
                price_velocity=-0.004,
                volume_1m=500.0,        # Very high volume
                buy_volume_ratio=0.85,  # Strong buying the dip
                volume_delta=200.0,
                order_flow_imbalance=0.7,  # Extreme buy pressure
                trade_intensity=25.0,
                avg_trade_size=2.0,
                rsi=15.0,  # Extremely oversold
                vwap=base_price * 1.05,  # Price way below VWAP
                volatility=0.02
            ),
            
            'extreme_overbought': MarketFeatures(
                symbol=symbol,
                timestamp=current_time,
                last_price=base_price,
                price_change_1m=0.12,   # 12% pump
                price_velocity=0.006,
                volume_1m=600.0,        # Very high volume
                buy_volume_ratio=0.15,  # Heavy selling
                volume_delta=-300.0,
                order_flow_imbalance=-0.7,  # Extreme sell pressure
                trade_intensity=30.0,
                avg_trade_size=2.5,
                rsi=85.0,  # Extremely overbought
                vwap=base_price * 0.92,  # Price way above VWAP
                volatility=0.02
            ),
            
            'perfect_breakout': MarketFeatures(
                symbol=symbol,
                timestamp=current_time,
                last_price=base_price,
                price_change_1m=0.05,   # 5% breakout
                price_velocity=0.0025,
                volume_1m=800.0,        # Massive volume
                buy_volume_ratio=0.9,   # Almost all buying
                volume_delta=640.0,
                order_flow_imbalance=0.8,  # Extreme buy pressure
                trade_intensity=40.0,
                avg_trade_size=3.0,
                rsi=60.0,  # Bullish momentum
                vwap=base_price * 0.98,  # Breaking above VWAP
                volatility=0.025
            ),
            
            'perfect_breakdown': MarketFeatures(
                symbol=symbol,
                timestamp=current_time,
                last_price=base_price,
                price_change_1m=-0.07,  # 7% breakdown
                price_velocity=-0.0035,
                volume_1m=700.0,        # High volume
                buy_volume_ratio=0.1,   # Almost all selling
                volume_delta=-560.0,
                order_flow_imbalance=-0.8,  # Extreme sell pressure
                trade_intensity=35.0,
                avg_trade_size=2.8,
                rsi=40.0,  # Bearish momentum
                vwap=base_price * 1.03,  # Breaking below VWAP
                volatility=0.025
            ),
            
            'momentum_continuation': MarketFeatures(
                symbol=symbol,
                timestamp=current_time,
                last_price=base_price,
                price_change_1m=0.03,   # 3% steady rise
                price_velocity=0.0015,
                volume_1m=400.0,
                buy_volume_ratio=0.75,  # Strong buying
                volume_delta=200.0,
                order_flow_imbalance=0.5,  # Strong buy pressure
                trade_intensity=20.0,
                avg_trade_size=1.5,
                rsi=45.0,  # Healthy momentum
                vwap=base_price * 0.99,  # Price above VWAP
                volatility=0.02
            )
        }
        
        return scenarios.get(scenario_name, scenarios['momentum_continuation'])
    
    async def run_extreme_scenario(self, scenario_name: str):
        """Run an extreme market scenario."""
        logger.info(f"\n🔥 EXTREME SCENARIO: {scenario_name.upper()}")
        logger.info("=" * 60)
        
        # Create extreme market features
        features = self.create_extreme_scenario(scenario_name)
        
        # Display scenario details
        logger.info(f"📊 EXTREME Market Conditions:")
        logger.info(f"   💰 Price: ${features.last_price:,.2f}")
        logger.info(f"   📈 1m Change: {features.price_change_1m:.2%}")
        logger.info(f"   📊 Volume: {features.volume_1m:.1f}")
        logger.info(f"   🔄 Buy Ratio: {features.buy_volume_ratio:.1%}")
        logger.info(f"   ⚖️ Order Flow: {features.order_flow_imbalance:.2f}")
        logger.info(f"   📉 RSI: {features.rsi:.1f}")
        logger.info(f"   📊 VWAP: ${features.vwap:.2f}")
        logger.info(f"   🌊 Volatility: {features.volatility:.3f}")
        
        # Store features
        self.data_store.store_features(features)
        
        # Process through AI strategy
        signal = await self.strategy_engine.process_features(features)
        
        if signal:
            logger.info(f"\n🚨 STRONG AI SIGNAL DETECTED!")
            logger.info(f"   🎯 Action: {signal.action}")
            logger.info(f"   💪 Confidence: {signal.confidence:.1%}")
            logger.info(f"   📊 Score: {signal.score:.3f}")
            logger.info(f"   💭 Reasoning: {signal.reasoning}")
            logger.info(f"   🤖 Model Contributions:")
            for model, contribution in signal.model_contributions.items():
                strength = "🔥" if abs(contribution) > 0.8 else "💪" if abs(contribution) > 0.5 else "👍"
                logger.info(f"     {strength} {model}: {contribution:.3f}")
        else:
            logger.info(f"\n⏸️ No signal generated")
        
        # Get model outputs for detailed analysis
        model_outputs = self.data_store.get_model_outputs(features.symbol)
        if model_outputs:
            logger.info(f"\n🤖 DETAILED Model Analysis:")
            for model_name, output in model_outputs.items():
                action = output.get('action', 'N/A')
                confidence = output.get('confidence', 0)
                signal_type = output.get('signal', 'N/A')
                
                # Add emoji based on strength
                emoji = "🔥" if confidence > 0.8 else "💪" if confidence > 0.6 else "👍" if confidence > 0.4 else "😐"
                
                logger.info(f"   {emoji} {model_name.upper()}: {action} "
                          f"({confidence:.1%}) - {signal_type}")
        
        logger.info("=" * 60)
        
        return signal
    
    async def run_all_extreme_scenarios(self):
        """Run all extreme scenarios to demonstrate strong signal generation."""
        logger.info("🚀 EXTREME AI SIGNAL GENERATION DEMO")
        logger.info("🔥 Testing extreme market conditions for strong signals")
        logger.info("💪 Lower confidence threshold for demonstration")
        
        scenarios = [
            'extreme_oversold',
            'extreme_overbought', 
            'perfect_breakout',
            'perfect_breakdown',
            'momentum_continuation'
        ]
        
        results = {}
        
        for scenario in scenarios:
            signal = await self.run_extreme_scenario(scenario)
            results[scenario] = signal
            
            # Delay between scenarios
            await asyncio.sleep(3)
        
        # Summary
        logger.info("\n🎯 EXTREME DEMO SUMMARY")
        logger.info("=" * 70)
        
        signals_generated = sum(1 for signal in results.values() if signal is not None)
        
        logger.info(f"🔥 Extreme scenarios tested: {len(scenarios)}")
        logger.info(f"🚨 Strong signals generated: {signals_generated}")
        logger.info(f"💪 Signal success rate: {signals_generated/len(scenarios)*100:.1f}%")
        
        logger.info(f"\n📋 SIGNAL BREAKDOWN:")
        for scenario, signal in results.items():
            if signal:
                action_emoji = "🚀" if signal.action == "LONG" else "📉" if signal.action == "SHORT" else "⏸️"
                logger.info(f"   {action_emoji} {scenario}: {signal.action} ({signal.confidence:.1%})")
            else:
                logger.info(f"   ❌ {scenario}: No signal")
        
        # Performance metrics
        performance = self.data_store.get_performance_metrics()
        logger.info(f"\n📊 PERFORMANCE METRICS:")
        logger.info(f"   📈 Total signals: {performance.get('total_signals', 0)}")
        logger.info(f"   🎯 Win rate: {performance.get('win_rate', 0):.1%}")
        
        # Get recent signals for timeline
        recent_signals = self.data_store.get_signals(limit=10)
        if recent_signals:
            logger.info(f"\n📈 RECENT SIGNAL TIMELINE:")
            for i, sig in enumerate(recent_signals[-5:], 1):
                action_emoji = "🚀" if sig.get('action') == "LONG" else "📉" if sig.get('action') == "SHORT" else "⏸️"
                logger.info(f"   {i}. {action_emoji} {sig.get('action', 'N/A')} @ {sig.get('formatted_time', 'N/A')} "
                          f"({sig.get('confidence', 0):.1%})")
        
        logger.info("\n🎉 EXTREME DEMO COMPLETED SUCCESSFULLY!")
        logger.info("🌐 Check dashboard at http://localhost:8086 for real-time view")
        logger.info("🔥 AI models are working and generating signals!")

async def main():
    """Main demo entry point."""
    demo = StrongSignalDemo()
    await demo.run_all_extreme_scenarios()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Demo interrupted by user")
    except Exception as e:
        print(f"❌ Demo error: {e}")
        sys.exit(1)
