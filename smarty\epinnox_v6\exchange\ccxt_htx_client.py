#!/usr/bin/env python3
"""
CCXT HTX Client for USDT-M Futures Trading
Real implementation using CCXT library for HTX futures
"""

import os
import logging
import asyncio
from typing import Dict, Any, Optional, List
import ccxt.async_support as ccxt

logger = logging.getLogger(__name__)

class CCXTHTXClient:
    """
    CCXT-based HTX USDT-M Futures client for live trading.
    Uses ccxt.huobi with defaultType='swap' for futures trading.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config

        # API Configuration
        self.api_key = os.getenv('HTX_API_KEY', '')
        self.api_secret = os.getenv('HTX_API_SECRET', '')
        self.passphrase = os.getenv('HTX_PASSPHRASE', '')

        # Trading Configuration
        trading_symbol_env = os.getenv('TRADING_SYMBOL', 'DOGE-USDT')
        # Convert to CCXT format: DOGE-USDT -> DOGE/USDT:USDT for futures
        if '-' in trading_symbol_env:
            base, quote = trading_symbol_env.split('-')
            self.trading_symbol = f"{base}/{quote}:{quote}"  # DOGE/USDT:USDT
        else:
            self.trading_symbol = trading_symbol_env
        self.leverage = float(os.getenv('LEVERAGE', '20'))

        # Initialize CCXT client
        self.client = None
        self.is_connected = False

        # Check credentials
        if not self.api_key or not self.api_secret:
            logger.warning("⚠️ HTX API credentials not configured - live trading disabled")
            self.live_trading_enabled = False
        else:
            self.live_trading_enabled = True
            logger.info(f"🔑 CCXT HTX client initialized for {self.trading_symbol}")

    def create_client(self):
        """Create CCXT HTX client for USDT-M futures."""
        try:
            if not self.live_trading_enabled:
                return None

            self.client = ccxt.huobi({
                'apiKey': self.api_key,
                'secret': self.api_secret,
                'enableRateLimit': True,
                'sandbox': False,  # Use live trading
                'options': {
                    'defaultType': 'swap',  # CRITICAL: 'swap' = USDT-M futures
                },
                'headers': {
                    'User-Agent': 'Epinnox-V6-Trading-Bot/1.0'
                }
            })

            logger.info("✅ CCXT HTX futures client created")
            return self.client

        except Exception as e:
            logger.error(f"Error creating CCXT HTX client: {e}")
            return None

    async def connect(self):
        """Connect and test the CCXT client."""
        try:
            if not self.client:
                self.client = self.create_client()

            if not self.client:
                return False

            # Test connection by fetching markets
            markets = await self.client.load_markets()

            # Check if our trading symbol exists
            if self.trading_symbol not in markets:
                logger.error(f"Trading symbol {self.trading_symbol} not found in HTX markets")
                return False

            self.is_connected = True
            logger.info(f"✅ Connected to HTX futures - {len(markets)} markets loaded")
            return True

        except Exception as e:
            logger.error(f"Error connecting to HTX: {e}")
            self.is_connected = False
            return False

    async def disconnect(self):
        """Disconnect from HTX."""
        try:
            if self.client:
                await self.client.close()
                self.client = None
            self.is_connected = False
            logger.info("🔌 Disconnected from HTX")

        except Exception as e:
            logger.error(f"Error disconnecting from HTX: {e}")

    async def get_account_balance(self) -> Optional[Dict[str, Any]]:
        """Get account balance for USDT-M futures."""
        try:
            if not self.is_connected or not self.client:
                logger.warning("HTX client not connected")
                return None

            # Fetch balance for futures (swap) trading
            balance = await self.client.fetch_balance({'type': 'swap'})

            logger.debug(f"📊 Account balance retrieved: {balance.get('USDT', {})}")
            return balance

        except Exception as e:
            logger.error(f"Error getting account balance: {e}")
            return None

    async def get_positions(self, symbol: str = None) -> Optional[List[Dict[str, Any]]]:
        """Get open positions."""
        try:
            if not self.is_connected or not self.client:
                logger.warning("HTX client not connected")
                return None

            # Get positions for specific symbol or all symbols
            symbols = [symbol] if symbol else None
            positions = await self.client.fetch_positions(symbols)

            # Filter out zero positions
            open_positions = [pos for pos in positions if float(pos.get('size', 0)) > 0]

            logger.debug(f"📊 Positions retrieved: {len(open_positions)} open positions")
            return open_positions

        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return None

    async def place_market_order(self, symbol: str, side: str, amount: float) -> Optional[Dict[str, Any]]:
        """Place a market order."""
        try:
            if not self.is_connected or not self.client:
                logger.warning("HTX client not connected - cannot place order")
                return None

            # Convert side to CCXT format
            ccxt_side = 'buy' if side.upper() in ['LONG', 'BUY'] else 'sell'

            logger.info(f"🚀 Placing market order: {ccxt_side} {amount} {symbol}")

            # Place market order
            order = await self.client.create_market_order(symbol, ccxt_side, amount)

            logger.info(f"✅ Order placed: {order.get('id')} - {order.get('status')}")
            return order

        except Exception as e:
            logger.error(f"Error placing market order: {e}")
            return None

    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an order."""
        try:
            if not self.is_connected or not self.client:
                logger.warning("HTX client not connected")
                return False

            result = await self.client.cancel_order(order_id, symbol)
            logger.info(f"✅ Order cancelled: {order_id}")
            return True

        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            return False

    async def get_ticker(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get current ticker for symbol."""
        try:
            if not self.is_connected or not self.client:
                return None

            ticker = await self.client.fetch_ticker(symbol)
            return ticker

        except Exception as e:
            logger.error(f"Error getting ticker: {e}")
            return None

    async def set_leverage(self, symbol: str, leverage: float) -> bool:
        """Set leverage for symbol."""
        try:
            if not self.is_connected or not self.client:
                logger.warning("HTX client not connected")
                return False

            # Set leverage for the symbol
            result = await self.client.set_leverage(leverage, symbol)
            logger.info(f"✅ Leverage set to {leverage}x for {symbol}")
            return True

        except Exception as e:
            logger.error(f"Error setting leverage: {e}")
            return False

    def get_trading_status(self) -> Dict[str, Any]:
        """Get current trading status."""
        return {
            'live_trading_enabled': self.live_trading_enabled,
            'is_connected': self.is_connected,
            'api_configured': bool(self.api_key and self.api_secret),
            'trading_symbol': self.trading_symbol,
            'leverage': self.leverage,
            'client_type': 'ccxt.huobi',
            'market_type': 'swap'  # USDT-M futures
        }

def create_htx_client(config: Dict[str, Any] = None) -> CCXTHTXClient:
    """Factory function to create HTX CCXT client."""
    config = config or {}
    return CCXTHTXClient(config)

async def test_htx_connection() -> bool:
    """Test HTX connection and basic functionality."""
    try:
        logger.info("🧪 Testing HTX CCXT connection...")

        # Create client
        client = create_htx_client()

        # Connect
        connected = await client.connect()
        if not connected:
            logger.error("❌ Failed to connect to HTX")
            return False

        # Test account balance
        balance = await client.get_account_balance()
        if balance:
            usdt_balance = balance.get('USDT', {})
            logger.info(f"✅ Account balance: {usdt_balance.get('total', 0)} USDT")

        # Test positions
        positions = await client.get_positions()
        if positions is not None:
            logger.info(f"✅ Positions: {len(positions)} open positions")

        # Test ticker
        ticker = await client.get_ticker('DOGE/USDT:USDT')
        if ticker:
            logger.info(f"✅ Ticker: DOGE/USDT price = ${ticker.get('last', 0):.4f}")

        # Disconnect
        await client.disconnect()

        logger.info("✅ HTX CCXT connection test completed successfully")
        return True

    except Exception as e:
        logger.error(f"❌ HTX connection test failed: {e}")
        return False

if __name__ == "__main__":
    # Test the client
    import asyncio
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_htx_connection())
