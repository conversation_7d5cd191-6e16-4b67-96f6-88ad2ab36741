# Trading Prompt Templates for LLM Integration
# Dynamic, context-aware prompts for intelligent trading decisions

# Base system prompt that establishes the LLM's role and constraints
system_prompt: |
  You are an expert cryptocurrency trading AI assistant integrated into the Epinnox V6 trading system.
  
  Your role is to analyze multiple AI model outputs and make final trading decisions for {symbol}.
  
  CRITICAL CONSTRAINTS:
  - Confidence scores must be between 0-100 (never exceed 100%)
  - Provide specific, actionable reasoning based on model outputs
  - Consider recent performance and market context
  - Acknowledge when models disagree and explain your resolution
  - Focus on risk management and capital preservation
  
  RESPONSE FORMAT (JSON only):
  {{
    "final_decision": "LONG|SHORT|WAIT",
    "confidence": <0-100>,
    "reasoning": "<2-3 sentences with specific model references>",
    "risk_assessment": "LOW|MEDIUM|HIGH",
    "model_consensus": "STRONG|WEAK|CONFLICTED"
  }}

# Main trading decision prompt template
trading_decision_prompt: |
  MARKET ANALYSIS REQUEST for {symbol}
  Current Time: {timestamp}
  Current Price: ${current_price:.2f}
  
  === AI MODEL OUTPUTS ===
  {model_outputs}
  
  === RECENT SIGNAL PERFORMANCE ===
  {performance_context}
  
  === MARKET CONTEXT ===
  Price Trend: {price_trend}
  Volatility: {volatility_level}
  Volume Profile: {volume_context}
  
  {disagreement_analysis}
  
  {performance_feedback}
  
  Based on this comprehensive analysis, provide your trading decision with clear reasoning that references specific model outputs and considers the performance context.

# Template for formatting individual model outputs
model_output_template: |
  {model_name}: {action} (confidence: {confidence:.1f}%)
    Signal: {signal}
    Value: {value}
    Reasoning: {model_reasoning}

# Template for performance context injection
performance_context_template: |
  Recent Performance (Last {signal_count} signals):
  - Win Rate: {win_rate:.1f}%
  - Total P&L: ${total_pnl:.2f}
  - Recent Trend: {recent_trend}
  - Best Performing Model: {best_model}
  - Worst Performing Model: {worst_model}
  
  Recent Signal History:
  {signal_history}

# Template for signal history formatting
signal_history_template: |
  {timestamp} | {direction} @ ${price:.2f} | {status} | P&L: ${pnl:.2f} | Confidence: {confidence:.0f}%

# Template for model disagreement analysis
disagreement_template: |
  === MODEL DISAGREEMENT DETECTED ===
  Conflicting Signals:
  {conflicts}
  
  Resolution Guidance:
  - Consider which models have been most accurate recently
  - Evaluate the strength of each signal (confidence levels)
  - Factor in current market conditions and volatility
  - When in doubt, prefer WAIT to preserve capital

# Template for performance feedback
performance_feedback_template: |
  === PERFORMANCE FEEDBACK ===
  {feedback_type}: {feedback_message}
  
  Recent Pattern Analysis:
  - {pattern_analysis}
  
  Adaptive Guidance:
  - {adaptive_guidance}

# Market regime templates
market_regimes:
  trending_up:
    context: "Market is in an upward trending regime"
    bias: "Favor LONG signals, be cautious with SHORT"
    risk_adjustment: "Increase confidence in bullish signals"
    
  trending_down:
    context: "Market is in a downward trending regime"
    bias: "Favor SHORT signals, be cautious with LONG"
    risk_adjustment: "Increase confidence in bearish signals"
    
  ranging:
    context: "Market is in a ranging/sideways regime"
    bias: "Look for reversal signals at support/resistance"
    risk_adjustment: "Reduce position sizes, favor mean reversion"
    
  high_volatility:
    context: "Market is experiencing high volatility"
    bias: "Reduce position sizes, require higher confidence"
    risk_adjustment: "Increase risk assessment by one level"
    
  low_volatility:
    context: "Market is in low volatility conditions"
    bias: "Can take larger positions with good signals"
    risk_adjustment: "Normal risk assessment applies"

# Confidence adjustment rules
confidence_adjustments:
  recent_wins:
    threshold: 3  # consecutive wins
    adjustment: 5  # increase confidence by 5%
    message: "Recent winning streak detected - slightly increased confidence"
    
  recent_losses:
    threshold: 3  # consecutive losses
    adjustment: -10  # decrease confidence by 10%
    message: "Recent losses detected - reduced confidence for risk management"
    
  model_disagreement:
    high_conflict: -15  # reduce confidence when models strongly disagree
    medium_conflict: -8
    low_conflict: -3
    
  performance_based:
    excellent: 5    # >80% win rate
    good: 2         # 60-80% win rate
    poor: -10       # <40% win rate
    terrible: -20   # <20% win rate

# Risk assessment criteria
risk_levels:
  low:
    conditions:
      - "Model consensus is strong (>80% agreement)"
      - "Recent performance is positive"
      - "Market volatility is normal"
      - "Signal confidence is high (>75%)"
    
  medium:
    conditions:
      - "Some model disagreement present"
      - "Mixed recent performance"
      - "Moderate market volatility"
      - "Medium signal confidence (50-75%)"
    
  high:
    conditions:
      - "Strong model disagreement"
      - "Recent poor performance"
      - "High market volatility"
      - "Low signal confidence (<50%)"
      - "Unusual market conditions"

# Symbol-specific contexts
symbol_contexts:
  BTC-USDT:
    characteristics: "High liquidity, trend-following, institutional interest"
    typical_volatility: "2-5% daily moves"
    key_levels: "Watch major psychological levels (100k, 50k, etc.)"
    
  ETH-USDT:
    characteristics: "DeFi correlation, gas fee sensitivity, tech developments"
    typical_volatility: "3-8% daily moves"
    key_levels: "Strong support/resistance at round numbers"
    
  DOGE-USDT:
    characteristics: "Meme coin, social sentiment driven, high volatility"
    typical_volatility: "5-15% daily moves"
    key_levels: "Psychological levels, social media influence"

# Fallback templates for error conditions
fallback_templates:
  no_model_data:
    decision: "WAIT"
    confidence: 0
    reasoning: "Insufficient model data available for analysis"
    risk_assessment: "HIGH"
    
  llm_error:
    decision: "WAIT"
    confidence: 0
    reasoning: "LLM analysis failed - defaulting to safe position"
    risk_assessment: "HIGH"
    
  invalid_response:
    decision: "WAIT"
    confidence: 0
    reasoning: "Invalid LLM response format - applying safety measures"
    risk_assessment: "HIGH"
